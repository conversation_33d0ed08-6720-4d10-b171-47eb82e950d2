const path = require('path');
const express = require('express');

/**
 * Configure le serveur Express pour servir les fichiers statiques du frontend
 * @param {express.Application} app - L'instance de l'application Express
 */
function configureStaticFiles(app) {
  // Chemin vers le répertoire de build du frontend
  const frontendDistPath = path.join(__dirname, '../../frontend/dist');
  
  console.log(`Serving static files from: ${frontendDistPath}`);
  
  // Servir les fichiers statiques du build Vite
  app.use(express.static(frontendDistPath));
  
  // Pour toutes les routes non-API, servir index.html
  app.get('*', function(req, res, next) {
    // Si la requête commence par /api ou /api-docs, passer au middleware suivant
    if (req.path.startsWith('/api') || req.path.startsWith('/api-docs')) {
      return next();
    }
    
    // Servir index.html pour le routage côté client React
    res.sendFile(path.join(frontendDistPath, 'index.html'));
  });
}

module.exports = configureStaticFiles;