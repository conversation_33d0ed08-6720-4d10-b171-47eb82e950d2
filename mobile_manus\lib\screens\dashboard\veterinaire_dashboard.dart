import 'package:flutter/material.dart';
import 'package:mobile_manus/models/user.dart';
import 'package:mobile_manus/providers/auth_provider.dart';
import 'package:provider/provider.dart';

class VeterinaireDashboard extends StatefulWidget {
  final User user;

  const VeterinaireDashboard({super.key, required this.user});

  @override
  State<VeterinaireDashboard> createState() => _VeterinaireDashboardState();
}

class _VeterinaireDashboardState extends State<VeterinaireDashboard> {
  int _selectedIndex = 0;

  @override
  Widget build(BuildContext context) {
    return Scaffold(
      appBar: AppBar(
        title: Text('Dr. ${widget.user.firstName ?? widget.user.username}'),
        backgroundColor: Colors.blue[700],
        foregroundColor: Colors.white,
        actions: [
          IconButton(
            icon: const Icon(Icons.notifications),
            onPressed: () {
              // TODO: Show notifications
            },
          ),
          IconButton(
            icon: const Icon(Icons.logout),
            onPressed: () {
              Provider.of<AuthProvider>(context, listen: false).logout();
            },
          ),
        ],
      ),
      body: IndexedStack(
        index: _selectedIndex,
        children: [
          _buildOverviewTab(),
          _buildConsultationsTab(),
          _buildPrescriptionsTab(),
          _buildPatientsTab(),
        ],
      ),
      bottomNavigationBar: BottomNavigationBar(
        type: BottomNavigationBarType.fixed,
        currentIndex: _selectedIndex,
        onTap: (index) {
          setState(() {
            _selectedIndex = index;
          });
        },
        selectedItemColor: Colors.blue[700],
        unselectedItemColor: Colors.grey,
        items: const [
          BottomNavigationBarItem(
            icon: Icon(Icons.dashboard),
            label: 'Aperçu',
          ),
          BottomNavigationBarItem(
            icon: Icon(Icons.calendar_today),
            label: 'Consultations',
          ),
          BottomNavigationBarItem(
            icon: Icon(Icons.medication),
            label: 'Prescriptions',
          ),
          BottomNavigationBarItem(
            icon: Icon(Icons.pets),
            label: 'Patients',
          ),
        ],
      ),
      floatingActionButton: FloatingActionButton(
        onPressed: () {
          _showQuickActionDialog();
        },
        backgroundColor: Colors.blue[700],
        child: const Icon(Icons.add, color: Colors.white),
      ),
    );
  }

  Widget _buildOverviewTab() {
    return SingleChildScrollView(
      padding: const EdgeInsets.all(16),
      child: Column(
        crossAxisAlignment: CrossAxisAlignment.start,
        children: [
          // Welcome Card
          Card(
            child: Padding(
              padding: const EdgeInsets.all(16),
              child: Row(
                children: [
                  CircleAvatar(
                    radius: 30,
                    backgroundColor: Colors.blue[100],
                    child: Icon(
                      Icons.medical_services,
                      size: 30,
                      color: Colors.blue[700],
                    ),
                  ),
                  const SizedBox(width: 16),
                  Expanded(
                    child: Column(
                      crossAxisAlignment: CrossAxisAlignment.start,
                      children: [
                        Text(
                          'Dr. ${widget.user.firstName ?? widget.user.username}',
                          style: Theme.of(context).textTheme.titleLarge,
                        ),
                        Text(
                          'Vétérinaire spécialisé en aviculture',
                          style: Theme.of(context).textTheme.bodyMedium?.copyWith(
                            color: Colors.grey[600],
                          ),
                        ),
                      ],
                    ),
                  ),
                ],
              ),
            ),
          ),
          
          const SizedBox(height: 16),
          
          // Today's Schedule
          Text(
            'Programme d\'aujourd\'hui',
            style: Theme.of(context).textTheme.titleLarge,
          ),
          const SizedBox(height: 8),
          
          Card(
            child: Column(
              children: [
                _buildScheduleItem(
                  '09:00',
                  'Consultation - Ferme Benali',
                  'Contrôle sanitaire routine',
                  Colors.green,
                ),
                const Divider(height: 1),
                _buildScheduleItem(
                  '14:00',
                  'Urgence - Ferme Khelifi',
                  'Mortalité anormale',
                  Colors.red,
                ),
                const Divider(height: 1),
                _buildScheduleItem(
                  '16:30',
                  'Suivi - Ferme Meziane',
                  'Contrôle post-traitement',
                  Colors.orange,
                ),
              ],
            ),
          ),
          
          const SizedBox(height: 16),
          
          // Quick Stats
          Text(
            'Statistiques de la semaine',
            style: Theme.of(context).textTheme.titleLarge,
          ),
          const SizedBox(height: 8),
          
          Row(
            children: [
              Expanded(
                child: _buildStatCard(
                  'Consultations',
                  '23',
                  Icons.calendar_today,
                  Colors.blue,
                ),
              ),
              const SizedBox(width: 8),
              Expanded(
                child: _buildStatCard(
                  'Prescriptions',
                  '18',
                  Icons.medication,
                  Colors.green,
                ),
              ),
            ],
          ),
          
          const SizedBox(height: 8),
          
          Row(
            children: [
              Expanded(
                child: _buildStatCard(
                  'Urgences',
                  '5',
                  Icons.emergency,
                  Colors.red,
                ),
              ),
              const SizedBox(width: 8),
              Expanded(
                child: _buildStatCard(
                  'Fermes suivies',
                  '12',
                  Icons.agriculture,
                  Colors.orange,
                ),
              ),
            ],
          ),
          
          const SizedBox(height: 16),
          
          // Health Alerts
          Text(
            'Alertes sanitaires',
            style: Theme.of(context).textTheme.titleLarge,
          ),
          const SizedBox(height: 8),
          
          Card(
            child: Column(
              children: [
                _buildAlertItem(
                  'Mortalité élevée',
                  'Ferme Boudjemaa - 3% cette semaine',
                  Icons.warning,
                  Colors.red,
                ),
                const Divider(height: 1),
                _buildAlertItem(
                  'Baisse de ponte',
                  'Ferme Cherif - -15% en 3 jours',
                  Icons.trending_down,
                  Colors.orange,
                ),
                const Divider(height: 1),
                _buildAlertItem(
                  'Fin de traitement',
                  'Ferme Amrani - Contrôle requis',
                  Icons.schedule,
                  Colors.blue,
                ),
              ],
            ),
          ),
          
          const SizedBox(height: 16),
          
          // Quick Actions
          Text(
            'Actions rapides',
            style: Theme.of(context).textTheme.titleLarge,
          ),
          const SizedBox(height: 8),
          
          GridView.count(
            shrinkWrap: true,
            physics: const NeverScrollableScrollPhysics(),
            crossAxisCount: 2,
            crossAxisSpacing: 8,
            mainAxisSpacing: 8,
            childAspectRatio: 2.5,
            children: [
              _buildQuickActionCard(
                'Nouvelle consultation',
                Icons.add_circle,
                Colors.blue,
                () {
                  // TODO: Navigate to new consultation
                },
              ),
              _buildQuickActionCard(
                'Prescription rapide',
                Icons.medication,
                Colors.green,
                () {
                  // TODO: Navigate to new prescription
                },
              ),
              _buildQuickActionCard(
                'Rapport d\'urgence',
                Icons.emergency,
                Colors.red,
                () {
                  // TODO: Navigate to emergency report
                },
              ),
              _buildQuickActionCard(
                'Historique patient',
                Icons.history,
                Colors.purple,
                () {
                  setState(() {
                    _selectedIndex = 3;
                  });
                },
              ),
            ],
          ),
        ],
      ),
    );
  }

  Widget _buildScheduleItem(String time, String title, String subtitle, Color color) {
    return ListTile(
      leading: Container(
        width: 50,
        height: 50,
        decoration: BoxDecoration(
          color: color.withOpacity(0.1),
          borderRadius: BorderRadius.circular(8),
        ),
        child: Center(
          child: Text(
            time,
            style: TextStyle(
              color: color,
              fontWeight: FontWeight.bold,
              fontSize: 12,
            ),
          ),
        ),
      ),
      title: Text(title),
      subtitle: Text(subtitle),
      trailing: Icon(Icons.chevron_right, color: color),
      onTap: () {
        // TODO: Handle schedule item tap
      },
    );
  }

  Widget _buildStatCard(String title, String value, IconData icon, Color color) {
    return Card(
      child: Padding(
        padding: const EdgeInsets.all(16),
        child: Column(
          children: [
            Icon(icon, color: color, size: 32),
            const SizedBox(height: 8),
            Text(
              value,
              style: Theme.of(context).textTheme.titleLarge?.copyWith(
                fontWeight: FontWeight.bold,
                color: color,
              ),
            ),
            Text(
              title,
              style: Theme.of(context).textTheme.bodySmall,
              textAlign: TextAlign.center,
            ),
          ],
        ),
      ),
    );
  }

  Widget _buildQuickActionCard(String title, IconData icon, Color color, VoidCallback onTap) {
    return Card(
      child: InkWell(
        onTap: onTap,
        borderRadius: BorderRadius.circular(8),
        child: Padding(
          padding: const EdgeInsets.all(12),
          child: Row(
            children: [
              Icon(icon, color: color, size: 24),
              const SizedBox(width: 8),
              Expanded(
                child: Text(
                  title,
                  style: Theme.of(context).textTheme.bodyMedium?.copyWith(
                    fontWeight: FontWeight.w500,
                  ),
                ),
              ),
            ],
          ),
        ),
      ),
    );
  }

  Widget _buildAlertItem(String title, String subtitle, IconData icon, Color color) {
    return ListTile(
      leading: Icon(icon, color: color),
      title: Text(title),
      subtitle: Text(subtitle),
      trailing: const Icon(Icons.chevron_right),
      onTap: () {
        // TODO: Handle alert tap
      },
    );
  }

  void _showQuickActionDialog() {
    showDialog(
      context: context,
      builder: (BuildContext context) {
        return AlertDialog(
          title: const Text('Action rapide'),
          content: Column(
            mainAxisSize: MainAxisSize.min,
            children: [
              ListTile(
                leading: const Icon(Icons.add_circle, color: Colors.blue),
                title: const Text('Nouvelle consultation'),
                onTap: () {
                  Navigator.pop(context);
                  // TODO: Navigate to new consultation
                },
              ),
              ListTile(
                leading: const Icon(Icons.medication, color: Colors.green),
                title: const Text('Prescription rapide'),
                onTap: () {
                  Navigator.pop(context);
                  // TODO: Navigate to new prescription
                },
              ),
              ListTile(
                leading: const Icon(Icons.emergency, color: Colors.red),
                title: const Text('Rapport d\'urgence'),
                onTap: () {
                  Navigator.pop(context);
                  // TODO: Navigate to emergency report
                },
              ),
            ],
          ),
        );
      },
    );
  }

  Widget _buildConsultationsTab() {
    return const Center(
      child: Text('Gestion des consultations - À implémenter'),
    );
  }

  Widget _buildPrescriptionsTab() {
    return const Center(
      child: Text('Gestion des prescriptions - À implémenter'),
    );
  }

  Widget _buildPatientsTab() {
    return const Center(
      child: Text('Gestion des patients - À implémenter'),
    );
  }
}

