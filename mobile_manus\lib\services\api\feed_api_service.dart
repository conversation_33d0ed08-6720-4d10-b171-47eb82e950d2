import 'package:dio/dio.dart';
import 'package:mobile_manus/models/feed_consumption_log.dart';
import 'package:mobile_manus/models/feed_plan.dart';

class FeedApiService {
  final Dio _dio = Dio();  final String baseUrl = 'http://192.168.1.102:3003/api';

  FeedApiService() {
    _dio.options.baseUrl = baseUrl;
    // Add interceptors for authentication, error handling, etc.
    _dio.interceptors.add(InterceptorsWrapper(
      onRequest: (options, handler) {
        // Add auth token
        // options.headers['Authorization'] = 'Bearer YOUR_TOKEN_HERE';
        return handler.next(options);
      },
      onError: (DioException e, handler) {
        // Handle errors globally
        print('FeedApiService Error: ${e.message}');
        return handler.next(e);
      },
    ));
  }

  Future<List<FeedConsumptionLog>> getFeedConsumptionLogs() async {
    try {
      final response = await _dio.get('/feed/consumption-logs');
      return (response.data as List)
          .map((json) => FeedConsumptionLog.fromJson(json))
          .toList();
    } catch (e) {
      throw Exception('Failed to load feed consumption logs: $e');
    }
  }

  Future<FeedConsumptionLog> createFeedConsumptionLog(FeedConsumptionLog log) async {
    try {
      final response = await _dio.post('/feed/consumption-logs', data: log.toJson());
      return FeedConsumptionLog.fromJson(response.data);
    } catch (e) {
      throw Exception('Failed to create feed consumption log: $e');
    }
  }

  Future<List<FeedPlan>> getFeedPlans() async {
    try {
      final response = await _dio.get('/feed/plans');
      return (response.data as List)
          .map((json) => FeedPlan.fromJson(json))
          .toList();
    } catch (e) {
      throw Exception('Failed to load feed plans: $e');
    }
  }

  Future<FeedPlan> createFeedPlan(FeedPlan plan) async {
    try {
      final response = await _dio.post('/feed/plans', data: plan.toJson());
      return FeedPlan.fromJson(response.data);
    } catch (e) {
      throw Exception('Failed to create feed plan: $e');
    }
  }
}
