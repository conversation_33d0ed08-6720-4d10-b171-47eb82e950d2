class ChatMessage {
  final int id;
  final int threadId;
  final int senderId;
  final String content;
  final DateTime sentAt;
  final bool isRead;

  ChatMessage({
    required this.id,
    required this.threadId,
    required this.senderId,
    required this.content,
    required this.sentAt,
    required this.isRead,
  });

  factory ChatMessage.fromJson(Map<String, dynamic> json) {
    return ChatMessage(
      id: json["id"],
      threadId: json["thread_id"],
      senderId: json["sender_id"],
      content: json["content"],
      sentAt: DateTime.parse(json["sent_at"]),
      isRead: json["is_read"],
    );
  }

  Map<String, dynamic> toJson() {
    return {
      "id": id,
      "thread_id": threadId,
      "sender_id": senderId,
      "content": content,
      "sent_at": sentAt.toIso8601String(),
      "is_read": isRead,
    };
  }
}


