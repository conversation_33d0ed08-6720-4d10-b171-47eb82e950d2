import{a as e}from"./index.WVN8qCy5.js";const s="/admin/settings",n=async()=>{try{return(await e.get(`${s}/smtp`)).data}catch(t){throw console.error("Error fetching SMTP settings:",t),t}},a=async()=>n(),c=async t=>{try{return(await e.post(`${s}/smtp`,t)).data}catch(r){throw console.error("Error updating SMTP settings:",r),r}},o=async()=>{try{return(await e.get(`${s}/security`)).data}catch(t){throw console.error("Error fetching security settings:",t),t}},i=async()=>o(),p=async t=>{try{return(await e.post(`${s}/security`,t)).data}catch(r){throw console.error("Error updating security settings:",r),r}},g=async()=>{try{return(await e.get(`${s}/general`)).data}catch(t){throw console.error("Error fetching general settings:",t),t}},y=async t=>{try{return(await e.post(`${s}/general`,t)).data}catch(r){throw console.error("Error updating general settings:",r),r}},u=async()=>{try{return(await e.get(`${s}/api-keys`)).data}catch(t){throw console.error("Error fetching API keys:",t),t}},h=async t=>{try{return(await e.post(`${s}/api-keys`,{apiKeys:t})).data}catch(r){throw console.error("Error updating API keys:",r),r}},S=async t=>{try{return(await e.post(`${s}/smtp/test`,t)).data}catch(r){throw console.error("Error testing SMTP configuration:",r),r}},w={fetchSmtpSettings:n,updateSmtpSettings:c,fetchSecuritySettings:o,getSecuritySettings:i,updateSecuritySettings:p,fetchGeneralSettings:g,updateGeneralSettings:y,fetchApiKeys:u,updateApiKeys:h,testSmtpConfig:S,getSmtpConfig:a};export{w as s};
//# sourceMappingURL=settingsService.DVLXOYtr.js.map
