# Set default encoding to UTF-8
$PSDefaultParameterValues['*:Encoding'] = 'UTF8'
[Console]::OutputEncoding = [System.Text.Encoding]::UTF8

function Invoke-ApiRequest {
    param (
        [string]$Method,
        [string]$Endpoint,
        [string]$Body = "",
        [string]$Token = ""
    )

    $headers = @{
        "Content-Type" = "application/json"
    }

    if ($Token) {
        $headers["Authorization"] = "Bearer $Token"
    }

    $params = @{
        Uri = "http://localhost:3003$Endpoint"
        Method = $Method
        ContentType = "application/json"
        Headers = $headers
    }

    if ($Body) {
        $params["Body"] = $Body
    }

    try {
        Start-Sleep -Milliseconds 500
        $response = Invoke-RestMethod @params
        Write-Host "Success!" -ForegroundColor Green
        return $response
    }
    catch {
        Write-Host "Error testing $Endpoint" -ForegroundColor Red
        Write-Host "Status Code: $($_.Exception.Response.StatusCode.value__)" -ForegroundColor Red
        Write-Host "Message: $($_.Exception.Message)`n" -ForegroundColor Red
        return $null
    }
}

Write-Host "`n=== Testing Backend API Endpoints ===`n" -ForegroundColor Cyan

# 1. Authentication
Write-Host "`n== Authentication ==" -ForegroundColor Yellow
$loginResponse = Invoke-ApiRequest -Method "POST" -Endpoint "/api/auth/login" `
    -Body '{"email":"<EMAIL>","password":"admin123"}'

if ($loginResponse) {
    $token = $loginResponse.token
    Write-Host "Logged in as: $($loginResponse.user.username) (Role: $($loginResponse.user.role))`n"
} else {
    Write-Host "Login failed. Cannot continue tests.`n" -ForegroundColor Red
    exit
}

# 2. Admin Routes
Write-Host "`n== Admin Routes ==" -ForegroundColor Yellow

# Users
Write-Host "Testing GET /api/admin/users" -ForegroundColor Yellow
$users = Invoke-ApiRequest -Method "GET" -Endpoint "/api/admin/users" -Token $token

# Roles
Write-Host "Testing GET /api/admin/roles" -ForegroundColor Yellow
$roles = Invoke-ApiRequest -Method "GET" -Endpoint "/api/admin/roles" -Token $token

# Settings
Write-Host "Testing GET /api/admin/settings/api-keys" -ForegroundColor Yellow
$apiConfig = Invoke-ApiRequest -Method "GET" -Endpoint "/api/admin/settings/api-keys" -Token $token

Write-Host "Testing GET /api/admin/settings/smtp" -ForegroundColor Yellow
$smtpConfig = Invoke-ApiRequest -Method "GET" -Endpoint "/api/admin/settings/smtp" -Token $token

Write-Host "Testing GET /api/admin/settings/security" -ForegroundColor Yellow
$securitySettings = Invoke-ApiRequest -Method "GET" -Endpoint "/api/admin/settings/security" -Token $token

# 3. Éleveur Routes
Write-Host "`n== Éleveur Routes ==" -ForegroundColor Yellow

Write-Host "Testing GET /api/eleveurs" -ForegroundColor Yellow
$eleveurs = Invoke-ApiRequest -Method "GET" -Endpoint "/api/eleveurs" -Token $token

if ($eleveurs -and $eleveurs.Count -gt 0) {
    $eleveurId = $eleveurs[0].id

    Write-Host "Testing GET /api/eleveurs/$eleveurId/dashboard" -ForegroundColor Yellow
    $dashboard = Invoke-ApiRequest -Method "GET" -Endpoint "/api/eleveurs/$eleveurId/dashboard" -Token $token

    Write-Host "Testing GET /api/eleveurs/$eleveurId/ouvriers" -ForegroundColor Yellow
    $ouvriers = Invoke-ApiRequest -Method "GET" -Endpoint "/api/eleveurs/$eleveurId/ouvriers" -Token $token

    Write-Host "Testing GET /api/eleveurs/$eleveurId/activites" -ForegroundColor Yellow
    $activites = Invoke-ApiRequest -Method "GET" -Endpoint "/api/eleveurs/$eleveurId/activites" -Token $token
}

# 4. Vétérinaire Routes
Write-Host "`n== Vétérinaire Routes ==" -ForegroundColor Yellow

Write-Host "Testing GET /api/veterinaires" -ForegroundColor Yellow
$veterinaires = Invoke-ApiRequest -Method "GET" -Endpoint "/api/veterinaires" -Token $token

if ($veterinaires -and $veterinaires.Count -gt 0) {
    $vetId = $veterinaires[0].id

    Write-Host "Testing GET /api/veterinaires/$vetId/dashboard" -ForegroundColor Yellow
    $vetDashboard = Invoke-ApiRequest -Method "GET" -Endpoint "/api/veterinaires/$vetId/dashboard" -Token $token

    Write-Host "Testing GET /api/veterinaires/$vetId/notifications" -ForegroundColor Yellow
    $vetNotifs = Invoke-ApiRequest -Method "GET" -Endpoint "/api/veterinaires/$vetId/notifications" -Token $token
}

# 5. Feed Management Routes
Write-Host "`n== Feed Management ==" -ForegroundColor Yellow

Write-Host "Testing GET /api/feed/items" -ForegroundColor Yellow
$feedItems = Invoke-ApiRequest -Method "GET" -Endpoint "/api/feed/items" -Token $token

# 6. Homepage Sections
Write-Host "`n== Homepage ==" -ForegroundColor Yellow

Write-Host "Testing GET /api/homepage/sections" -ForegroundColor Yellow
$sections = Invoke-ApiRequest -Method "GET" -Endpoint "/api/homepage/sections" -Token $token

Write-Host "`n=== Test Complete ===`n" -ForegroundColor Cyan
