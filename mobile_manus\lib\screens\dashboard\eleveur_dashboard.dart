import 'package:flutter/material.dart';
import 'package:mobile_manus/models/user.dart';
import 'package:mobile_manus/providers/auth_provider.dart';
import 'package:provider/provider.dart';

class EleveurDashboard extends StatefulWidget {
  final User user;

  const EleveurDashboard({super.key, required this.user});

  @override
  State<EleveurDashboard> createState() => _EleveurDashboardState();
}

class _EleveurDashboardState extends State<EleveurDashboard> {
  int _selectedIndex = 0;

  @override
  Widget build(BuildContext context) {
    return Scaffold(
      appBar: AppBar(
        title: Text('Tableau de bord - ${widget.user.firstName ?? widget.user.username}'),
        backgroundColor: Colors.green[700],
        foregroundColor: Colors.white,
        actions: [
          IconButton(
            icon: const Icon(Icons.notifications),
            onPressed: () {
              // TODO: Show notifications
            },
          ),
          IconButton(
            icon: const Icon(Icons.logout),
            onPressed: () {
              Provider.of<AuthProvider>(context, listen: false).logout();
            },
          ),
        ],
      ),
      body: IndexedStack(
        index: _selectedIndex,
        children: [
          _buildOverviewTab(),
          _buildFeedManagementTab(),
          _buildSalesTab(),
          _buildAnalyticsTab(),
        ],
      ),
      bottomNavigationBar: BottomNavigationBar(
        type: BottomNavigationBarType.fixed,
        currentIndex: _selectedIndex,
        onTap: (index) {
          setState(() {
            _selectedIndex = index;
          });
        },
        selectedItemColor: Colors.green[700],
        unselectedItemColor: Colors.grey,
        items: const [
          BottomNavigationBarItem(
            icon: Icon(Icons.dashboard),
            label: 'Aperçu',
          ),
          BottomNavigationBarItem(
            icon: Icon(Icons.grass),
            label: 'Aliments',
          ),
          BottomNavigationBarItem(
            icon: Icon(Icons.sell),
            label: 'Ventes',
          ),
          BottomNavigationBarItem(
            icon: Icon(Icons.analytics),
            label: 'Analyses',
          ),
        ],
      ),
    );
  }

  Widget _buildOverviewTab() {
    return SingleChildScrollView(
      padding: const EdgeInsets.all(16),
      child: Column(
        crossAxisAlignment: CrossAxisAlignment.start,
        children: [
          // Welcome Card
          Card(
            child: Padding(
              padding: const EdgeInsets.all(16),
              child: Row(
                children: [
                  CircleAvatar(
                    radius: 30,
                    backgroundColor: Colors.green[100],
                    child: Icon(
                      Icons.person,
                      size: 30,
                      color: Colors.green[700],
                    ),
                  ),
                  const SizedBox(width: 16),
                  Expanded(
                    child: Column(
                      crossAxisAlignment: CrossAxisAlignment.start,
                      children: [
                        Text(
                          'Bienvenue, ${widget.user.firstName ?? widget.user.username}!',
                          style: Theme.of(context).textTheme.titleLarge,
                        ),
                        Text(
                          'Éleveur',
                          style: Theme.of(context).textTheme.bodyMedium?.copyWith(
                            color: Colors.grey[600],
                          ),
                        ),
                      ],
                    ),
                  ),
                ],
              ),
            ),
          ),
          
          const SizedBox(height: 16),
          
          // Quick Stats
          Text(
            'Statistiques rapides',
            style: Theme.of(context).textTheme.titleLarge,
          ),
          const SizedBox(height: 8),
          
          Row(
            children: [
              Expanded(
                child: _buildStatCard(
                  'Volailles',
                  '1,250',
                  Icons.pets,
                  Colors.blue,
                ),
              ),
              const SizedBox(width: 8),
              Expanded(
                child: _buildStatCard(
                  'Œufs/jour',
                  '980',
                  Icons.egg,
                  Colors.orange,
                ),
              ),
            ],
          ),
          
          const SizedBox(height: 8),
          
          Row(
            children: [
              Expanded(
                child: _buildStatCard(
                  'Aliment (kg)',
                  '2,500',
                  Icons.grass,
                  Colors.green,
                ),
              ),
              const SizedBox(width: 8),
              Expanded(
                child: _buildStatCard(
                  'Revenus',
                  '45,000 DA',
                  Icons.attach_money,
                  Colors.purple,
                ),
              ),
            ],
          ),
          
          const SizedBox(height: 16),
          
          // Quick Actions
          Text(
            'Actions rapides',
            style: Theme.of(context).textTheme.titleLarge,
          ),
          const SizedBox(height: 8),
          
          GridView.count(
            shrinkWrap: true,
            physics: const NeverScrollableScrollPhysics(),
            crossAxisCount: 2,
            crossAxisSpacing: 8,
            mainAxisSpacing: 8,
            childAspectRatio: 2.5,
            children: [
              _buildQuickActionCard(
                'Saisie quotidienne',
                Icons.edit,
                Colors.blue,
                () {
                  // TODO: Navigate to daily entry
                },
              ),
              _buildQuickActionCard(
                'Nouvelle vente',
                Icons.sell,
                Colors.green,
                () {
                  // TODO: Navigate to new sale
                },
              ),
              _buildQuickActionCard(
                'Gestion aliments',
                Icons.grass,
                Colors.orange,
                () {
                  setState(() {
                    _selectedIndex = 1;
                  });
                },
              ),
              _buildQuickActionCard(
                'Consulter vétérinaire',
                Icons.medical_services,
                Colors.red,
                () {
                  // TODO: Navigate to vet consultation
                },
              ),
            ],
          ),
          
          const SizedBox(height: 16),
          
          // Recent Alerts
          Text(
            'Alertes récentes',
            style: Theme.of(context).textTheme.titleLarge,
          ),
          const SizedBox(height: 8),
          
          Card(
            child: Column(
              children: [
                _buildAlertItem(
                  'Stock d\'aliment faible',
                  'Il reste moins de 3 jours d\'aliment',
                  Icons.warning,
                  Colors.orange,
                ),
                const Divider(height: 1),
                _buildAlertItem(
                  'Consultation programmée',
                  'Visite vétérinaire demain à 14h',
                  Icons.schedule,
                  Colors.blue,
                ),
                const Divider(height: 1),
                _buildAlertItem(
                  'Production en baisse',
                  'Production d\'œufs -5% cette semaine',
                  Icons.trending_down,
                  Colors.red,
                ),
              ],
            ),
          ),
        ],
      ),
    );
  }

  Widget _buildStatCard(String title, String value, IconData icon, Color color) {
    return Card(
      child: Padding(
        padding: const EdgeInsets.all(16),
        child: Column(
          children: [
            Icon(icon, color: color, size: 32),
            const SizedBox(height: 8),
            Text(
              value,
              style: Theme.of(context).textTheme.titleLarge?.copyWith(
                fontWeight: FontWeight.bold,
                color: color,
              ),
            ),
            Text(
              title,
              style: Theme.of(context).textTheme.bodySmall,
              textAlign: TextAlign.center,
            ),
          ],
        ),
      ),
    );
  }

  Widget _buildQuickActionCard(String title, IconData icon, Color color, VoidCallback onTap) {
    return Card(
      child: InkWell(
        onTap: onTap,
        borderRadius: BorderRadius.circular(8),
        child: Padding(
          padding: const EdgeInsets.all(12),
          child: Row(
            children: [
              Icon(icon, color: color, size: 24),
              const SizedBox(width: 8),
              Expanded(
                child: Text(
                  title,
                  style: Theme.of(context).textTheme.bodyMedium?.copyWith(
                    fontWeight: FontWeight.w500,
                  ),
                ),
              ),
            ],
          ),
        ),
      ),
    );
  }

  Widget _buildAlertItem(String title, String subtitle, IconData icon, Color color) {
    return ListTile(
      leading: Icon(icon, color: color),
      title: Text(title),
      subtitle: Text(subtitle),
      trailing: const Icon(Icons.chevron_right),
      onTap: () {
        // TODO: Handle alert tap
      },
    );
  }

  Widget _buildFeedManagementTab() {
    return const Center(
      child: Text('Gestion des aliments - À implémenter'),
    );
  }

  Widget _buildSalesTab() {
    return const Center(
      child: Text('Gestion des ventes - À implémenter'),
    );
  }

  Widget _buildAnalyticsTab() {
    return const Center(
      child: Text('Analyses et graphiques - À implémenter'),
    );
  }
}

