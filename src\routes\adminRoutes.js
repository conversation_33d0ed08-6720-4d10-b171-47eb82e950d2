const express = require('express');
const router = express.Router();
const { Op } = require('sequelize');
const { Pool } = require('pg');
const bcrypt = require('bcryptjs');
const jwt = require('jsonwebtoken');
const { auth, checkRole } = require('../middleware/auth');
const admin = require('../middleware/admin');
const validatePagination = require('../middleware/pagination');
const roleController = require('../controllers/roleController');
const subscriptionPlanController = require('../controllers/subscriptionPlanController');
const homepageController = require('../controllers/homepageSectionController');
const models = require('../models');
const { User, Role } = models;
// Log the model configurations for debugging
console.log('Model configurations:', {
  User: {
    name: User.name,
    tableName: User.tableName,
    schema: User.schema
  },
  Role: {
    name: Role.name,
    tableName: Role.tableName,
    schema: Role.schema
  }
});
const { admin: firebaseAdmin } = require('../config/firebase');
const settingsController = require('../controllers/settingsController'); // Import settingsController

// Import du contrôleur admin
const adminController = require('../controllers/adminController');

// Configuration des constantes
const LOGIN_AS_USER_EXPIRATION = '1h'; // Durée de validité du token d'impersonation

// Logger pour les requêtes SQL
const logQuery = (query, params) => {
  console.log('SQL Query:', {
    text: query,
    params,
    timestamp: new Date().toISOString()
  });
};

// Configuration de la connexion à la base de données
const pool = new Pool({
  user: process.env.DB_USER,
  host: process.env.DB_HOST,
  database: process.env.DB_NAME,
  password: process.env.DB_PASSWORD,
  port: process.env.DB_PORT,
});

// Appliquer les middlewares d'authentification et d'admin à toutes les routes
router.use(auth);
router.use(admin);

// Routes pour la gestion des rôles
router.get('/roles', roleController.getAllRoles);
router.get('/roles/:id', roleController.getRoleById);
router.post('/roles', roleController.createRole);
router.put('/roles/:id', roleController.updateRole);
router.delete('/roles/:id', roleController.deleteRole);

// Routes pour les statistiques administrateur
router.get('/stats', adminController.getStats);

// Routes pour la gestion des utilisateurs
router.get('/users', validatePagination, adminController.getUsersByRole);
router.patch('/users/:id/status', adminController.updateUserStatus);

// Routes pour la gestion des paramètres et API keys
router.get('/settings/api-keys', settingsController.getApiConfig);
router.post('/settings/api-keys', settingsController.createApiConfig);

/**
 * Route permettant à un administrateur de se connecter en tant qu'utilisateur spécifique
 * Cette fonctionnalité est réservée aux administrateurs pour le test et le support
 * @route POST /api/admin/login-as-user/:userId
 * @param {string} userId - L'ID de l'utilisateur à impersonifier
 */
router.post('/login-as-user/:userId', async (req, res) => {
  try {
    const { userId } = req.params;

    // Vérifier si l'utilisateur existe
    const user = await User.findByPk(userId, {
      include: [{ model: models.Role, as: 'role' }] // Utilise le modèle Role importé et l'alias 'role'
    });

    if (!user) {
      return res.status(404).json({ message: 'Utilisateur non trouvé' });
    }

    // Vérifier que l'utilisateur n'est pas un admin
    // Accéder au nom du rôle via l'association chargée (user.role.name)
    if (user.role && user.role.name === 'admin') {
      return res.status(403).json({
        message: 'Impossible de se connecter en tant qu\'administrateur pour des raisons de sécurité'
      });
    }

    // Vérifier que l'utilisateur est actif
    if (!user.isActive) {
      return res.status(403).json({
        message: 'Impossible de se connecter en tant qu\'utilisateur inactif'
      });
    }

    // Générer un token JWT pour l'utilisateur sélectionné
    const impersonationStart = Date.now();
    const token = jwt.sign(
      {
        id: user.id,
        email: user.email,
        role: user.role.name, // Accéder au nom du rôle via l'association chargée
        role_id: user.role_id, // Inclure role_id pour la cohérence
        isImpersonating: true,
        adminId: req.user.id, // Stocker l'ID de l'admin pour traçabilité
        impersonationStart, // Horodatage du début de l'impersonation
      },
      process.env.JWT_SECRET,
      { expiresIn: LOGIN_AS_USER_EXPIRATION }
    );

    // Logger l'action pour l'audit
    console.log(`Admin ${req.user.id} s'est connecté en tant que ${user.role.name} (ID: ${user.id}) à ${new Date().toISOString()}`);

    res.json({
      token,
      user: {
        id: user.id,
        email: user.email,
        role: user.role.name, // Accéder au nom du rôle via l'association chargée
        isImpersonating: true,
        expiresIn: LOGIN_AS_USER_EXPIRATION,
        impersonationStart
      },
      impersonationStart,
      expiresIn: LOGIN_AS_USER_EXPIRATION,
      message: `Vous êtes maintenant connecté en tant que ${user.role.name}. Cette session expirera dans ${LOGIN_AS_USER_EXPIRATION}.`
    });

  } catch (error) {
    console.error('Erreur lors de la connexion en tant qu\'utilisateur:', error);
    res.status(500).json({ message: 'Erreur serveur lors de la connexion' });
  }
});

// Route pour obtenir la liste des utilisateurs avec pagination
// @route   GET /api/admin/users
// @desc    Get users with optional role filter and pagination
// @access  Private/Admin
router.get('/users', async (req, res) => {
  try {
    const page = parseInt(req.query.page) || 1;
    const limit = Math.min(parseInt(req.query.limit) || 10, 100); // Maximum 100 users per page
    const offset = (page - 1) * limit;
    const role = req.query.role;

    let whereClause = {};
    let includeOptions = [{
      model: models.Role,
      as: 'role',
      attributes: ['id', 'name', 'description']
    }];

    // Si un rôle est spécifié, filtrer par ce rôle
    if (role) {
      includeOptions[0].where = { name: role };
    }

    const { count, rows } = await User.findAndCountAll({
      where: whereClause,
      attributes: ['id', 'username', 'email', 'role_id', 'first_name', 'last_name', 'status', 'created_at', 'updated_at'],
      include: includeOptions,
      limit,
      offset,
      order: [['created_at', 'DESC']],
      distinct: true // Important pour le count avec include
    });

    // Formatter les résultats pour inclure le nom du rôle directement
    const formattedUsers = rows.map(user => {
      const userData = user.toJSON();
      return {
        ...userData,
        role: userData.role ? userData.role.name : null
      };
    });

    const totalPages = Math.ceil(count / limit);

    res.json({
      users: formattedUsers,
      pagination: {
        total: count,
        totalPages,
        currentPage: page,
        limit,
        hasMore: page < totalPages
      }
    });
  } catch (error) {
    console.error('Erreur lors de la récupération des utilisateurs:', error);
    res.status(500).json({
      message: 'Erreur lors de la récupération des utilisateurs',
      error: process.env.NODE_ENV === 'development' ? error.message : undefined
    });
  }
});

// Route pour mettre à jour un utilisateur
// @route   PUT /api/admin/users/:id
// @desc    Update a user's information
// @access  Private/Admin
// Delete user route
router.delete('/users/:id', async (req, res) => {
  try {
    const userId = req.params.id;

    // Find the user
    const user = await User.findByPk(userId, {
      include: [{ model: models.Role, as: 'role' }]
    });

    if (!user) {
      return res.status(404).json({ message: "Utilisateur non trouvé" });
    }

    // If user has a Firebase account, delete it from Firebase first
    if (user.firebase_uid) {
      try {
        await firebaseAdmin.auth().deleteUser(user.firebase_uid);
        console.log('Firebase user deleted successfully');
      } catch (firebaseError) {
        console.error('Error deleting Firebase user:', firebaseError);
        // Continue with PostgreSQL deletion even if Firebase deletion fails
      }
    }

    // Delete from PostgreSQL
    await user.destroy();

    res.json({ message: "Utilisateur supprimé avec succès" });
  } catch (error) {
    console.error('Erreur lors de la suppression de l\'utilisateur:', error);
    res.status(500).json({
      message: "Erreur serveur lors de la suppression de l'utilisateur",
      error: process.env.NODE_ENV === 'development' ? error.message : undefined
    });
  }
});

// Update user route
router.put('/users/:id', async (req, res) => {
  try {
    const userId = req.params.id;
    const { username, email, password, role, first_name, last_name, phone, address, status } = req.body;

    // Trouver l'utilisateur par ID
    const user = await User.findByPk(userId);
    if (!user) {
      return res.status(404).json({ message: "Utilisateur non trouvé" });
    }

    // Préparer les données à mettre à jour
    const updateData = {
      username,
      email,
      first_name,
      last_name,
      phone,
      address,
      status
    };

    // Si un nouveau mot de passe est fourni, le hacher et l'ajouter aux données à mettre à jour
    if (password && password.trim() !== '') {
      const salt = await bcrypt.genSalt(10);
      updateData.password = await bcrypt.hash(password, salt);
    }

    // Si le rôle est changé, trouver l'ID du rôle correspondant
    if (role) {
      const roleObject = await models.Role.findOne({ where: { name: role } });
      if (roleObject) {
        updateData.role_id = roleObject.id;
      }
    }

    // Mettre à jour l'utilisateur
    await user.update(updateData);

    res.json({ message: "Utilisateur mis à jour avec succès", user: user });
  } catch (error) {
    console.error('Erreur lors de la mise à jour de l\'utilisateur:', error);
    res.status(500).json({ message: "Erreur serveur lors de la mise à jour de l'utilisateur" });
  }
});

// Route pour créer un nouvel utilisateur
// @route   POST /api/admin/users
// @desc    Create a new user
// @access  Private/Admin
router.post('/users', async (req, res) => {
  try {
    const { username, email, password, role, first_name, last_name, phone, address, status = 'active' } = req.body;

    console.log('Creating new user with data:', {
      email,
      role,
      first_name,
      last_name,
      status
    });

    // Validation des champs requis
    if (!email || !password || !role) {
      return res.status(400).json({
        message: "Email, mot de passe et rôle sont requis",
        details: {
          email: !email ? 'Email requis' : null,
          password: !password ? 'Mot de passe requis' : null,
          role: !role ? 'Rôle requis' : null
        }
      });
    }

    // Vérifier si l'email existe déjà
    const existingUser = await User.findOne({ where: { email } });
    if (existingUser) {
      return res.status(400).json({ message: "Un utilisateur avec cet email existe déjà" });
    }

    // Trouver l'ID du rôle d'abord
    let roleId = null;
    try {
      // Log available roles first
      const availableRoles = await models.Role.findAll();
      console.log('Available roles:', availableRoles.map(r => ({ id: r.id, name: r.name })));

      const roleObject = await models.Role.findOne({
        where: {
          name: { [Op.iLike]: role } // Case-insensitive comparison
        }
      });

      if (!roleObject) {
        return res.status(400).json({
          message: "Rôle invalide",
          details: `Le rôle '${role}' n'existe pas dans le système`,
          availableRoles: availableRoles.map(r => r.name)
        });
      }
      roleId = roleObject.id;
      console.log(`Found role ${roleObject.name} with ID ${roleId}`);
    } catch (roleError) {
      console.error('Error finding role:', roleError);
      return res.status(500).json({
        message: "Erreur lors de la vérification du rôle",
        error: roleError.message,
        stack: process.env.NODE_ENV === 'development' ? roleError.stack : undefined
      });
    }

    // Create user in Firebase first
    let firebaseUser;
    try {
      firebaseUser = await firebaseAdmin.auth().createUser({
        email: email,
        password: password,
        displayName: `${first_name} ${last_name}`.trim() || username
      });
      console.log('Firebase user created successfully:', firebaseUser.uid);
    } catch (firebaseError) {
      console.error('Error creating Firebase user:', firebaseError);
      return res.status(500).json({
        message: "Erreur lors de la création du compte Firebase",
        error: firebaseError.message
      });
    }

    // Hacher le mot de passe pour PostgreSQL
    let hashedPassword;
    try {
      const salt = await bcrypt.genSalt(10);
      hashedPassword = await bcrypt.hash(password, salt);
    } catch (hashError) {
      console.error('Error hashing password:', hashError);
      // Try to clean up the Firebase user if password hashing fails
      try {
        await firebaseAdmin.auth().deleteUser(firebaseUser.uid);
      } catch (deleteError) {
        console.error('Error cleaning up Firebase user after password hash failure:', deleteError);
      }
      return res.status(500).json({
        message: "Erreur lors du hachage du mot de passe",
        error: hashError.message
      });
    }

    // Créer l'utilisateur dans PostgreSQL avec le Firebase UID
    let newUser;
    try {
      newUser = await User.create({
        username: username || email.split('@')[0], // Use email prefix if no username provided
        email,
        password: hashedPassword,
        first_name,
        last_name,
        phone,
        address,
        status,
        role_id: roleId,
        firebase_uid: firebaseUser.uid,
        isActive: status === 'active'
      });
    } catch (dbError) {
      console.error('Error creating user in database:', dbError);
      // Clean up Firebase user if database creation fails
      try {
        await firebaseAdmin.auth().deleteUser(firebaseUser.uid);
      } catch (deleteError) {
        console.error('Error cleaning up Firebase user after database failure:', deleteError);
      }
      return res.status(500).json({
        message: "Erreur lors de la création de l'utilisateur dans la base de données",
        error: dbError.message
      });
    }

    // Récupérer l'utilisateur créé avec le rôle
    let userWithRole;
    try {
      userWithRole = await User.findByPk(newUser.id, {
        include: [{ model: models.Role, as: 'role' }]
      });

      if (!userWithRole) {
        throw new Error('User not found after creation');
      }
    } catch (fetchError) {
      console.error('Error fetching created user:', fetchError);
      return res.status(500).json({
        message: "Utilisateur créé mais erreur lors de la récupération des détails",
        error: fetchError.message
      });
    }

    res.status(201).json({
      message: "Utilisateur créé avec succès",
      user: {
        id: userWithRole.id,
        username: userWithRole.username,
        email: userWithRole.email,
        first_name: userWithRole.first_name,
        last_name: userWithRole.last_name,
        phone: userWithRole.phone,
        address: userWithRole.address,
        status: userWithRole.status,
        role: userWithRole.role ? userWithRole.role.name : null,
        isActive: userWithRole.isActive,
        createdAt: userWithRole.createdAt,
        updatedAt: userWithRole.updatedAt
      }
    });
  } catch (error) {
    console.error('Erreur lors de la création de l\'utilisateur:', error);
    res.status(500).json({
      message: "Erreur serveur lors de la création de l'utilisateur",
      error: process.env.NODE_ENV === 'development' ? error.message : undefined
    });
  }
});

// Route pour obtenir les données de plateforme avec filtres
router.get('/data', async (req, res) => {
  try {
    const { type = 'sales', timeRange = 'month' } = req.query;

    // Calculer la période en fonction du timeRange
    let dateFilter = '';
    switch (timeRange) {
      case 'day':
        dateFilter = "AND created_at >= NOW() - INTERVAL '1 day'";
        break;
      case 'week':
        dateFilter = "AND created_at >= NOW() - INTERVAL '7 days'";
        break;
      case 'month':
        dateFilter = "AND created_at >= NOW() - INTERVAL '30 days'";
        break;
      case 'year':
        dateFilter = "AND created_at >= NOW() - INTERVAL '365 days'";
        break;
      default:
        dateFilter = "AND created_at >= NOW() - INTERVAL '30 days'";
    }

    let data = [];
    let total = 0;
    let growth = 0;

    switch (type) {
      case 'sales':
        // Utiliser les bons noms de colonnes pour les timestamps
        // Données de ventes simulées (à remplacer par vraies données)
        const salesQuery = `
          SELECT
            DATE("createdAt") as date,
            COUNT(*) as value
          FROM "Ventes"
          WHERE 1=1 ${dateFilter}
          GROUP BY DATE("createdAt")
          ORDER BY date DESC
        `;

        try {
          const salesResult = await pool.query(salesQuery);
          data = salesResult.rows;
          total = data.reduce((sum, item) => sum + parseInt(item.value), 0);
        } catch (err) {
          // Si la table ventes n'existe pas, retourner des données simulées
          data = generateMockData(timeRange, 'sales');
          total = data.reduce((sum, item) => sum + item.value, 0);
        }
        break;

      case 'users':
        const usersQuery = `
          SELECT
            DATE("createdAt") as date,
            COUNT(*) as value
          FROM "Users"
          WHERE 1=1 ${dateFilter}
          GROUP BY DATE("createdAt")
          ORDER BY date DESC
        `;

        const usersResult = await pool.query(usersQuery);
        data = usersResult.rows;
        total = data.reduce((sum, item) => sum + parseInt(item.value), 0);
        break;

      case 'revenue':
        // Données de revenus simulées
        data = generateMockData(timeRange, 'revenue');
        total = data.reduce((sum, item) => sum + item.value, 0);
        break;

      default:
        data = generateMockData(timeRange, type);
        total = data.reduce((sum, item) => sum + item.value, 0);
    }

    // Calculer la croissance (simulée)
    growth = Math.random() * 20 - 5; // Entre -5% et +15%

    res.json({
      data,
      total,
      growth: parseFloat(growth.toFixed(2)),
      type,
      timeRange
    });
  } catch (error) {
    console.error('Erreur lors de la récupération des données de plateforme:', error);
    res.status(500).json({
      message: 'Erreur lors de la récupération des données de plateforme',
      error: error.message
    });
  }
});

// Fonction utilitaire pour générer des données simulées
function generateMockData(timeRange, type) {
  const data = [];
  const now = new Date();
  let days = 30;

  switch (timeRange) {
    case 'day': days = 1; break;
    case 'week': days = 7; break;
    case 'month': days = 30; break;
    case 'year': days = 365; break;
  }

  for (let i = days - 1; i >= 0; i--) {
    const date = new Date(now);
    date.setDate(date.getDate() - i);

    let value;
    switch (type) {
      case 'sales':
        // Utiliser les bons noms de colonnes pour les timestamps
        value = Math.floor(Math.random() * 50) + 10;
        break;
      case 'users':
        value = Math.floor(Math.random() * 20) + 5;
        break;
      case 'revenue':
        value = Math.floor(Math.random() * 10000) + 2000;
        break;
      default:
        value = Math.floor(Math.random() * 100) + 10;
    }

    data.push({
      date: date.toISOString().split('T')[0],
      value
    });
  }

  return data;
}

// Route pour obtenir les statistiques générales
router.get('/stats', async (req, res) => {
  try {
    // Vérifier si la table ventes existe
    const tableExists = await pool.query(`
      SELECT EXISTS (
        SELECT FROM information_schema.tables
        WHERE table_schema = 'public'
        AND table_name = 'ventes'
      )
    `);

    const ventesTableExists = tableExists.rows[0].exists;
    console.log('Table ventes existe:', ventesTableExists);

    // Récupérer le nombre total d'utilisateurs par rôle
    const userStats = await pool.query(
      `SELECT role, COUNT(*) as count
       FROM users
       GROUP BY role`
    );

    // Initialize role statistics with zeros
    const roleStats = {
      admin: 0,
      eleveur: 0,
      veterinaire: 0,
      marchand: 0
    };

    // Update with actual counts
    userStats.rows.forEach(row => {
      if (row.role && row.count) {
        roleStats[row.role] = parseInt(row.count);
      }
    });

    // Récupérer les statistiques des volailles
    let volailleStats;
    try {
      // Vérifier les colonnes de la table volailles
      const columnsQuery = await pool.query(`
        SELECT column_name
        FROM information_schema.columns
        WHERE table_name = 'volailles'
      `);

      const columns = columnsQuery.rows.map(row => row.column_name);
      console.log('Colonnes de la table volailles:', columns);

      // Vérifier si la colonne 'statut' ou 'active' existe
      const statusColumn = columns.includes('statut') ? 'statut' :
                          (columns.includes('active') ? 'active' : null);

      let whereClause = '';
      if (statusColumn) {
        if (statusColumn === 'statut') {
          whereClause = `WHERE statut = 'disponible'`;
        } else if (statusColumn === 'active') {
          whereClause = `WHERE active = true`;
        }
      }

      volailleStats = await pool.query(`
        SELECT
          COUNT(*) as total_volailles,
          COUNT(DISTINCT eleveur_id) as total_eleveurs,
          COALESCE(AVG(prix_unitaire), 0) as prix_moyen
        FROM volailles
        ${whereClause}
      `);
    } catch (err) {
      console.error('Erreur lors de la récupération des statistiques de volailles:', err);
      volailleStats = { rows: [{ total_volailles: 0, total_eleveurs: 0, prix_moyen: 0 }] };
    }

    // Récupérer les statistiques des ventes
    let venteStats;
    if (ventesTableExists) {
      try {
        venteStats = await pool.query(
          `SELECT
            COUNT(*) as total_ventes,
            COALESCE(SUM(montant_total), 0) as chiffre_affaires_total
           FROM ventes
           WHERE date_vente >= NOW() - INTERVAL '30 days'`
        );
      } catch (err) {
        console.error('Erreur lors de la récupération des statistiques de ventes:', err);
        venteStats = { rows: [{ total_ventes: 0, chiffre_affaires_total: 0 }] };
      }
    } else {
      venteStats = { rows: [{ total_ventes: 0, chiffre_affaires_total: 0 }] };
    }

    res.json({
      users: userStats.rows,
      volailles: volailleStats.rows[0],
      ventes: venteStats.rows[0],
      totalEleveurs: roleStats['eleveur'] || 0,
      totalVeterinaires: roleStats['veterinaire'] || 0,
      totalMarchands: roleStats['marchand'] || 0,
      totalAdmins: roleStats['admin'] || 0,
      totalUsers: userStats.rows.reduce((sum, row) => sum + parseInt(row.count), 0)
    });
  } catch (error) {
    console.error('Erreur lors de la récupération des statistiques:', error);
    res.status(500).json({
      message: 'Erreur lors de la récupération des statistiques',
      error: error.message
    });
  }
});

// Routes pour les rôles
router.get('/roles', roleController.getAllRoles);
router.get('/roles/:id', roleController.getRoleById);
router.post('/roles', roleController.createRole);
router.put('/roles/:id', roleController.updateRole);
router.delete('/roles/:id', roleController.deleteRole);

// Routes pour les plans d'abonnement
router.get('/subscription-plans', subscriptionPlanController.getAllPlans);
router.get('/subscription-plans/:id', subscriptionPlanController.getPlanById);
router.post('/subscription-plans', subscriptionPlanController.createPlan);
router.put('/subscription-plans/:id', subscriptionPlanController.updatePlan);
router.delete('/subscription-plans/:id', subscriptionPlanController.deletePlan);

// Routes pour les notifications admin
router.get('/notifications', async (req, res) => {
  try {
    const { page = 1, limit = 10 } = req.query;
    const offset = (page - 1) * limit;

    // Récupérer toutes les notifications avec pagination
    const notificationsQuery = `
      SELECT n.*, u.username, u.email
      FROM notifications n
      LEFT JOIN users u ON n.user_id = u.id
      ORDER BY n.created_at DESC
      LIMIT $1 OFFSET $2
    `;

    const countQuery = 'SELECT COUNT(*) FROM notifications';

    const [notificationsResult, countResult] = await Promise.all([
      pool.query(notificationsQuery, [limit, offset]),
      pool.query(countQuery)
    ]);

    const totalNotifications = parseInt(countResult.rows[0].count);
    const totalPages = Math.ceil(totalNotifications / limit);

    res.json({
      notifications: notificationsResult.rows,
      pagination: {
        currentPage: parseInt(page),
        totalPages,
        totalItems: totalNotifications,
        itemsPerPage: parseInt(limit)
      }
    });
  } catch (error) {
    console.error('Erreur lors de la récupération des notifications admin:', error);
    res.status(500).json({
      message: 'Erreur lors de la récupération des notifications',
      error: error.message
    });
  }
});

// Route pour envoyer une notification (admin)
router.post('/notifications/send', async (req, res) => {
  try {
    const { userId, title, message, type, data, email, push } = req.body;

    if (!userId || !title || !message) {
      return res.status(400).json({ message: 'User ID, title, and message are required' });
    }

    // Import the notification service
    const { notifyUser } = require('../services/notifications');

    const result = await notifyUser({
      userId,
      title,
      message,
      type: type || 'admin',
      data: data || {},
      email: email === true,
      push: push === true,
    });

    res.json(result);
  } catch (error) {
    console.error('Erreur lors de l\'envoi de la notification:', error);
    res.status(500).json({
      message: 'Erreur lors de l\'envoi de la notification',
      error: error.message
    });
  }
});

// Route pour supprimer une notification
router.delete('/notifications/:id', async (req, res) => {
  try {
    const { id } = req.params;

    const deleteQuery = 'DELETE FROM notifications WHERE id = $1 RETURNING *';
    const result = await pool.query(deleteQuery, [id]);

    if (result.rowCount === 0) {
      return res.status(404).json({ message: 'Notification non trouvée' });
    }

    res.json({ message: 'Notification supprimée avec succès' });
  } catch (error) {
    console.error('Erreur lors de la suppression de la notification:', error);
    res.status(500).json({
      message: 'Erreur lors de la suppression de la notification',
      error: error.message
    });
  }
});

// Settings Routes
router.get('/settings/smtp', settingsController.getSmtpConfig);
router.post('/settings/smtp', settingsController.updateSmtpConfig);
router.post('/settings/smtp/test', settingsController.testSmtpConfig);
router.get('/settings/security', settingsController.getSecuritySettings);
router.post('/settings/security', settingsController.updateSecuritySettings);
router.get('/settings/general', settingsController.getGeneralSettings);
router.post('/settings/general', settingsController.updateGeneralSettings);

// API Keys routes - added explicit routes for api-keys that match frontend paths
router.get('/settings/api-keys', settingsController.getApiConfig);
router.post('/settings/api-keys', settingsController.createApiConfig);
router.put('/settings/api-keys/:id', settingsController.updateApiConfig);
router.delete('/settings/api-keys/:id', settingsController.deleteApiConfig);

// Homepage section routes
router.get('/homepage/sections', homepageController.getAllSections);
router.post('/homepage/sections', homepageController.createSection);
router.put('/homepage/sections/:id', homepageController.updateSection);
router.delete('/homepage/sections/:id', homepageController.deleteSection);
router.put('/homepage/sections/order/update', homepageController.updateSectionOrders);

// AI Config routes (keeping for compatibility)
router.get('/ai/config', settingsController.getApiConfig);
router.post('/ai/config', settingsController.createApiConfig);
router.put('/ai/config/:id', settingsController.updateApiConfig);
router.delete('/ai/config/:id', settingsController.deleteApiConfig);

module.exports = router;
