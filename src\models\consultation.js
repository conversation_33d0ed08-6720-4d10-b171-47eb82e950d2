const { DataTypes, Model } = require('sequelize');

module.exports = (sequelize, DataTypes) => {
  class Consultation extends Model {    static associate(models) {
      Consultation.belongsTo(models.Veterinaire, {
        foreignKey: 'veterinaire_id',
        as: 'veterinaire'
      });
      Consultation.belongsTo(models.User, {
        foreignKey: 'eleveur_id',
        as: 'eleveur'
      });
      Consultation.belongsTo(models.Volaille, {
        foreignKey: 'volaille_id',
        as: 'volaille'
      });
      Consultation.hasMany(models.Prescription, {
        foreignKey: 'consultation_id',
        as: 'prescriptions'
      });
    }

    static async findByEleveur(eleveurId) {
      return await this.findAll({
        where: { eleveur_id: eleveurId },
        order: [['created_at', 'DESC']]
      });
    }

    static async findByVeterinaire(veterinaireId) {
      return await this.findAll({
        where: { veterinaire_id: veterinaireId },
        order: [['created_at', 'DESC']]
      });
    }

    static async findByStatut(statut) {
      return await this.findAll({
        where: { statut },
        order: [['created_at', 'DESC']]
      });
    }

    static async updateStatut(id, statut) {
      return await this.update(
        { statut },
        { where: { id } }
      );
    }
  }

  Consultation.init({
    id: {
      type: DataTypes.INTEGER,
      primaryKey: true,
      autoIncrement: true
    },
    veterinaire_id: {
      type: DataTypes.INTEGER,
      allowNull: false,
      references: {
        model: 'users',
        key: 'id'
      }
    },
    eleveur_id: {
      type: DataTypes.INTEGER,
      allowNull: false,
      references: {
        model: 'users',
        key: 'id'
      }
    },
    volaille_id: {
      type: DataTypes.INTEGER,
      allowNull: false,
      references: {
        model: 'volailles',
        key: 'id'
      }
    },
    date_consultation: {
      type: DataTypes.DATE,
      defaultValue: DataTypes.NOW
    },
    symptomes: {
      type: DataTypes.TEXT
    },
    diagnostic: {
      type: DataTypes.TEXT
    },
    traitement: {
      type: DataTypes.TEXT
    },
    notes: {
      type: DataTypes.TEXT
    },
    statut: {
      type: DataTypes.STRING(20),
      defaultValue: 'en_cours'
    },
    cout: {
      type: DataTypes.DECIMAL(10, 2)
    }
  }, {
    sequelize,
    modelName: 'Consultation',
    tableName: 'consultations',
    timestamps: true,
    createdAt: 'created_at',
    updatedAt: 'updated_at'
  });

  return Consultation;
};
