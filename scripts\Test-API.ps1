# Test-API.ps1
# Script pour tester l'accessibilité de l'API

$baseUrls = @(
    'http://localhost:3003',
    'http://127.0.0.1:3003',
    'http://*************:3003'
)

$endpoints = @(
    '',
    '/api',
    '/api/health',
    '/api/auth',
    '/api/eleveur',
    '/api/veterinaire'
)

function Test-Endpoint {
    param (
        [string]$url
    )

    Write-Host "\nTesting endpoint: $url" -ForegroundColor Cyan
    try {
        $response = Invoke-WebRequest -Uri $url -Method Get -UseBasicParsing -TimeoutSec 5
        Write-Host "[SUCCESS] Status: $($response.StatusCode) $($response.StatusDescription)" -ForegroundColor Green
        
        if ($response.Content) {
            try {
                $jsonContent = $response.Content | ConvertFrom-Json
                Write-Host "Response Data:" -ForegroundColor Yellow
                $jsonContent | Format-List
            }
            catch {
                Write-Host "Raw Response: $($response.Content)" -ForegroundColor Yellow
            }
        }
    }
    catch {
        Write-Host "[ERROR] $($_.Exception.Message)" -ForegroundColor Red
        if ($_.Exception.Response) {
            Write-Host "Status Code: $($_.Exception.Response.StatusCode.value__)" -ForegroundColor Red
        }
    }
    Write-Host ("-" * 80)
}

Write-Host "\nAPI Accessibility Test" -ForegroundColor Yellow
Write-Host "==================" -ForegroundColor Yellow

# Test port accessibility first
foreach ($baseUrl in $baseUrls) {
    $uri = [System.Uri]$baseUrl
    Write-Host "\nChecking port accessibility for $($uri.Host):$($uri.Port)..." -ForegroundColor Cyan
    try {
        $portTest = Test-NetConnection -ComputerName $uri.Host -Port $uri.Port -WarningAction SilentlyContinue
        if ($portTest.TcpTestSucceeded) {
            Write-Host "[SUCCESS] Port $($uri.Port) is accessible" -ForegroundColor Green
        }
        else {
            Write-Host "[FAILED] Port $($uri.Port) is not accessible" -ForegroundColor Red
        }
    }
    catch {
        Write-Host "[ERROR] Testing port: $($_.Exception.Message)" -ForegroundColor Red
    }
}

# Test each endpoint
foreach ($baseUrl in $baseUrls) {
    Write-Host "\nTesting endpoints for $baseUrl" -ForegroundColor Yellow
    Write-Host ("-" * 80)
    
    foreach ($endpoint in $endpoints) {
        $fullUrl = $baseUrl + $endpoint
        Test-Endpoint -url $fullUrl
        Start-Sleep -Milliseconds 500  # Add small delay between requests
    }
}