
import { Box, Paper, Typography, Avatar } from '@mui/material';
import { styled } from '@mui/material/styles';

// Style personnalisé pour la carte de statistique
const StyledPaper = styled(Paper)(({ theme }) => ({
  padding: theme.spacing(3),
  display: 'flex',
  alignItems: 'center',
  height: '100%',
  transition: 'transform 0.3s ease-in-out, box-shadow 0.3s ease-in-out',
  '&:hover': {
    transform: 'translateY(-5px)',
    boxShadow: theme.shadows[10],
  },
}));

const StyledAvatar = styled(Avatar)(({ theme, bgcolor }) => ({
  backgroundColor: bgcolor || theme.palette.primary.main,
  width: 56,
  height: 56,
  marginRight: theme.spacing(2),
}));

/**
 * Composant de carte statistique pour afficher une métrique clé
 * @param {Object} props - Propriétés du composant
 * @param {string} props.title - Titre de la statistique
 * @param {string|number} props.value - Valeur de la statistique
 * @param {React.ReactNode} props.icon - Icône à afficher
 * @param {string} props.color - Couleur de l'avatar
 * @param {string} props.subtitle - Sous-titre optionnel (ex: variation)
 */
const StatCard = ({ title, value, icon, color, subtitle }) => {
  return (
    <StyledPaper elevation={3}>
      <StyledAvatar bgcolor={color}>
        {icon}
      </StyledAvatar>
      <Box>
        <Typography variant="body2" color="textSecondary" gutterBottom>
          {title}
        </Typography>
        <Typography variant="h5" component="div" sx={{ fontWeight: 'bold' }}>
          {value}
        </Typography>
        {subtitle && (
          <Typography variant="caption" color="textSecondary">
            {subtitle}
          </Typography>
        )}
      </Box>
    </StyledPaper>
  );
};

export default StatCard;
