import 'package:flutter/material.dart';
import 'package:provider/provider.dart';
import 'package:mobile_manus/providers/feed_provider.dart';
import 'package:mobile_manus/models/feed_consumption_log.dart';
import 'package:mobile_manus/models/feed_plan.dart';

class FeedManagementScreen extends StatefulWidget {
  const FeedManagementScreen({super.key});

  @override
  State<FeedManagementScreen> createState() => _FeedManagementScreenState();
}

class _FeedManagementScreenState extends State<FeedManagementScreen>
    with SingleTickerProviderStateMixin {
  late TabController _tabController;

  @override
  void initState() {
    super.initState();
    _tabController = TabController(length: 3, vsync: this);

    // Load feed data when screen initializes
    WidgetsBinding.instance.addPostFrameCallback((_) {
      Provider.of<FeedProvider>(context, listen: false).loadFeedPlans();
      Provider.of<FeedProvider>(context, listen: false).loadConsumptionLogs();
    });
  }

  @override
  void dispose() {
    _tabController.dispose();
    super.dispose();
  }

  @override
  Widget build(BuildContext context) {
    return Scaffold(
      appBar: AppBar(
        title: const Text('Gestion des Aliments'),
        backgroundColor: Colors.green[700],
        foregroundColor: Colors.white,
        bottom: TabBar(
          controller: _tabController,
          labelColor: Colors.white,
          unselectedLabelColor: Colors.white70,
          indicatorColor: Colors.white,
          tabs: const [
            Tab(text: 'Stock', icon: Icon(Icons.inventory)),
            Tab(text: 'Plans', icon: Icon(Icons.schedule)),
            Tab(text: 'Consommation', icon: Icon(Icons.analytics)),
          ],
        ),
      ),
      body: TabBarView(
        controller: _tabController,
        children: [
          _buildStockTab(),
          _buildPlansTab(),
          _buildConsumptionTab(),
        ],
      ),
      floatingActionButton: FloatingActionButton(
        onPressed: () {
          _showAddDialog();
        },
        backgroundColor: Colors.green[700],
        child: const Icon(Icons.add, color: Colors.white),
      ),
    );
  }

  Widget _buildStockTab() {
    return Consumer<FeedProvider>(
      builder: (context, feedProvider, child) {
        return RefreshIndicator(
          onRefresh: () async {
            await feedProvider.loadFeedPlans();
          },
          child: ListView(
            padding: const EdgeInsets.all(16),
            children: [
              // Stock Summary Cards
              Row(
                children: [
                  Expanded(
                    child: _buildStockCard(
                      'Total Stock',
                      '2,500 kg',
                      Icons.inventory,
                      Colors.blue,
                    ),
                  ),
                  const SizedBox(width: 8),
                  Expanded(
                    child: _buildStockCard(
                      'Jours restants',
                      '12 jours',
                      Icons.schedule,
                      Colors.orange,
                    ),
                  ),
                ],
              ),

              const SizedBox(height: 16),

              // Feed Types
              Text(
                'Types d\'aliments',
                style: Theme.of(context).textTheme.titleLarge,
              ),
              const SizedBox(height: 8),

              Card(
                child: Column(
                  children: [
                    _buildFeedTypeItem(
                      'Aliment Pondeuse',
                      '1,200 kg',
                      '8 jours',
                      Colors.green,
                      85,
                    ),
                    const Divider(height: 1),
                    _buildFeedTypeItem(
                      'Aliment Poussin',
                      '800 kg',
                      '15 jours',
                      Colors.blue,
                      60,
                    ),
                    const Divider(height: 1),
                    _buildFeedTypeItem(
                      'Aliment Croissance',
                      '500 kg',
                      '5 jours',
                      Colors.orange,
                      25,
                    ),
                  ],
                ),
              ),

              const SizedBox(height: 16),

              // Recent Deliveries
              Text(
                'Livraisons récentes',
                style: Theme.of(context).textTheme.titleLarge,
              ),
              const SizedBox(height: 8),

              Card(
                child: Column(
                  children: [
                    _buildDeliveryItem(
                      '15/06/2025',
                      'Aliment Pondeuse Premium',
                      '20 sacs (1000 kg)',
                      'Fournisseur ABC',
                    ),
                    const Divider(height: 1),
                    _buildDeliveryItem(
                      '12/06/2025',
                      'Vitamines A+D3',
                      '5 flacons',
                      'Pharmacie Vétérinaire',
                    ),
                    const Divider(height: 1),
                    _buildDeliveryItem(
                      '10/06/2025',
                      'Aliment Poussin',
                      '15 sacs (750 kg)',
                      'Fournisseur XYZ',
                    ),
                  ],
                ),
              ),
            ],
          ),
        );
      },
    );
  }

  Widget _buildPlansTab() {
    return Consumer<FeedProvider>(
      builder: (context, feedProvider, child) {
        return RefreshIndicator(
          onRefresh: () async {
            await feedProvider.loadFeedPlans();
          },
          child: ListView(
            padding: const EdgeInsets.all(16),
            children: [
              // Active Plans
              Text(
                'Plans d\'alimentation actifs',
                style: Theme.of(context).textTheme.titleLarge,
              ),
              const SizedBox(height: 8),

              Card(
                child: Column(
                  children: [
                    _buildPlanItem(
                      'Plan Pondeuses Bâtiment A',
                      '150 kg/jour',
                      '06:00 - 18:00',
                      'Actif',
                      Colors.green,
                    ),
                    const Divider(height: 1),
                    _buildPlanItem(
                      'Plan Poussins Bâtiment B',
                      '80 kg/jour',
                      '07:00 - 19:00',
                      'Actif',
                      Colors.green,
                    ),
                    const Divider(height: 1),
                    _buildPlanItem(
                      'Plan Croissance Bâtiment C',
                      '120 kg/jour',
                      '06:30 - 18:30',
                      'Suspendu',
                      Colors.orange,
                    ),
                  ],
                ),
              ),

              const SizedBox(height: 16),

              // Feed Schedule
              Text(
                'Horaires d\'alimentation aujourd\'hui',
                style: Theme.of(context).textTheme.titleLarge,
              ),
              const SizedBox(height: 8),

              Card(
                child: Column(
                  children: [
                    _buildScheduleItem(
                      '06:00',
                      'Distribution matinale',
                      'Bâtiments A, B, C',
                      '350 kg total',
                      Colors.blue,
                    ),
                    const Divider(height: 1),
                    _buildScheduleItem(
                      '12:00',
                      'Distribution midi',
                      'Bâtiments A, B',
                      '230 kg total',
                      Colors.green,
                    ),
                    const Divider(height: 1),
                    _buildScheduleItem(
                      '18:00',
                      'Distribution soir',
                      'Tous bâtiments',
                      '400 kg total',
                      Colors.orange,
                    ),
                  ],
                ),
              ),
            ],
          ),
        );
      },
    );
  }

  Widget _buildConsumptionTab() {
    return Consumer<FeedProvider>(
      builder: (context, feedProvider, child) {
        return RefreshIndicator(
          onRefresh: () async {
            await feedProvider.loadConsumptionLogs();
          },
          child: ListView(
            padding: const EdgeInsets.all(16),
            children: [
              // Consumption Summary
              Row(
                children: [
                  Expanded(
                    child: _buildStockCard(
                      'Aujourd\'hui',
                      '350 kg',
                      Icons.today,
                      Colors.green,
                    ),
                  ),
                  const SizedBox(width: 8),
                  Expanded(
                    child: _buildStockCard(
                      'Cette semaine',
                      '2,450 kg',
                      Icons.calendar_today,
                      Colors.blue,
                    ),
                  ),
                ],
              ),

              const SizedBox(height: 16),

              // Daily Consumption
              Text(
                'Consommation quotidienne',
                style: Theme.of(context).textTheme.titleLarge,
              ),
              const SizedBox(height: 8),

              Card(
                child: Column(
                  children: [
                    _buildConsumptionItem(
                      '19/06/2025',
                      '350 kg',
                      '98%',
                      'Normal',
                      Colors.green,
                    ),
                    const Divider(height: 1),
                    _buildConsumptionItem(
                      '18/06/2025',
                      '340 kg',
                      '95%',
                      'Normal',
                      Colors.green,
                    ),
                    const Divider(height: 1),
                    _buildConsumptionItem(
                      '17/06/2025',
                      '320 kg',
                      '89%',
                      'Faible',
                      Colors.orange,
                    ),
                    const Divider(height: 1),
                    _buildConsumptionItem(
                      '16/06/2025',
                      '380 kg',
                      '106%',
                      'Élevé',
                      Colors.red,
                    ),
                  ],
                ),
              ),

              const SizedBox(height: 16),

              // Efficiency Analysis
              Text(
                'Analyse d\'efficacité',
                style: Theme.of(context).textTheme.titleLarge,
              ),
              const SizedBox(height: 8),

              Card(
                child: Padding(
                  padding: const EdgeInsets.all(16),
                  child: Column(
                    children: [
                      Row(
                        mainAxisAlignment: MainAxisAlignment.spaceBetween,
                        children: [
                          const Text('Conversion alimentaire'),
                          Text(
                            '2.1 kg/kg',
                            style: TextStyle(
                              fontWeight: FontWeight.bold,
                              color: Colors.green[700],
                            ),
                          ),
                        ],
                      ),
                      const SizedBox(height: 8),
                      Row(
                        mainAxisAlignment: MainAxisAlignment.spaceBetween,
                        children: [
                          const Text('Gaspillage estimé'),
                          Text(
                            '3.2%',
                            style: TextStyle(
                              fontWeight: FontWeight.bold,
                              color: Colors.orange[700],
                            ),
                          ),
                        ],
                      ),
                      const SizedBox(height: 8),
                      Row(
                        mainAxisAlignment: MainAxisAlignment.spaceBetween,
                        children: [
                          const Text('Coût par kg produit'),
                          Text(
                            '180 DA',
                            style: TextStyle(
                              fontWeight: FontWeight.bold,
                              color: Colors.blue[700],
                            ),
                          ),
                        ],
                      ),
                    ],
                  ),
                ),
              ),
            ],
          ),
        );
      },
    );
  }

  Widget _buildStockCard(String title, String value, IconData icon, Color color) {
    return Card(
      child: Padding(
        padding: const EdgeInsets.all(16),
        child: Column(
          children: [
            Icon(icon, color: color, size: 32),
            const SizedBox(height: 8),
            Text(
              value,
              style: Theme.of(context).textTheme.titleLarge?.copyWith(
                fontWeight: FontWeight.bold,
                color: color,
              ),
            ),
            Text(
              title,
              style: Theme.of(context).textTheme.bodySmall,
              textAlign: TextAlign.center,
            ),
          ],
        ),
      ),
    );
  }

  Widget _buildFeedTypeItem(String name, String stock, String duration, Color color, int percentage) {
    return ListTile(
      leading: CircleAvatar(
        backgroundColor: color.withOpacity(0.1),
        child: Icon(Icons.grass, color: color),
      ),
      title: Text(name),
      subtitle: Column(
        crossAxisAlignment: CrossAxisAlignment.start,
        children: [
          Text('Stock: $stock'),
          const SizedBox(height: 4),
          LinearProgressIndicator(
            value: percentage / 100,
            backgroundColor: Colors.grey[300],
            valueColor: AlwaysStoppedAnimation<Color>(color),
          ),
        ],
      ),
      trailing: Text(
        duration,
        style: TextStyle(
          color: color,
          fontWeight: FontWeight.bold,
        ),
      ),
    );
  }

  Widget _buildDeliveryItem(String date, String product, String quantity, String supplier) {
    return ListTile(
      leading: const Icon(Icons.local_shipping, color: Colors.blue),
      title: Text(product),
      subtitle: Text('$quantity - $supplier'),
      trailing: Text(
        date,
        style: TextStyle(
          color: Colors.grey[600],
          fontSize: 12,
        ),
      ),
    );
  }

  Widget _buildPlanItem(String name, String quantity, String schedule, String status, Color statusColor) {
    return ListTile(
      leading: Icon(Icons.schedule, color: statusColor),
      title: Text(name),
      subtitle: Text('$quantity - $schedule'),
      trailing: Container(
        padding: const EdgeInsets.symmetric(horizontal: 8, vertical: 4),
        decoration: BoxDecoration(
          color: statusColor.withOpacity(0.1),
          borderRadius: BorderRadius.circular(12),
        ),
        child: Text(
          status,
          style: TextStyle(
            color: statusColor,
            fontSize: 12,
            fontWeight: FontWeight.w500,
          ),
        ),
      ),
    );
  }

  Widget _buildScheduleItem(String time, String title, String buildings, String quantity, Color color) {
    return ListTile(
      leading: Container(
        width: 50,
        height: 50,
        decoration: BoxDecoration(
          color: color.withOpacity(0.1),
          borderRadius: BorderRadius.circular(8),
        ),
        child: Center(
          child: Text(
            time,
            style: TextStyle(
              color: color,
              fontWeight: FontWeight.bold,
              fontSize: 12,
            ),
          ),
        ),
      ),
      title: Text(title),
      subtitle: Text('$buildings\n$quantity'),
      isThreeLine: true,
    );
  }

  Widget _buildConsumptionItem(String date, String quantity, String percentage, String status, Color statusColor) {
    return ListTile(
      leading: Icon(Icons.analytics, color: statusColor),
      title: Text(date),
      subtitle: Text('$quantity ($percentage du prévu)'),
      trailing: Container(
        padding: const EdgeInsets.symmetric(horizontal: 8, vertical: 4),
        decoration: BoxDecoration(
          color: statusColor.withOpacity(0.1),
          borderRadius: BorderRadius.circular(12),
        ),
        child: Text(
          status,
          style: TextStyle(
            color: statusColor,
            fontSize: 12,
            fontWeight: FontWeight.w500,
          ),
        ),
      ),
    );
  }

  void _showAddDialog() {
    showDialog(
      context: context,
      builder: (BuildContext context) {
        return AlertDialog(
          title: const Text('Ajouter'),
          content: Column(
            mainAxisSize: MainAxisSize.min,
            children: [
              ListTile(
                leading: const Icon(Icons.add_box, color: Colors.green),
                title: const Text('Nouveau stock'),
                onTap: () {
                  Navigator.pop(context);
                  _showAddStockDialog();
                },
              ),
              ListTile(
                leading: const Icon(Icons.schedule, color: Colors.blue),
                title: const Text('Nouveau plan'),
                onTap: () {
                  Navigator.pop(context);
                  _showAddPlanDialog();
                },
              ),
              ListTile(
                leading: const Icon(Icons.edit, color: Colors.orange),
                title: const Text('Saisie consommation'),
                onTap: () {
                  Navigator.pop(context);
                  _showAddConsumptionDialog();
                },
              ),
            ],
          ),
        );
      },
    );
  }

  void _showAddStockDialog() {
    // TODO: Implement add stock dialog
    ScaffoldMessenger.of(context).showSnackBar(
      const SnackBar(content: Text('Ajout de stock - À implémenter')),
    );
  }

  void _showAddPlanDialog() {
    // TODO: Implement add plan dialog
    ScaffoldMessenger.of(context).showSnackBar(
      const SnackBar(content: Text('Nouveau plan - À implémenter')),
    );
  }

  void _showAddConsumptionDialog() {
    // TODO: Implement add consumption dialog
    ScaffoldMessenger.of(context).showSnackBar(
      const SnackBar(content: Text('Saisie consommation - À implémenter')),
    );
  }
}
