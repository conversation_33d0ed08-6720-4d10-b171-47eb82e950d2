import 'package:flutter/material.dart';
import 'package:flutter_riverpod/flutter_riverpod.dart';
import 'package:intl/intl.dart';
import '../../models/prescription.dart';
import '../../models/product.dart';
import '../../providers/auth_provider.dart';
import '../../services/api_service.dart';
import '../../widgets/agricultural_app_bar.dart';
import '../../widgets/offline_banner.dart';

class PrescriptionScreen extends ConsumerStatefulWidget {
  final String? prescriptionId;
  final String? consultationId;
  final bool isNewPrescription;

  const PrescriptionScreen({
    Key? key,
    this.prescriptionId,
    this.consultationId,
    this.isNewPrescription = false,
  }) : super(key: key);

  @override
  ConsumerState<PrescriptionScreen> createState() => _PrescriptionScreenState();
}

class _PrescriptionScreenState extends ConsumerState<PrescriptionScreen> {
  bool isLoading = true;
  bool isOffline = false;
  bool isSaving = false;
  
  late Prescription prescription;
  List<Product> availableProducts = [];
  
  final _formKey = GlobalKey<FormState>();
  final _instructionsController = TextEditingController();
  final _notesController = TextEditingController();
  
  @override
  void initState() {
    super.initState();
    _loadData();
  }
  
  @override
  void dispose() {
    _instructionsController.dispose();
    _notesController.dispose();
    super.dispose();
  }
  
  Future<void> _loadData() async {
    setState(() {
      isLoading = true;
    });
    
    try {
      final apiService = ref.read(apiServiceProvider);
      
      // Charger les produits disponibles
      final userId = ref.read(currentUserProvider)?.id;
      if (userId != null) {
        availableProducts = await apiService.getVeterinaryProducts(userId);
      }
      
      // Si c'est une nouvelle prescription
      if (widget.isNewPrescription) {
        prescription = Prescription(
          id: 'new_${DateTime.now().millisecondsSinceEpoch}',
          veterinarianId: userId ?? '',
          consultationId: widget.consultationId ?? '',
          date: DateTime.now(),
          instructions: '',
          notes: '',
          status: 'active',
          items: [],
        );
      } 
      // Sinon, charger la prescription existante
      else if (widget.prescriptionId != null) {
        prescription = await apiService.getPrescription(widget.prescriptionId!);
      } 
      // Fallback si aucun ID n'est fourni
      else {
        prescription = Prescription.empty();
      }
      
      // Initialiser les contrôleurs avec les données
      _instructionsController.text = prescription.instructions;
      _notesController.text = prescription.notes;
      
      setState(() {
        isLoading = false;
      });
    } catch (e) {
      print('Erreur lors du chargement des données: $e');
      setState(() {
        isLoading = false;
        isOffline = true;
        // Charger des données fictives en mode hors ligne
        _loadMockData();
      });
    }
  }
  
  void _loadMockData() {
    // Charger des produits fictifs
    availableProducts = [
      Product(
        id: 'p1',
        name: 'Avian Protect Plus',
        description: 'Antibiotique à large spectre pour volailles',
        category: 'antibiotique',
        price: 1250.0,
        stockQuantity: 45,
        unit: 'flacon',
        imageUrl: 'assets/images/products/antibiotique1.jpg',
        expiryDate: DateTime.now().add(Duration(days: 365)),
        metadata: {
          'dosage': '5ml/L d\'eau',
          'active_ingredient': 'Enrofloxacine',
          'withdrawal_period': '7 jours',
        },
      ),
      Product(
        id: 'p2',
        name: 'ImmunoBoost Avian',
        description: 'Complément vitaminé pour renforcer l\'immunité',
        category: 'vitamine',
        price: 850.0,
        stockQuantity: 28,
        unit: 'sachet',
        imageUrl: 'assets/images/products/vitamine1.jpg',
        expiryDate: DateTime.now().add(Duration(days: 180)),
        metadata: {
          'dosage': '1g/L d\'eau pendant 5 jours',
          'active_ingredient': 'Complexe vitamines A, D3, E, C',
          'withdrawal_period': '0 jour',
        },
      ),
      Product(
        id: 'p3',
        name: 'CoccidioClear',
        description: 'Traitement anticoccidien pour volailles',
        category: 'antiparasitaire',
        price: 1800.0,
        stockQuantity: 12,
        unit: 'flacon',
        imageUrl: 'assets/images/products/antiparasitaire1.jpg',
        expiryDate: DateTime.now().add(Duration(days: 240)),
        metadata: {
          'dosage': '1ml/L d\'eau pendant 3 jours',
          'active_ingredient': 'Amprolium',
          'withdrawal_period': '5 jours',
        },
      ),
    ];
    
    // Si c'est une nouvelle prescription
    if (widget.isNewPrescription) {
      final userId = ref.read(currentUserProvider)?.id ?? 'vet123';
      prescription = Prescription(
        id: 'new_${DateTime.now().millisecondsSinceEpoch}',
        veterinarianId: userId,
        consultationId: widget.consultationId ?? 'cons123',
        date: DateTime.now(),
        instructions: '',
        notes: '',
        status: 'active',
        items: [],
      );
    } 
    // Sinon, créer une prescription fictive
    else {
      prescription = Prescription(
        id: widget.prescriptionId ?? 'presc123',
        veterinarianId: 'vet123',
        consultationId: 'cons123',
        date: DateTime.now(),
        instructions: 'Administrer les médicaments selon le dosage indiqué pendant 5 jours.',
        notes: 'Surveiller la consommation d\'eau et l\'état général des animaux.',
        status: 'active',
        items: [
          PrescriptionItem(
            id: 'item1',
            productId: 'p1',
            productName: 'Avian Protect Plus',
            dosage: '5ml/L d\'eau',
            frequency: '2 fois par jour',
            duration: '5 jours',
            quantity: 2,
            notes: 'Administrer dans l\'eau de boisson',
          ),
          PrescriptionItem(
            id: 'item2',
            productId: 'p2',
            productName: 'ImmunoBoost Avian',
            dosage: '1g/L d\'eau',
            frequency: '1 fois par jour',
            duration: '7 jours',
            quantity: 1,
            notes: 'Commencer après l\'antibiotique',
          ),
        ],
      );
    }
    
    // Initialiser les contrôleurs avec les données
    _instructionsController.text = prescription.instructions;
    _notesController.text = prescription.notes;
  }
  
  Future<void> _savePrescription() async {
    if (!_formKey.currentState!.validate()) {
      return;
    }
    
    setState(() {
      isSaving = true;
    });
    
    try {
      // Mettre à jour les données de la prescription
      prescription = prescription.copyWith(
        instructions: _instructionsController.text,
        notes: _notesController.text,
      );
      
      final apiService = ref.read(apiServiceProvider);
      
      // Si c'est une nouvelle prescription
      if (widget.isNewPrescription) {
        final newPrescription = await apiService.createPrescription(prescription);
        prescription = newPrescription;
      } 
      // Sinon, mettre à jour la prescription existante
      else {
        await apiService.updatePrescription(prescription);
      }
      
      setState(() {
        isSaving = false;
      });
      
      ScaffoldMessenger.of(context).showSnackBar(
        SnackBar(content: Text('Prescription enregistrée avec succès')),
      );
      
      // Retourner à l'écran précédent
      Navigator.pop(context, true);
    } catch (e) {
      print('Erreur lors de l\'enregistrement de la prescription: $e');
      setState(() {
        isSaving = false;
      });
      
      // En mode hors ligne, simuler un enregistrement réussi
      if (isOffline) {
        ScaffoldMessenger.of(context).showSnackBar(
          SnackBar(
            content: Text('Prescription enregistrée localement (mode hors ligne)'),
            backgroundColor: Colors.orange,
          ),
        );
        Navigator.pop(context, true);
      } else {
        ScaffoldMessenger.of(context).showSnackBar(
          SnackBar(
            content: Text('Erreur lors de l\'enregistrement. Réessayez plus tard.'),
            backgroundColor: Colors.red,
          ),
        );
      }
    }
  }
  
  void _showAddItemDialog() {
    Product? selectedProduct;
    String dosage = '';
    String frequency = '';
    String duration = '';
    int quantity = 1;
    String notes = '';
    
    showDialog(
      context: context,
      builder: (context) => StatefulBuilder(
        builder: (context, setState) => AlertDialog(
          title: Text('Ajouter un médicament'),
          content: SingleChildScrollView(
            child: Column(
              mainAxisSize: MainAxisSize.min,
              crossAxisAlignment: CrossAxisAlignment.start,
              children: [
                DropdownButtonFormField<Product>(
                  decoration: InputDecoration(
                    labelText: 'Produit',
                    border: OutlineInputBorder(),
                  ),
                  value: selectedProduct,
                  items: availableProducts.map((product) {
                    return DropdownMenuItem<Product>(
                      value: product,
                      child: Text(product.name),
                    );
                  }).toList(),
                  onChanged: (value) {
                    setState(() {
                      selectedProduct = value;
                      // Pré-remplir le dosage si disponible
                      if (value != null && value.metadata.containsKey('dosage')) {
                        dosage = value.metadata['dosage'] as String;
                      }
                    });
                  },
                ),
                SizedBox(height: 16),
                TextField(
                  decoration: InputDecoration(
                    labelText: 'Dosage',
                    hintText: 'Ex: 5ml/L d\'eau',
                    border: OutlineInputBorder(),
                  ),
                  onChanged: (value) => dosage = value,
                  controller: TextEditingController(text: dosage),
                ),
                SizedBox(height: 16),
                TextField(
                  decoration: InputDecoration(
                    labelText: 'Fréquence',
                    hintText: 'Ex: 2 fois par jour',
                    border: OutlineInputBorder(),
                  ),
                  onChanged: (value) => frequency = value,
                ),
                SizedBox(height: 16),
                TextField(
                  decoration: InputDecoration(
                    labelText: 'Durée',
                    hintText: 'Ex: 5 jours',
                    border: OutlineInputBorder(),
                  ),
                  onChanged: (value) => duration = value,
                ),
                SizedBox(height: 16),
                Row(
                  children: [
                    Expanded(
                      child: TextField(
                        decoration: InputDecoration(
                          labelText: 'Quantité',
                          border: OutlineInputBorder(),
                        ),
                        keyboardType: TextInputType.number,
                        onChanged: (value) => quantity = int.tryParse(value) ?? 1,
                        controller: TextEditingController(text: quantity.toString()),
                      ),
                    ),
                    if (selectedProduct != null)
                      Padding(
                        padding: EdgeInsets.only(left: 8),
                        child: Text(
                          selectedProduct.unit,
                          style: TextStyle(fontWeight: FontWeight.bold),
                        ),
                      ),
                  ],
                ),
                SizedBox(height: 16),
                TextField(
                  decoration: InputDecoration(
                    labelText: 'Notes',
                    hintText: 'Instructions supplémentaires',
                    border: OutlineInputBorder(),
                  ),
                  maxLines: 2,
                  onChanged: (value) => notes = value,
                ),
              ],
            ),
          ),
          actions: [
            TextButton(
              onPressed: () => Navigator.pop(context),
              child: Text('Annuler'),
            ),
            ElevatedButton(
              onPressed: () {
                if (selectedProduct == null) {
                  ScaffoldMessenger.of(context).showSnackBar(
                    SnackBar(
                      content: Text('Veuillez sélectionner un produit'),
                      backgroundColor: Colors.red,
                    ),
                  );
                  return;
                }
                
                if (dosage.isEmpty || frequency.isEmpty || duration.isEmpty) {
                  ScaffoldMessenger.of(context).showSnackBar(
                    SnackBar(
                      content: Text('Veuillez remplir tous les champs obligatoires'),
                      backgroundColor: Colors.red,
                    ),
                  );
                  return;
                }
                
                // Ajouter l'item à la prescription
                final newItem = PrescriptionItem(
                  id: 'item_${DateTime.now().millisecondsSinceEpoch}',
                  productId: selectedProduct.id,
                  productName: selectedProduct.name,
                  dosage: dosage,
                  frequency: frequency,
                  duration: duration,
                  quantity: quantity,
                  notes: notes,
                );
                
                setState(() {
                  prescription = prescription.copyWith(
                    items: [...prescription.items, newItem],
                  );
                });
                
                Navigator.pop(context);
              },
              child: Text('Ajouter'),
              style: ElevatedButton.styleFrom(
                backgroundColor: Colors.green[700],
                foregroundColor: Colors.white,
              ),
            ),
          ],
        ),
      ),
    );
  }
  
  void _removeItem(String itemId) {
    setState(() {
      prescription = prescription.copyWith(
        items: prescription.items.where((item) => item.id != itemId).toList(),
      );
    });
  }
  
  @override
  Widget build(BuildContext context) {
    return Scaffold(
      backgroundColor: Colors.grey[50],
      appBar: AgricultureAppBar(
        title: widget.isNewPrescription ? 'Nouvelle Prescription' : 'Modifier Prescription',
        actions: [
          if (!isLoading)
            IconButton(
              icon: Icon(Icons.save),
              onPressed: isSaving ? null : _savePrescription,
            ),
        ],
      ),
      body: isLoading
        ? Center(child: CircularProgressIndicator())
        : Column(
            children: [
              if (isOffline) OfflineBanner(),
              Expanded(
                child: Form(
                  key: _formKey,
                  child: ListView(
                    padding: EdgeInsets.all(16),
                    children: [
                      _buildPrescriptionHeader(),
                      SizedBox(height: 24),
                      _buildInstructionsSection(),
                      SizedBox(height: 24),
                      _buildItemsSection(),
                      SizedBox(height: 24),
                      _buildNotesSection(),
                      SizedBox(height: 100), // Espace pour le bouton flottant
                    ],
                  ),
                ),
              ),
            ],
          ),
      floatingActionButton: !isLoading
        ? FloatingActionButton.extended(
            onPressed: _showAddItemDialog,
            icon: Icon(Icons.add),
            label: Text('Ajouter un médicament'),
            backgroundColor: Colors.green[700],
          )
        : null,
    );
  }
  
  Widget _buildPrescriptionHeader() {
    return Card(
      elevation: 2,
      shape: RoundedRectangleBorder(borderRadius: BorderRadius.circular(12)),
      child: Padding(
        padding: EdgeInsets.all(16),
        child: Column(
          crossAxisAlignment: CrossAxisAlignment.start,
          children: [
            Row(
              mainAxisAlignment: MainAxisAlignment.spaceBetween,
              children: [
                Text(
                  'Prescription #${prescription.id.substring(0, 8)}',
                  style: TextStyle(
                    fontWeight: FontWeight.bold,
                    fontSize: 18,
                  ),
                ),
                _buildStatusChip(prescription.status),
              ],
            ),
            SizedBox(height: 8),
            Text(
              'Date: ${DateFormat('dd/MM/yyyy').format(prescription.date)}',
              style: TextStyle(color: Colors.grey[700]),
            ),
            if (prescription.consultationId.isNotEmpty)
              Padding(
                padding: EdgeInsets.only(top: 4),
                child: Text(
                  'Consultation: #${prescription.consultationId.substring(0, 8)}',
                  style: TextStyle(color: Colors.grey[700]),
                ),
              ),
          ],
        ),
      ),
    );
  }
  
  Widget _buildInstructionsSection() {
    return Column(
      crossAxisAlignment: CrossAxisAlignment.start,
      children: [
        Text(
          'Instructions générales',
          style: TextStyle(
            fontWeight: FontWeight.bold,
            fontSize: 16,
          ),
        ),
        SizedBox(height: 8),
        TextFormField(
          controller: _instructionsController,
          decoration: InputDecoration(
            hintText: 'Instructions pour l\'administration des médicaments',
            border: OutlineInputBorder(borderRadius: BorderRadius.circular(12)),
            filled: true,
            fillColor: Colors.white,
          ),
          maxLines: 3,
          validator: (value) {
            if (value == null || value.isEmpty) {
              return 'Veuillez saisir des instructions';
            }
            return null;
          },
        ),
      ],
    );
  }
  
  Widget _buildItemsSection() {
    return Column(
      crossAxisAlignment: CrossAxisAlignment.start,
      children: [
        Row(
          mainAxisAlignment: MainAxisAlignment.spaceBetween,
          children: [
            Text(
              'Médicaments prescrits',
              style: TextStyle(
                fontWeight: FontWeight.bold,
                fontSize: 16,
              ),
            ),
            Text(
              '${prescription.items.length} produit${prescription.items.length > 1 ? "s" : ""}',
              style: TextStyle(
                color: Colors.grey[700],
              ),
            ),
          ],
        ),
        SizedBox(height: 8),
        prescription.items.isEmpty
          ? _buildEmptyItemsState()
          : Column(
              children: prescription.items.map((item) => _buildItemCard(item)).toList(),
            ),
      ],
    );
  }
  
  Widget _buildEmptyItemsState() {
    return Container(
      height: 120,
      decoration: BoxDecoration(
        color: Colors.white,
        borderRadius: BorderRadius.circular(12),
        border: Border.all(color: Colors.grey[300]!),
      ),
      child: Center(
        child: Column(
          mainAxisAlignment: MainAxisAlignment.center,
          children: [
            Icon(
              Icons.medication_outlined,
              size: 40,
              color: Colors.grey[400],
            ),
            SizedBox(height: 8),
            Text(
              'Aucun médicament ajouté',
              style: TextStyle(
                color: Colors.grey[600],
              ),
            ),
            SizedBox(height: 4),
            Text(
              'Appuyez sur le bouton + pour ajouter',
              style: TextStyle(
                color: Colors.grey[500],
                fontSize: 12,
              ),
            ),
          ],
        ),
      ),
    );
  }
  
  Widget _buildItemCard(PrescriptionItem item) {
    // Trouver le produit correspondant pour afficher plus d'informations
    final product = availableProducts.firstWhere(
      (p) => p.id == item.productId,
      orElse: () => Product.empty(),
    );
    
    final hasWithdrawalPeriod = product.metadata.containsKey('withdrawal_period');
    final withdrawalPeriod = hasWithdrawalPeriod
        ? product.metadata['withdrawal_period'] as String
        : '';
    
    return Card(
      margin: EdgeInsets.only(bottom: 12),
      elevation: 1,
      shape: RoundedRectangleBorder(borderRadius: BorderRadius.circular(12)),
      child: Padding(
        padding: EdgeInsets.all(16),
        child: Column(
          crossAxisAlignment: CrossAxisAlignment.start,
          children: [
            Row(
              crossAxisAlignment: CrossAxisAlignment.start,
              children: [
                Expanded(
                  child: Column(
                    crossAxisAlignment: CrossAxisAlignment.start,
                    children: [
                      Text(
                        item.productName,
                        style: TextStyle(
                          fontWeight: FontWeight.bold,
                          fontSize: 16,
                        ),
                      ),
                      if (product.category.isNotEmpty)
                        Padding(
                          padding: EdgeInsets.only(top: 4),
                          child: Text(
                            _getCategoryLabel(product.category),
                            style: TextStyle(
                              color: _getCategoryColor(product.category),
                              fontSize: 12,
                              fontWeight: FontWeight.w500,
                            ),
                          ),
                        ),
                    ],
                  ),
                ),
                IconButton(
                  icon: Icon(Icons.delete_outline, color: Colors.red[700]),
                  onPressed: () => _removeItem(item.id),
                  tooltip: 'Supprimer',
                ),
              ],
            ),
            SizedBox(height: 12),
            _buildItemDetail('Dosage', item.dosage),
            _buildItemDetail('Fréquence', item.frequency),
            _buildItemDetail('Durée', item.duration),
            _buildItemDetail('Quantité', '${item.quantity} ${product.unit}${item.quantity > 1 ? "s" : ""}'),
            if (hasWithdrawalPeriod)
              _buildItemDetail('Délai d\'attente', withdrawalPeriod, isWarning: true),
            if (item.notes.isNotEmpty)
              Padding(
                padding: EdgeInsets.only(top: 8),
                child: Text(
                  'Notes: ${item.notes}',
                  style: TextStyle(
                    fontStyle: FontStyle.italic,
                    color: Colors.grey[700],
                  ),
                ),
              ),
          ],
        ),
      ),
    );
  }
  
  Widget _buildItemDetail(String label, String value, {bool isWarning = false}) {
    return Padding(
      padding: EdgeInsets.only(bottom: 4),
      child: Row(
        crossAxisAlignment: CrossAxisAlignment.start,
        children: [
          SizedBox(width: 100, child: Text('$label:', style: TextStyle(fontWeight: FontWeight.w500))),
          Expanded(
            child: Text(
              value,
              style: TextStyle(
                color: isWarning ? Colors.orange[700] : Colors.black87,
                fontWeight: isWarning ? FontWeight.bold : FontWeight.normal,
              ),
            ),
          ),
        ],
      ),
    );
  }
  
  Widget _buildNotesSection() {
    return Column(
      crossAxisAlignment: CrossAxisAlignment.start,
      children: [
        Text(
          'Notes supplémentaires',
          style: TextStyle(
            fontWeight: FontWeight.bold,
            fontSize: 16,
          ),
        ),
        SizedBox(height: 8),
        TextFormField(
          controller: _notesController,
          decoration: InputDecoration(
            hintText: 'Notes ou recommandations supplémentaires',
            border: OutlineInputBorder(borderRadius: BorderRadius.circular(12)),
            filled: true,
            fillColor: Colors.white,
          ),
          maxLines: 3,
        ),
      ],
    );
  }
  
  Widget _buildStatusChip(String status) {
    String label;
    Color color;
    
    switch (status) {
      case 'active':
        label = 'Active';
        color = Colors.green[700]!;
        break;
      case 'completed':
        label = 'Terminée';
        color = Colors.blue[700]!;
        break;
      case 'cancelled':
        label = 'Annulée';
        color = Colors.red[700]!;
        break;
      default:
        label = 'En attente';
        color = Colors.orange[700]!;
    }
    
    return Container(
      padding: EdgeInsets.symmetric(horizontal: 12, vertical: 6),
      decoration: BoxDecoration(
        color: color.withOpacity(0.1),
        borderRadius: BorderRadius.circular(16),
        border: Border.all(color: color.withOpacity(0.5)),
      ),
      child: Text(
        label,
        style: TextStyle(
          color: color,
          fontWeight: FontWeight.bold,
          fontSize: 12,
        ),
      ),
    );
  }
  
  Color _getCategoryColor(String category) {
    switch (category) {
      case 'antibiotique':
        return Colors.blue[700]!;
      case 'antiparasitaire':
        return Colors.purple[700]!;
      case 'vitamine':
        return Colors.orange[700]!;
      case 'probiotique':
        return Colors.green[700]!;
      case 'minéral':
        return Colors.cyan[700]!;
      case 'désinfectant':
        return Colors.red[700]!;
      default:
        return Colors.grey[700]!;
    }
  }
  
  String _getCategoryLabel(String category) {
    switch (category) {
      case 'antibiotique':
        return 'Antibiotique';
      case 'antiparasitaire':
        return 'Antiparasitaire';
      case 'vitamine':
        return 'Vitamine';
      case 'probiotique':
        return 'Probiotique';
      case 'minéral':
        return 'Minéral';
      case 'désinfectant':
        return 'Désinfectant';
      default:
        return 'Autre';
    }
  }
}