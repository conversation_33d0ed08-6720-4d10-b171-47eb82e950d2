import { useState, useEffect } from 'react';
import {
  Box,
  Typography,
  Paper,
  TextField,
  Button,
  CircularProgress,
  Alert,
  Grid,
  FormControlLabel,
  Checkbox,
  Tooltip,
  IconButton,
  Snackbar
} from '@mui/material';
import {
  Save as SaveIcon,
  Visibility,
  VisibilityOff,
  Email as EmailIcon,
  Cable as TestConnectionIcon
} from '@mui/icons-material';
import settingsService from '../../services/settingsService';
import { useLanguage } from '../../contexts/LanguageContext';

const SmtpConfig = () => {
  const { t } = useLanguage();

  const [smtpSettings, setSmtpSettings] = useState({
    host: '',
    port: 587,
    secure: false,
    user: '',
    pass: '',
    fromName: '',
    fromEmail: '',
    replyTo: '',
    testEmailRecipient: '',
    isEnabled: true
  });

  const [loading, setLoading] = useState(true);
  const [saving, setSaving] = useState(false);
  const [testing, setTesting] = useState(false);
  const [showPassword, setShowPassword] = useState(false);
  const [error, setError] = useState(null);
  const [success, setSuccess] = useState(null);
  const [testResult, setTestResult] = useState(null);
  const [snackbarOpen, setSnackbarOpen] = useState(false);

  useEffect(() => {
    fetchSettings();
  }, []);

  const fetchSettings = async () => {
    try {
      setLoading(true);
      setError(null);
      const data = await settingsService.fetchSmtpSettings();
      setSmtpSettings(data || {
        host: '',
        port: 587,
        secure: false,
        user: '',
        pass: '',
        fromName: '',
        fromEmail: '',
        replyTo: '',
        testEmailRecipient: '',
        isEnabled: true
      });
    } catch (err) {
      console.error("Error fetching SMTP settings:", err);
      setError(t('smtp.errors.fetchFailed'));
    } finally {
      setLoading(false);
    }
  };

  const handleChange = (e) => {
    const { name, value, type, checked } = e.target;
    setSmtpSettings(prev => ({
      ...prev,
      [name]: type === 'checkbox' ? checked :
              name === 'port' ? parseInt(value, 10) || '' : value
    }));
  };

  const handleSave = async (e) => {
    e.preventDefault();
    try {
      setSaving(true);
      setError(null);
      setSuccess(null);
      await settingsService.updateSmtpSettings(smtpSettings);
      setSuccess(t('smtp.success.saveSuccess'));
      setSnackbarOpen(true);
    } catch (err) {
      console.error("Error saving SMTP settings:", err);
      setError(t('smtp.errors.saveFailed'));
    } finally {
      setSaving(false);
    }
  };

  const handleTestConnection = async () => {
    try {
      setTesting(true);
      setError(null);
      setTestResult(null);
      const result = await settingsService.testSmtpConnection(smtpSettings);
      setTestResult({
        success: true,
        message: t('smtp.success.testSuccess', { recipient: result.recipient || smtpSettings.testEmailRecipient || t('smtp.defaultTestEmail') })
      });
      setSnackbarOpen(true);
    } catch (err) {
      console.error("Error testing SMTP connection:", err);
      setTestResult({
        success: false,
        message: t('smtp.errors.testFailed')
      });
    } finally {
      setTesting(false);
    }
  };

  const toggleShowPassword = () => {
    setShowPassword(!showPassword);
  };

  const handleCloseSnackbar = () => {
    setSnackbarOpen(false);
  };

  if (loading) {
    return (
      <Box sx={{ display: 'flex', justifyContent: 'center', alignItems: 'center', height: '50vh' }}>
        <CircularProgress />
      </Box>
    );
  }

  return (
    <Box sx={{ p: 3 }}>
      <Typography variant="h4" gutterBottom>
        {t('smtp.title')}
      </Typography>
      <Typography variant="body1" paragraph>
        {t('smtp.description')}
      </Typography>

      {error && (
        <Alert severity="error" sx={{ mb: 2 }}>
          {error}
        </Alert>
      )}

      {success && (
        <Alert severity="success" sx={{ mb: 2 }}>
          {success}
        </Alert>
      )}

      {testResult && (
        <Alert
          severity={testResult.success ? "success" : "error"}
          sx={{ mb: 2 }}
        >
          {testResult.message}
        </Alert>
      )}

      <Paper sx={{ p: 3, mb: 3 }}>
        <form onSubmit={handleSave}>
          <Grid container spacing={3}>
            <Grid item xs={12} md={6}>
              <TextField
                fullWidth
                label={t('smtp.fields.host')}
                name="host"
                value={smtpSettings.host}
                onChange={handleChange}
                required
                margin="normal"
                helperText={t('smtp.helpText.host')}
              />
            </Grid>
            <Grid item xs={12} md={3}>
              <TextField
                fullWidth
                label={t('smtp.fields.port')}
                name="port"
                type="number"
                value={smtpSettings.port}
                onChange={handleChange}
                required
                margin="normal"
                helperText={t('smtp.helpText.port')}
              />
            </Grid>
            <Grid item xs={12} md={3}>
              <FormControlLabel
                control={
                  <Checkbox
                    name="secure"
                    checked={smtpSettings.secure}
                    onChange={handleChange}
                  />
                }
                label={t('smtp.fields.secure')}
                sx={{ mt: 2 }}
              />
              <Typography variant="caption" display="block" color="text.secondary">
                {t('smtp.helpText.secure')}
              </Typography>
            </Grid>

            <Grid item xs={12} md={6}>
              <TextField
                fullWidth
                label={t('smtp.fields.user')}
                name="user"
                value={smtpSettings.user}
                onChange={handleChange}
                required
                margin="normal"
                helperText={t('smtp.helpText.user')}
              />
            </Grid>
            <Grid item xs={12} md={6}>
              <TextField
                fullWidth
                label={t('smtp.fields.pass')}
                name="pass"
                type={showPassword ? 'text' : 'password'}
                value={smtpSettings.pass}
                onChange={handleChange}
                required
                margin="normal"
                helperText={t('smtp.helpText.pass')}
                InputProps={{
                  endAdornment: (
                    <IconButton onClick={toggleShowPassword} edge="end">
                      {showPassword ? <VisibilityOff /> : <Visibility />}
                    </IconButton>
                  ),
                }}
              />
            </Grid>

            <Grid item xs={12} md={6}>
              <TextField
                fullWidth
                label={t('smtp.fields.fromName')}
                name="fromName"
                value={smtpSettings.fromName}
                onChange={handleChange}
                margin="normal"
                helperText={t('smtp.helpText.fromName')}
              />
            </Grid>
            <Grid item xs={12} md={6}>
              <TextField
                fullWidth
                label={t('smtp.fields.fromEmail')}
                name="fromEmail"
                type="email"
                value={smtpSettings.fromEmail}
                onChange={handleChange}
                required
                margin="normal"
                helperText={t('smtp.helpText.fromEmail')}
              />
            </Grid>

            <Grid item xs={12} md={6}>
              <TextField
                fullWidth
                label={t('smtp.fields.replyTo')}
                name="replyTo"
                type="email"
                value={smtpSettings.replyTo}
                onChange={handleChange}
                margin="normal"
                helperText={t('smtp.helpText.replyTo')}
              />
            </Grid>
            <Grid item xs={12} md={6}>
              <TextField
                fullWidth
                label={t('smtp.fields.testEmailRecipient')}
                name="testEmailRecipient"
                type="email"
                value={smtpSettings.testEmailRecipient}
                onChange={handleChange}
                margin="normal"
                helperText={t('smtp.helpText.testEmailRecipient')}
              />
            </Grid>

            <Grid item xs={12}>
              <FormControlLabel
                control={
                  <Checkbox
                    name="isEnabled"
                    checked={smtpSettings.isEnabled}
                    onChange={handleChange}
                  />
                }
                label={t('smtp.fields.isEnabled')}
              />
              <Typography variant="caption" display="block" color="text.secondary">
                {t('smtp.helpText.isEnabled')}
              </Typography>
            </Grid>

            <Grid item xs={12}>
              <Box sx={{ display: 'flex', gap: 2, mt: 2 }}>
                <Button
                  variant="contained"
                  color="primary"
                  startIcon={<SaveIcon />}
                  type="submit"
                  disabled={saving}
                >
                  {saving ? t('common.saving') : t('common.save')}
                  {saving && <CircularProgress size={24} sx={{ ml: 1 }} />}
                </Button>
                <Button
                  variant="outlined"
                  color="secondary"
                  startIcon={<TestConnectionIcon />}
                  onClick={handleTestConnection}
                  disabled={testing || !smtpSettings.host || !smtpSettings.user || !smtpSettings.pass}
                >
                  {testing ? t('smtp.testing') : t('smtp.testConnection')}
                  {testing && <CircularProgress size={24} sx={{ ml: 1 }} />}
                </Button>
                <Button
                  variant="outlined"
                  startIcon={<EmailIcon />}
                  onClick={() => window.open(`mailto:${smtpSettings.fromEmail}`)}
                  disabled={!smtpSettings.fromEmail}
                >
                  {t('smtp.testFromEmail')}
                </Button>
              </Box>
            </Grid>
          </Grid>
        </form>
      </Paper>

      <Snackbar
        open={snackbarOpen}
        autoHideDuration={6000}
        onClose={handleCloseSnackbar}
        message={success || (testResult && testResult.message)}
      />
    </Box>
  );
};

export default SmtpConfig;
