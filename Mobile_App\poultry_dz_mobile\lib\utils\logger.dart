import 'package:flutter/foundation.dart';
import 'package:flutter/foundation.dart';
import 'package:logger/logger.dart';

/// Service de logging pour l'application Poultry DZ
class AppLogger {
  static final Logger _logger = Logger(
    printer: PrettyPrinter(
      methodCount: 2,
      errorMethodCount: 8,
      lineLength: 120,
      colors: true,
      printEmojis: true,
      printTime: true,
    ),
  );

  /// Log de débogage
  static void d(String message, [dynamic error, StackTrace? stackTrace]) {
    if (kDebugMode) {
      _logger.d(message, error: error, stackTrace: stackTrace);
    }
  }

  /// Log d'information
  static void i(String message, [dynamic error, StackTrace? stackTrace]) {
    _logger.i(message, error: error, stackTrace: stackTrace);
  }

  /// Log d'avertissement
  static void w(String message, [dynamic error, StackTrace? stackTrace]) {
    _logger.w(message, error: error, stackTrace: stackTrace);
  }

  /// Log d'erreur
  static void e(String message, [dynamic error, StackTrace? stackTrace]) {
    _logger.e(message, error: error, stackTrace: stackTrace);
  }

  /// Log fatal
  static void f(String message, [dynamic error, StackTrace? stackTrace]) {
    _logger.f(message, error: error, stackTrace: stackTrace);
  }

  /// Log de trace
  static void t(String message, [dynamic error, StackTrace? stackTrace]) {
    _logger.t(message, error: error, stackTrace: stackTrace);
  }

  // Alias methods for compatibility
  /// Log d'erreur (alias pour e)
  static void error(String message, [dynamic error, StackTrace? stackTrace]) {
    e(message, error, stackTrace);
  }

  /// Log d'information (alias pour i)
  static void info(String message, [dynamic error, StackTrace? stackTrace]) {
    i(message, error, stackTrace);
  }

  /// Log d'avertissement (alias pour w)
  static void warning(String message, [dynamic error, StackTrace? stackTrace]) {
    w(message, error, stackTrace);
  }

  /// Log de débogage (alias pour d)
  static void debug(String message, [dynamic error, StackTrace? stackTrace]) {
    d(message, error, stackTrace);
  }
}

/// Alias pour faciliter l'utilisation
typedef Logger = AppLogger;