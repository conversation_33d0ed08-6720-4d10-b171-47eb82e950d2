{"version": 3, "file": "ApiConfig.CQDJF2D9.js", "sources": ["../../src/pages/admin/ApiConfig.jsx"], "sourcesContent": ["import React, { useState, useEffect } from 'react';\r\nimport {\r\n  Box,\r\n  Typography,\r\n  Paper,\r\n  TextField,\r\n  Button,\r\n  CircularProgress,\r\n  Alert,\r\n  Grid,\r\n  IconButton,\r\n  Tooltip,\r\n} from '@mui/material';\r\nimport { Save as SaveIcon, Visibility, VisibilityOff } from '@mui/icons-material';\r\nimport axiosInstance from '../../utils/axiosConfig';\r\n\r\nconst ApiConfig = () => {\r\n  const [apiKeys, setApiKeys] = useState({});\r\n  const [loading, setLoading] = useState(true);\r\n  const [error, setError] = useState('');\r\n  const [success, setSuccess] = useState('');\r\n  const [showKeys, setShowKeys] = useState({});\r\n\r\n  useEffect(() => {\r\n    const fetchApiKeys = async () => {\r\n      try {\r\n        setLoading(true);\r\n        // Corriger le chemin en supprimant le préfixe '/api' qui est déjà dans baseURL\r\n        const response = await axiosInstance.get('/admin/settings/api-keys');\r\n        setApiKeys(response.data || {});\r\n        setError('');\r\n      } catch (err) {\r\n        setError(err.response?.data?.message || \"Erreur lors de la récupération des clés API.\");\r\n        console.error(\"Erreur fetchApiKeys:\", err);\r\n      } finally {\r\n        setLoading(false);\r\n      }\r\n    };\r\n    fetchApiKeys();\r\n  }, []);\r\n\r\n  const handleChange = (service, value) => {\r\n    setApiKeys(prev => ({ ...prev, [service]: value }));\r\n  };\r\n\r\n  const toggleShowKey = (service) => {\r\n    setShowKeys(prev => ({ ...prev, [service]: !prev[service] }));\r\n  };\r\n\r\n  const handleSubmit = async (e) => {\r\n    e.preventDefault();\r\n    setLoading(true);\r\n    setError('');\r\n    setSuccess('');\r\n    try {\r\n      // Log what we're sending to the server\r\n      console.log(\"Saving API keys:\", { apiKeys });\r\n\r\n      // Corriger le chemin en supprimant le préfixe '/api' qui est déjà dans baseURL\r\n      const response = await axiosInstance.post('/admin/settings/api-keys', { apiKeys });\r\n      console.log(\"Server response:\", response.data);\r\n      setSuccess('Clés API sauvegardées avec succès !');\r\n    } catch (err) {\r\n      console.error(\"Erreur handleSubmit détaillée:\", {\r\n        message: err.message,\r\n        response: err.response?.data,\r\n        status: err.response?.status\r\n      });\r\n      setError(err.response?.data?.message || err.response?.data?.error || \"Erreur lors de la sauvegarde des clés API.\");\r\n      console.error(\"Erreur handleSubmit:\", err);\r\n    } finally {\r\n      setLoading(false);\r\n    }\r\n  };\r\n\r\n  // Map friendly names for API services\r\n  const serviceLabels = {\r\n    openai: 'OpenAI API Key',\r\n    google: 'Google AI API Key',\r\n    azure_openai: 'Azure OpenAI API Key',\r\n    claude: 'Anthropic Claude API Key',\r\n    gemini: 'Google Gemini API Key'\r\n  };\r\n\r\n  const renderApiKeyField = (service, label) => (\r\n    <Grid item xs={12} key={service}>\r\n      <TextField\r\n        fullWidth\r\n        label={label || serviceLabels[service] || `${service} API Key`}\r\n        variant=\"outlined\"\r\n        type={showKeys[service] ? 'text' : 'password'}\r\n        value={apiKeys[service] || ''}\r\n        onChange={(e) => handleChange(service, e.target.value)}\r\n        InputProps={{\r\n          endAdornment: (\r\n            <Tooltip title={showKeys[service] ? \"Cacher la clé\" : \"Afficher la clé\"}>\r\n              <IconButton onClick={() => toggleShowKey(service)} edge=\"end\">\r\n                {showKeys[service] ? <VisibilityOff /> : <Visibility />}\r\n              </IconButton>\r\n            </Tooltip>\r\n          ),\r\n        }}\r\n        sx={{ mb: 2 }}\r\n      />\r\n    </Grid>\r\n  );\r\n\r\n  if (loading && !Object.keys(apiKeys).length) {\r\n    return (\r\n      <Box display=\"flex\" justifyContent=\"center\" alignItems=\"center\" minHeight=\"200px\">\r\n        <CircularProgress />\r\n      </Box>\r\n    );\r\n  }\r\n\r\n  return (\r\n    <Paper elevation={3} sx={{ p: 4, m: 2 }}>\r\n      <Typography variant=\"h5\" gutterBottom sx={{ mb: 3 }}>\r\n        Configuration des Clés API pour l'IA\r\n      </Typography>\r\n      {error && <Alert severity=\"error\" sx={{ mb: 2 }}>{error}</Alert>}\r\n      {success && <Alert severity=\"success\" sx={{ mb: 2 }}>{success}</Alert>}\r\n      <Typography variant=\"body1\" sx={{ mb: 3 }}>\r\n        Configurez les clés API pour les services d'intelligence artificielle utilisés par l'application.\r\n        Ces clés sont stockées de manière sécurisée et chiffrées dans la base de données.\r\n      </Typography>\r\n\r\n      <form onSubmit={handleSubmit}>\r\n        <Grid container spacing={2}>\r\n          {/* Afficher dynamiquement tous les services API définis */}\r\n          {Object.keys(serviceLabels).map(service =>\r\n            renderApiKeyField(service)\r\n          )}\r\n        </Grid>\r\n        <Box sx={{ mt: 3, display: 'flex', justifyContent: 'flex-end' }}>\r\n          <Button\r\n            type=\"submit\"\r\n            variant=\"contained\"\r\n            color=\"primary\"\r\n            startIcon={<SaveIcon />}\r\n            disabled={loading}\r\n          >\r\n            {loading ? <CircularProgress size={24} color=\"inherit\" /> : 'Sauvegarder les Clés API'}\r\n          </Button>\r\n        </Box>\r\n      </form>\r\n    </Paper>\r\n  );\r\n};\r\n\r\nexport default ApiConfig;\r\n\r\n\r\n"], "names": ["ApiConfig", "<PERSON><PERSON><PERSON><PERSON><PERSON>", "set<PERSON><PERSON><PERSON><PERSON><PERSON>", "useState", "loading", "setLoading", "error", "setError", "success", "setSuccess", "showKeys", "setShowKeys", "useEffect", "response", "axiosInstance", "err", "handleChange", "service", "value", "prev", "toggleShowKey", "handleSubmit", "serviceLabels", "renderApiKeyField", "label", "Grid", "jsx", "TextField", "e", "<PERSON><PERSON><PERSON>", "IconButton", "VisibilityOff", "Visibility", "Box", "CircularProgress", "jsxs", "Paper", "Typography", "<PERSON><PERSON>", "<PERSON><PERSON>", "SaveIcon"], "mappings": "+OAgBA,MAAMA,EAAY,IAAM,CACtB,KAAM,CAACC,EAASC,CAAU,EAAIC,EAAAA,SAAS,CAAA,CAAE,EACnC,CAACC,EAASC,CAAU,EAAIF,EAAAA,SAAS,EAAI,EACrC,CAACG,EAAOC,CAAQ,EAAIJ,EAAAA,SAAS,EAAE,EAC/B,CAACK,EAASC,CAAU,EAAIN,EAAAA,SAAS,EAAE,EACnC,CAACO,EAAUC,CAAW,EAAIR,EAAAA,SAAS,CAAA,CAAE,EAE3CS,EAAAA,UAAU,IAAM,EACO,SAAY,CAC/B,GAAI,CACFP,EAAW,EAAI,EAEf,MAAMQ,EAAW,MAAMC,EAAc,IAAI,0BAA0B,EACnEZ,EAAWW,EAAS,MAAQ,EAAE,EAC9BN,EAAS,EAAE,CAAA,OACJQ,EAAK,CACZR,EAASQ,EAAI,UAAU,MAAM,SAAW,8CAA8C,EACtF,QAAQ,MAAM,uBAAwBA,CAAG,CAAA,QAC3C,CACEV,EAAW,EAAK,CAAA,CAClB,GAEF,CAAa,EACZ,EAAE,EAEL,MAAMW,EAAe,CAACC,EAASC,IAAU,CACvChB,EAAWiB,IAAS,CAAE,GAAGA,EAAM,CAACF,CAAO,EAAGC,GAAQ,CAAA,EAG9CE,EAAiBH,GAAY,CACjCN,EAAYQ,IAAS,CAAE,GAAGA,EAAM,CAACF,CAAO,EAAG,CAACE,EAAKF,CAAO,CAAA,EAAI,CAAA,EAGxDI,EAAe,MAAO,GAAM,CAChC,EAAE,eAAA,EACFhB,EAAW,EAAI,EACfE,EAAS,EAAE,EACXE,EAAW,EAAE,EACb,GAAI,CAEF,QAAQ,IAAI,mBAAoB,CAAE,QAAAR,CAAA,CAAS,EAG3C,MAAMY,EAAW,MAAMC,EAAc,KAAK,2BAA4B,CAAE,QAAAb,EAAS,EACjF,QAAQ,IAAI,mBAAoBY,EAAS,IAAI,EAC7CJ,EAAW,qCAAqC,CAAA,OACzCM,EAAK,CACZ,QAAQ,MAAM,iCAAkC,CAC9C,QAASA,EAAI,QACb,SAAUA,EAAI,UAAU,KACxB,OAAQA,EAAI,UAAU,MAAA,CACvB,EACDR,EAASQ,EAAI,UAAU,MAAM,SAAWA,EAAI,UAAU,MAAM,OAAS,4CAA4C,EACjH,QAAQ,MAAM,uBAAwBA,CAAG,CAAA,QAC3C,CACEV,EAAW,EAAK,CAAA,CAClB,EAIIiB,EAAgB,CACpB,OAAQ,iBACR,OAAQ,oBACR,aAAc,uBACd,OAAQ,2BACR,OAAQ,uBAAA,EAGJC,EAAoB,CAACN,EAASO,UACjCC,EAAA,CAAK,KAAI,GAAC,GAAI,GACb,SAAAC,EAAAA,IAACC,EAAA,CACC,UAAS,GACT,MAAgBL,EAAcL,CAAO,GAAK,GAAGA,CAAO,WACpD,QAAQ,WACR,KAAMP,EAASO,CAAO,EAAI,OAAS,WACnC,MAAOhB,EAAQgB,CAAO,GAAK,GAC3B,SAAWW,GAAMZ,EAAaC,EAASW,EAAE,OAAO,KAAK,EACrD,WAAY,CACV,aACEF,EAAAA,IAACG,EAAA,CAAQ,MAAOnB,EAASO,CAAO,EAAI,gBAAkB,kBACpD,SAAAS,EAAAA,IAACI,EAAA,CAAW,QAAS,IAAMV,EAAcH,CAAO,EAAG,KAAK,MACrD,SAAAP,EAASO,CAAO,EAAIS,EAAAA,IAACK,EAAA,CAAA,CAAc,EAAKL,EAAAA,IAACM,EAAA,CAAA,CAAW,CAAA,CACvD,CAAA,CACF,CAAA,EAGJ,GAAI,CAAE,GAAI,CAAA,CAAE,CAAA,GAjBQf,CAmBxB,EAGF,OAAIb,GAAW,CAAC,OAAO,KAAKH,CAAO,EAAE,OAEjCyB,EAAAA,IAACO,EAAA,CAAI,QAAQ,OAAO,eAAe,SAAS,WAAW,SAAS,UAAU,QACxE,SAAAP,EAAAA,IAACQ,EAAA,CAAA,CAAiB,EACpB,EAKFC,OAACC,EAAA,CAAM,UAAW,EAAG,GAAI,CAAE,EAAG,EAAG,EAAG,CAAA,EAClC,SAAA,CAAAV,EAAAA,IAACW,EAAA,CAAW,QAAQ,KAAK,aAAY,GAAC,GAAI,CAAE,GAAI,CAAA,EAAK,SAAA,sCAAA,CAErD,EACC/B,GAASoB,EAAAA,IAACY,EAAA,CAAM,SAAS,QAAQ,GAAI,CAAE,GAAI,CAAA,EAAM,SAAAhC,CAAA,CAAM,EACvDE,GAAWkB,EAAAA,IAACY,EAAA,CAAM,SAAS,UAAU,GAAI,CAAE,GAAI,CAAA,EAAM,SAAA9B,CAAA,CAAQ,EAC9DkB,EAAAA,IAACW,GAAW,QAAQ,QAAQ,GAAI,CAAE,GAAI,CAAA,EAAK,SAAA,qLAAA,CAG3C,EAEAF,EAAAA,KAAC,OAAA,CAAK,SAAUd,EACd,SAAA,CAAAK,EAAAA,IAACD,EAAA,CAAK,UAAS,GAAC,QAAS,EAEtB,SAAA,OAAO,KAAKH,CAAa,EAAE,IAAIL,GAC9BM,EAAkBN,CAAO,CAAA,EAE7B,EACAS,EAAAA,IAACO,EAAA,CAAI,GAAI,CAAE,GAAI,EAAG,QAAS,OAAQ,eAAgB,UAAA,EACjD,SAAAP,EAAAA,IAACa,EAAA,CACC,KAAK,SACL,QAAQ,YACR,MAAM,UACN,gBAAYC,EAAA,EAAS,EACrB,SAAUpC,EAET,WAAUsB,EAAAA,IAACQ,EAAA,CAAiB,KAAM,GAAI,MAAM,UAAU,EAAK,0BAAA,CAAA,CAC9D,CACF,CAAA,CAAA,CACF,CAAA,EACF,CAEJ"}