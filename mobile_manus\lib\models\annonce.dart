class Annonce {
  final int id;
  final String titre;
  final String description;
  final String espece;
  final String? race;
  final int quantite;
  final double prixUnitaire;
  final List<String>? images;
  final String status;
  final int vendeurId;
  final DateTime createdAt;
  final DateTime updatedAt;

  Annonce({
    required this.id,
    required this.titre,
    required this.description,
    required this.espece,
    this.race,
    required this.quantite,
    required this.prixUnitaire,
    this.images,
    required this.status,
    required this.vendeurId,
    required this.createdAt,
    required this.updatedAt,
  });

  factory Annonce.fromJson(Map<String, dynamic> json) {
    return Annonce(
      id: json["id"],
      titre: json["titre"],
      description: json["description"],
      espece: json["espece"],
      race: json["race"],
      quantite: json["quantite"],
      prixUnitaire: json["prix_unitaire"].toDouble(),
      images: json["images"] != null ? List<String>.from(json["images"]) : null,
      status: json["status"],
      vendeurId: json["vendeur_id"],
      createdAt: DateTime.parse(json["created_at"]),
      updatedAt: DateTime.parse(json["updated_at"]),
    );
  }

  Map<String, dynamic> toJson() {
    return {
      "id": id,
      "titre": titre,
      "description": description,
      "espece": espece,
      "race": race,
      "quantite": quantite,
      "prix_unitaire": prixUnitaire,
      "images": images,
      "status": status,
      "vendeur_id": vendeurId,
      "created_at": createdAt.toIso8601String(),
      "updated_at": updatedAt.toIso8601String(),
    };
  }
}


