import 'package:dio/dio.dart';

class MarchandService {
  final Dio _dio = Dio();  final String baseUrl = 'http://192.168.1.102:3003/api';

  MarchandService() {
    _dio.options.baseUrl = baseUrl;
    _dio.options.connectTimeout = const Duration(seconds: 5);
    _dio.options.receiveTimeout = const Duration(seconds: 3);
  }

  void setAuthToken(String token) {
    _dio.options.headers['Authorization'] = 'Bearer $token';
  }

  Future<Map<String, dynamic>> getDashboard() async {
    try {
      final response = await _dio.get('/marchand/dashboard');

      if (response.statusCode == 200) {
        return response.data;
      } else {
        throw Exception('Failed to load marchand dashboard');
      }
    } on DioException catch (e) {
      if (e.response != null) {
        throw Exception('Dashboard error: ${e.response?.data['message'] ?? 'Unknown error'}');
      } else {
        throw Exception('Network error: ${e.message}');
      }
    }
  }

  Future<Map<String, dynamic>> getRevenue({
    required String startDate,
    required String endDate,
    String groupBy = 'day',
  }) async {
    try {
      final response = await _dio.get(
        '/marchand/dashboard/revenue',
        queryParameters: {
          'start_date': startDate,
          'end_date': endDate,
          'groupBy': groupBy,
        },
      );

      if (response.statusCode == 200) {
        return response.data;
      } else {
        throw Exception('Failed to load revenue data');
      }
    } on DioException catch (e) {
      if (e.response != null) {
        throw Exception('Revenue error: ${e.response?.data['message'] ?? 'Unknown error'}');
      } else {
        throw Exception('Network error: ${e.message}');
      }
    }
  }

  Future<Map<String, dynamic>> createProductRapide(Map<String, dynamic> productData) async {
    try {
      final response = await _dio.post(
        '/marchand/products/quick',
        data: productData,
      );

      if (response.statusCode == 201) {
        return response.data;
      } else {
        throw Exception('Failed to create product');
      }
    } on DioException catch (e) {
      if (e.response != null) {
        throw Exception('Product creation error: ${e.response?.data['message'] ?? 'Unknown error'}');
      } else {
        throw Exception('Network error: ${e.message}');
      }
    }
  }

  Future<Map<String, dynamic>> updateProductStock(int productId, Map<String, dynamic> stockData) async {
    try {
      final response = await _dio.patch(
        '/marchand/products/$productId/stock',
        data: stockData,
      );

      if (response.statusCode == 200) {
        return response.data;
      } else {
        throw Exception('Failed to update product stock');
      }
    } on DioException catch (e) {
      if (e.response != null) {
        throw Exception('Stock update error: ${e.response?.data['message'] ?? 'Unknown error'}');
      } else {
        throw Exception('Network error: ${e.message}');
      }
    }
  }

  Future<List<Map<String, dynamic>>> getAIRecommendations() async {
    try {
      final response = await _dio.get('/marchand/ai/recommendations');

      if (response.statusCode == 200) {
        return List<Map<String, dynamic>>.from(response.data);
      } else {
        throw Exception('Failed to load AI recommendations');
      }
    } on DioException catch (e) {
      if (e.response != null) {
        throw Exception('AI recommendations error: ${e.response?.data['message'] ?? 'Unknown error'}');
      } else {
        throw Exception('Network error: ${e.message}');
      }
    }
  }

  Future<List<Map<String, dynamic>>> getProducts({int page = 1, int limit = 10}) async {
    try {
      final response = await _dio.get(
        '/marchand/products',
        queryParameters: {
          'page': page,
          'limit': limit,
        },
      );

      if (response.statusCode == 200) {
        return List<Map<String, dynamic>>.from(response.data['products'] ?? response.data);
      } else {
        throw Exception('Failed to load products');
      }
    } on DioException catch (e) {
      if (e.response != null) {
        throw Exception('Products error: ${e.response?.data['message'] ?? 'Unknown error'}');
      } else {
        throw Exception('Network error: ${e.message}');
      }
    }
  }

  Future<List<Map<String, dynamic>>> getOrders({int page = 1, int limit = 10}) async {
    try {
      final response = await _dio.get(
        '/marchand/orders',
        queryParameters: {
          'page': page,
          'limit': limit,
        },
      );

      if (response.statusCode == 200) {
        return List<Map<String, dynamic>>.from(response.data['orders'] ?? response.data);
      } else {
        throw Exception('Failed to load orders');
      }
    } on DioException catch (e) {
      if (e.response != null) {
        throw Exception('Orders error: ${e.response?.data['message'] ?? 'Unknown error'}');
      } else {
        throw Exception('Network error: ${e.message}');
      }
    }
  }

  Future<Map<String, dynamic>> createOrder(Map<String, dynamic> orderData) async {
    try {
      final response = await _dio.post(
        '/marchand/orders',
        data: orderData,
      );

      if (response.statusCode == 201) {
        return response.data;
      } else {
        throw Exception('Failed to create order');
      }
    } on DioException catch (e) {
      if (e.response != null) {
        throw Exception('Order creation error: ${e.response?.data['message'] ?? 'Unknown error'}');
      } else {
        throw Exception('Network error: ${e.message}');
      }
    }
  }

  Future<Map<String, dynamic>> updateOrderStatus(int orderId, String status) async {
    try {
      final response = await _dio.patch(
        '/marchand/orders/$orderId/status',
        data: {'status': status},
      );

      if (response.statusCode == 200) {
        return response.data;
      } else {
        throw Exception('Failed to update order status');
      }
    } on DioException catch (e) {
      if (e.response != null) {
        throw Exception('Order status update error: ${e.response?.data['message'] ?? 'Unknown error'}');
      } else {
        throw Exception('Network error: ${e.message}');
      }
    }
  }
}
