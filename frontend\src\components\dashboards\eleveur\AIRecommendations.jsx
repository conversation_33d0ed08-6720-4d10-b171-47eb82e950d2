
import {
  Box,
  Typography,
  Card,
  CardHeader,
  Card<PERSON>ontent,
  Divider,
  List,
  ListItem,
  ListItemIcon,
  ListItemText,
  Chip,
  Tooltip,
  IconButton
} from '@mui/material';
import {
  Lightbulb,
  TrendingUp,
  Warning,
  MonetizationOn,
  Pets,
  Inventory,
  Info
} from '@mui/icons-material';

/**
 * Composant de recommandations IA pour le tableau de bord éleveur
 * Affiche des suggestions intelligentes basées sur les données de l'éleveur
 *
 * @param {Object} props - Les propriétés du composant
 * @param {Array} props.recommendations - Liste des recommandations à afficher
 * @returns {JSX.Element} - Le composant de recommandations
 */
const AIRecommendations = ({ recommendations = [] }) => {
  // Si aucune recommandation n'est fournie, utiliser des recommandations par défaut
  const defaultRecommendations = [
    {
      id: 1,
      type: 'market',
      title: 'Opportunité de marché',
      description: 'Les prix des poulets devraient augmenter de 8% la semaine prochaine. Envisagez de retarder vos ventes.',
      importance: 'high',
      icon: <MonetizationOn />,
      color: '#10B981' // vert
    },
    {
      id: 2,
      type: 'health',
      title: 'Alerte sanitaire',
      description: 'Risque élevé de maladie détecté dans votre région. Vérifiez votre lot #A123.',
      importance: 'high',
      icon: <Warning />,
      color: '#EF4444' // rouge
    },
    {
      id: 3,
      type: 'stock',
      title: 'Gestion des stocks',
      description: 'Votre stock d\'aliments sera épuisé dans 5 jours. Commandez maintenant pour éviter une rupture.',
      importance: 'medium',
      icon: <Inventory />,
      color: '#F59E0B' // orange
    },
    {
      id: 4,
      type: 'optimization',
      title: 'Optimisation',
      description: 'Augmentez votre production de 15% en ajustant la température de 2°C dans le bâtiment B.',
      importance: 'medium',
      icon: <TrendingUp />,
      color: '#3B82F6' // bleu
    }
  ];

  // Utiliser les recommandations fournies ou les recommandations par défaut
  const displayRecommendations = recommendations.length > 0 ? recommendations : defaultRecommendations;

  // Fonction pour obtenir la couleur de fond en fonction de l'importance
  const getBackgroundColor = (importance) => {
    switch (importance) {
      case 'high':
        return 'rgba(239, 68, 68, 0.1)'; // rouge clair
      case 'medium':
        return 'rgba(245, 158, 11, 0.1)'; // orange clair
      case 'low':
        return 'rgba(59, 130, 246, 0.1)'; // bleu clair
      default:
        return 'rgba(209, 213, 219, 0.1)'; // gris clair
    }
  };

  // Fonction pour obtenir la couleur de bordure en fonction de l'importance
  const getBorderColor = (importance) => {
    switch (importance) {
      case 'high':
        return '#EF4444'; // rouge
      case 'medium':
        return '#F59E0B'; // orange
      case 'low':
        return '#3B82F6'; // bleu
      default:
        return '#D1D5DB'; // gris
    }
  };

  return (
    <Card sx={{ height: '100%' }}>
      <CardHeader
        title={
          <Box sx={{ display: 'flex', alignItems: 'center' }}>
            <Lightbulb sx={{ mr: 1, color: '#F59E0B' }} />
            <Typography variant="h6">Recommandations IA</Typography>
            <Tooltip title="Recommandations basées sur l'analyse de vos données et des tendances du marché">
              <IconButton size="small" sx={{ ml: 1 }}>
                <Info fontSize="small" />
              </IconButton>
            </Tooltip>
          </Box>
        }
      />
      <Divider />
      <CardContent sx={{ p: 0 }}>
        <List disablePadding>
          {displayRecommendations.map((recommendation) => (
            <ListItem
              key={recommendation.id}
              sx={{
                py: 1.5,
                px: 2,
                borderLeft: `4px solid ${recommendation.color || getBorderColor(recommendation.importance)}`,
                backgroundColor: getBackgroundColor(recommendation.importance),
                '&:not(:last-child)': {
                  borderBottom: '1px solid rgba(0, 0, 0, 0.08)'
                }
              }}
            >
              <ListItemIcon sx={{ minWidth: 40, color: recommendation.color || getBorderColor(recommendation.importance) }}>
                {React.isValidElement(recommendation.icon) ? recommendation.icon :
                 (recommendation.icon ? React.createElement(recommendation.icon) : <Lightbulb />)}
              </ListItemIcon>
              <ListItemText
                primary={
                  <Box display="flex" justifyContent="space-between" alignItems="center">
                    <Typography variant="subtitle2" fontWeight="bold">
                      {recommendation.title}
                    </Typography>
                    <Chip
                      label={recommendation.importance === 'high' ? 'Important' : 'Suggestion'}
                      size="small"
                      sx={{
                        backgroundColor: recommendation.importance === 'high' ? 'rgba(239, 68, 68, 0.1)' : 'rgba(59, 130, 246, 0.1)',
                        color: recommendation.importance === 'high' ? '#EF4444' : '#3B82F6',
                        fontWeight: 'bold',
                        fontSize: '0.7rem'
                      }}
                    />
                  </Box>
                }
                secondary={recommendation.description}
                secondaryTypographyProps={{
                  variant: 'body2',
                  sx: { mt: 0.5 }
                }}
              />
            </ListItem>
          ))}
        </List>
      </CardContent>
    </Card>
  );
};

export default AIRecommendations;
