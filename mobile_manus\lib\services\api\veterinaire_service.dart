import 'package:dio/dio.dart';
import 'package:mobile_manus/models/veterinaire.dart';

class VeterinaireService {
  final Dio _dio = Dio();  final String baseUrl = 'http://192.168.1.102:3003/api';

  VeterinaireService() {
    _dio.options.baseUrl = baseUrl;
    _dio.options.connectTimeout = const Duration(seconds: 5);
    _dio.options.receiveTimeout = const Duration(seconds: 3);
  }

  void setAuthToken(String token) {
    _dio.options.headers['Authorization'] = 'Bearer $token';
  }

  Future<Map<String, dynamic>> getDashboard() async {
    try {
      final response = await _dio.get('/veterinaire/dashboard');

      if (response.statusCode == 200) {
        return response.data;
      } else {
        throw Exception('Failed to load veterinaire dashboard');
      }
    } on DioException catch (e) {
      if (e.response != null) {
        throw Exception('Dashboard error: ${e.response?.data['message'] ?? 'Unknown error'}');
      } else {
        throw Exception('Network error: ${e.message}');
      }
    }
  }

  Future<List<Map<String, dynamic>>> getNotifications() async {
    try {
      final response = await _dio.get('/veterinaire/notifications');

      if (response.statusCode == 200) {
        return List<Map<String, dynamic>>.from(response.data);
      } else {
        throw Exception('Failed to load notifications');
      }
    } on DioException catch (e) {
      if (e.response != null) {
        throw Exception('Notifications error: ${e.response?.data['message'] ?? 'Unknown error'}');
      } else {
        throw Exception('Network error: ${e.message}');
      }
    }
  }

  Future<Map<String, dynamic>> createConsultationRapide(Map<String, dynamic> consultationData) async {
    try {
      final response = await _dio.post(
        '/veterinaire/consultations/quick',
        data: consultationData,
      );

      if (response.statusCode == 201) {
        return response.data;
      } else {
        throw Exception('Failed to create consultation');
      }
    } on DioException catch (e) {
      if (e.response != null) {
        throw Exception('Consultation error: ${e.response?.data['message'] ?? 'Unknown error'}');
      } else {
        throw Exception('Network error: ${e.message}');
      }
    }
  }

  Future<Map<String, dynamic>> createPrescriptionRapide(Map<String, dynamic> prescriptionData) async {
    try {
      final response = await _dio.post(
        '/veterinaire/prescriptions/quick',
        data: prescriptionData,
      );

      if (response.statusCode == 201) {
        return response.data;
      } else {
        throw Exception('Failed to create prescription');
      }
    } on DioException catch (e) {
      if (e.response != null) {
        throw Exception('Prescription error: ${e.response?.data['message'] ?? 'Unknown error'}');
      } else {
        throw Exception('Network error: ${e.message}');
      }
    }
  }

  Future<List<Veterinaire>> getVeterinaires({int page = 1, int limit = 10}) async {
    try {
      final response = await _dio.get(
        '/veterinaires',
        queryParameters: {
          'page': page,
          'limit': limit,
        },
      );

      if (response.statusCode == 200) {
        final List<dynamic> data = response.data['veterinaires'] ?? response.data;
        return data.map((json) => Veterinaire.fromJson(json)).toList();
      } else {
        throw Exception('Failed to load veterinaires');
      }
    } on DioException catch (e) {
      if (e.response != null) {
        throw Exception('Veterinaires error: ${e.response?.data['message'] ?? 'Unknown error'}');
      } else {
        throw Exception('Network error: ${e.message}');
      }
    }
  }

  Future<Veterinaire> getVeterinaire(int id) async {
    try {
      final response = await _dio.get('/veterinaires/$id');

      if (response.statusCode == 200) {
        return Veterinaire.fromJson(response.data);
      } else {
        throw Exception('Failed to load veterinaire');
      }
    } on DioException catch (e) {
      if (e.response != null) {
        throw Exception('Veterinaire error: ${e.response?.data['message'] ?? 'Unknown error'}');
      } else {
        throw Exception('Network error: ${e.message}');
      }
    }
  }

  Future<Veterinaire> createVeterinaire(Map<String, dynamic> veterinaireData) async {
    try {
      final response = await _dio.post(
        '/veterinaires',
        data: veterinaireData,
      );

      if (response.statusCode == 201) {
        return Veterinaire.fromJson(response.data);
      } else {
        throw Exception('Failed to create veterinaire');
      }
    } on DioException catch (e) {
      if (e.response != null) {
        throw Exception('Create veterinaire error: ${e.response?.data['message'] ?? 'Unknown error'}');
      } else {
        throw Exception('Network error: ${e.message}');
      }
    }
  }

  Future<Veterinaire> updateVeterinaire(int id, Map<String, dynamic> veterinaireData) async {
    try {
      final response = await _dio.put(
        '/veterinaires/$id',
        data: veterinaireData,
      );

      if (response.statusCode == 200) {
        return Veterinaire.fromJson(response.data);
      } else {
        throw Exception('Failed to update veterinaire');
      }
    } on DioException catch (e) {
      if (e.response != null) {
        throw Exception('Update veterinaire error: ${e.response?.data['message'] ?? 'Unknown error'}');
      } else {
        throw Exception('Network error: ${e.message}');
      }
    }
  }

  Future<void> deleteVeterinaire(int id) async {
    try {
      final response = await _dio.delete('/veterinaires/$id');

      if (response.statusCode != 204) {
        throw Exception('Failed to delete veterinaire');
      }
    } on DioException catch (e) {
      if (e.response != null) {
        throw Exception('Delete veterinaire error: ${e.response?.data['message'] ?? 'Unknown error'}');
      } else {
        throw Exception('Network error: ${e.message}');
      }
    }
  }
}
