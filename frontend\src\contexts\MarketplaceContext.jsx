import { createContext, useContext, useReducer, useEffect } from 'react';
import axios from 'axios';

const MarketplaceContext = createContext();

const initialState = {
  cart: [],
  favorites: [],
  searchFilters: {
    query: '',
    categories: [],
    priceRange: [0, 10000],
    sortBy: 'relevance'
  },
  loading: false,
  error: null
};

const marketplaceReducer = (state, action) => {
  switch (action.type) {
    case 'SET_CART':
      return { ...state, cart: action.payload };
    case 'ADD_TO_CART':
      return { ...state, cart: [...state.cart, action.payload] };
    case 'UPDATE_CART_ITEM':
      return {
        ...state,
        cart: state.cart.map(item =>
          item.id === action.payload.id ? { ...item, ...action.payload } : item
        )
      };
    case 'REMOVE_FROM_CART':
      return {
        ...state,
        cart: state.cart.filter(item => item.id !== action.payload)
      };
    case 'SET_FAVORITES':
      return { ...state, favorites: action.payload };
    case 'TOGGLE_FAVORITE':
      return {
        ...state,
        favorites: state.favorites.includes(action.payload)
          ? state.favorites.filter(id => id !== action.payload)
          : [...state.favorites, action.payload]
      };
    case 'UPDATE_SEARCH_FILTERS':
      return {
        ...state,
        searchFilters: { ...state.searchFilters, ...action.payload }
      };
    case 'SET_LOADING':
      return { ...state, loading: action.payload };
    case 'SET_ERROR':
      return { ...state, error: action.payload };
    case 'CLEAR_ERROR':
      return { ...state, error: null };
    default:
      return state;
  }
};

export const MarketplaceProvider = ({ children }) => {
  const [state, dispatch] = useReducer(marketplaceReducer, initialState);

  useEffect(() => {
    fetchCart();
    fetchFavorites();
  }, []);

  const fetchCart = async () => {
    try {
      dispatch({ type: 'SET_LOADING', payload: true });
      const response = await axios.get('/api/marketplace/cart');
      dispatch({ type: 'SET_CART', payload: response.data });
    } catch (error) {
      dispatch({
        type: 'SET_ERROR',
        payload: 'Erreur lors du chargement du panier'
      });
    } finally {
      dispatch({ type: 'SET_LOADING', payload: false });
    }
  };

  const fetchFavorites = async () => {
    try {
      const response = await axios.get('/api/marketplace/favorites');
      dispatch({ type: 'SET_FAVORITES', payload: response.data });
    } catch (error) {
      console.error('Erreur lors du chargement des favoris:', error);
    }
  };

  const addToCart = async (product, quantity = 1) => {
    try {
      dispatch({ type: 'SET_LOADING', payload: true });
      const response = await axios.post('/api/marketplace/cart', {
        productId: product.id,
        quantity
      });
      dispatch({ type: 'ADD_TO_CART', payload: response.data });
    } catch (error) {
      dispatch({
        type: 'SET_ERROR',
        payload: 'Erreur lors de l\'ajout au panier'
      });
    } finally {
      dispatch({ type: 'SET_LOADING', payload: false });
    }
  };

  const updateCartItem = async (itemId, quantity) => {
    try {
      dispatch({ type: 'SET_LOADING', payload: true });
      const response = await axios.put(`/api/marketplace/cart/${itemId}`, {
        quantity
      });
      dispatch({ type: 'UPDATE_CART_ITEM', payload: response.data });
    } catch (error) {
      dispatch({
        type: 'SET_ERROR',
        payload: 'Erreur lors de la mise à jour du panier'
      });
    } finally {
      dispatch({ type: 'SET_LOADING', payload: false });
    }
  };

  const removeFromCart = async (itemId) => {
    try {
      dispatch({ type: 'SET_LOADING', payload: true });
      await axios.delete(`/api/marketplace/cart/${itemId}`);
      dispatch({ type: 'REMOVE_FROM_CART', payload: itemId });
    } catch (error) {
      dispatch({
        type: 'SET_ERROR',
        payload: 'Erreur lors de la suppression du produit'
      });
    } finally {
      dispatch({ type: 'SET_LOADING', payload: false });
    }
  };

  const toggleFavorite = async (productId) => {
    try {
      if (state.favorites.includes(productId)) {
        await axios.delete(`/api/marketplace/favorites/${productId}`);
      } else {
        await axios.post('/api/marketplace/favorites', { productId });
      }
      dispatch({ type: 'TOGGLE_FAVORITE', payload: productId });
    } catch (error) {
      dispatch({
        type: 'SET_ERROR',
        payload: 'Erreur lors de la mise à jour des favoris'
      });
    }
  };

  const updateSearchFilters = (filters) => {
    dispatch({ type: 'UPDATE_SEARCH_FILTERS', payload: filters });
  };

  const clearError = () => {
    dispatch({ type: 'CLEAR_ERROR' });
  };

  const calculateCartTotal = () => {
    return state.cart.reduce((total, item) => total + (item.prix * item.quantity), 0);
  };

  const value = {
    cart: state.cart,
    favorites: state.favorites,
    searchFilters: state.searchFilters,
    loading: state.loading,
    error: state.error,
    addToCart,
    updateCartItem,
    removeFromCart,
    toggleFavorite,
    updateSearchFilters,
    clearError,
    calculateCartTotal
  };

  return (
    <MarketplaceContext.Provider value={value}>
      {children}
    </MarketplaceContext.Provider>
  );
};

export const useMarketplace = () => {
  const context = useContext(MarketplaceContext);
  if (!context) {
    throw new Error('useMarketplace must be used within a MarketplaceProvider');
  }
  return context;
};
