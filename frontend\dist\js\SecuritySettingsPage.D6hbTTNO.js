import{r as l,j as e}from"./vendor.CTQIA7G6.js";import{s as A}from"./settingsService.DVLXOYtr.js";import{u as O}from"./index.WVN8qCy5.js";import{b as h,t as E,a as c,aR as q,q as B,W as j,G as s,bs as M,al as b,bo as P,br as T,bt as z,K as o,an as V,bu as G,bv as K,bw as N,O as Z,Q,R as U,e as d,B as _,aT as $,V as J}from"./mui.D_tNY0b-.js";import"./firebase.BaqyMmVp.js";const ie=()=>{const{t}=O(),[i,g]=l.useState({enable2FA:!1,sessionTimeout:30,maxLoginAttempts:5,lockoutDuration:15,passwordComplexityRegex:"",passwordHistoryCount:3,passwordExpiryDays:90,contentSecurityPolicy:"",corsAllowedOrigins:"",logLevel:"info",apiRateLimitingEnabled:!0,apiRateLimitRequests:100,apiRateLimitWindowMs:9e5}),[W,v]=l.useState(!0),[S,f]=l.useState(!1),[w,u]=l.useState(""),[L,F]=l.useState(""),[m,C]=l.useState({open:!1,message:"",severity:"info"});l.useEffect(()=>{(async()=>{try{v(!0),console.log("Fetching security settings via SecuritySettingsPage component...");const n=await A.getSecuritySettings();console.log("Fetched security settings:",n),g(n),u("")}catch(n){console.error("Error fetching security settings:",n),u(t("settings.security.fetchError")||"Failed to load security settings"),x(t("settings.security.fetchError")||"Failed to load security settings","error")}finally{v(!1)}})()},[t]);const r=a=>{const{name:n,value:p,checked:y}=a.target,k=a.target.type==="checkbox"?y:p;g(H=>({...H,[n]:k}))},D=a=>(n,p)=>{g(y=>({...y,[a]:p}))},I=async a=>{a.preventDefault();try{f(!0),u(""),console.log("Submitting security settings:",i),await A.updateSecuritySettings(i),console.log("Security settings updated successfully"),F(t("settings.security.saveSuccess")||"Security settings updated successfully"),x(t("settings.security.saveSuccess")||"Security settings updated successfully","success")}catch(n){console.error("Error updating security settings:",n),u(t("settings.security.saveError")||"Failed to update security settings"),x(t("settings.security.saveError")||"Failed to update security settings","error")}finally{f(!1)}},x=(a,n)=>{C({open:!0,message:a,severity:n})},R=()=>{C({...m,open:!1})};return W?e.jsx(h,{sx:{display:"flex",justifyContent:"center",mt:4},children:e.jsx(E,{})}):e.jsxs(h,{sx:{p:3},children:[e.jsxs(c,{variant:"h4",component:"h1",gutterBottom:!0,children:[e.jsx(q,{sx:{mr:1,verticalAlign:"middle"}}),t("settings.security.title")||"Security Settings"]}),e.jsxs(B,{sx:{p:3,mt:2},children:[w&&e.jsx(j,{severity:"error",sx:{mb:2},children:w}),L&&e.jsx(j,{severity:"success",sx:{mb:2},children:L}),e.jsx("form",{onSubmit:I,children:e.jsxs(s,{container:!0,spacing:3,children:[e.jsxs(s,{item:!0,xs:12,children:[e.jsxs(c,{variant:"h5",component:"h2",gutterBottom:!0,children:[e.jsx(M,{sx:{mr:1,verticalAlign:"middle"}}),t("settings.security.authentication.title")||"Authentication"]}),e.jsx(b,{sx:{mb:2}})]}),e.jsx(s,{item:!0,xs:12,sm:6,children:e.jsx(P,{control:e.jsx(T,{checked:i.enable2FA,onChange:r,name:"enable2FA",color:"primary"}),label:t("settings.security.authentication.enable2FA")||"Enable Two-Factor Authentication"})}),e.jsxs(s,{item:!0,xs:12,sm:6,children:[e.jsx(c,{gutterBottom:!0,children:t("settings.security.authentication.sessionTimeout")||"Session Timeout (minutes)"}),e.jsx(z,{value:i.sessionTimeout,onChange:D("sessionTimeout"),"aria-labelledby":"session-timeout-slider",valueLabelDisplay:"auto",step:5,marks:!0,min:5,max:120})]}),e.jsx(s,{item:!0,xs:12,sm:6,children:e.jsx(o,{fullWidth:!0,label:t("settings.security.authentication.maxLoginAttempts")||"Max Login Attempts",name:"maxLoginAttempts",type:"number",value:i.maxLoginAttempts,onChange:r,InputProps:{startAdornment:e.jsx(V,{position:"start",children:e.jsx(G,{})})}})}),e.jsx(s,{item:!0,xs:12,sm:6,children:e.jsx(o,{fullWidth:!0,label:t("settings.security.authentication.lockoutDuration")||"Lockout Duration (minutes)",name:"lockoutDuration",type:"number",value:i.lockoutDuration,onChange:r})}),e.jsxs(s,{item:!0,xs:12,children:[e.jsxs(c,{variant:"h5",component:"h2",gutterBottom:!0,sx:{mt:2},children:[e.jsx(K,{sx:{mr:1,verticalAlign:"middle"}}),t("settings.security.passwordPolicy.title")||"Password Policy"]}),e.jsx(b,{sx:{mb:2}})]}),e.jsx(s,{item:!0,xs:12,children:e.jsx(o,{fullWidth:!0,label:t("settings.security.passwordPolicy.complexityRegex")||"Password Complexity Regex",name:"passwordComplexityRegex",value:i.passwordComplexityRegex,onChange:r,helperText:t("settings.security.passwordPolicy.complexityRegexHelp")||"Regular expression for password validation (e.g., ^(?=.*[a-z])(?=.*[A-Z])(?=.*\\d)[a-zA-Z\\d]{8,}$)"})}),e.jsx(s,{item:!0,xs:12,sm:6,children:e.jsx(o,{fullWidth:!0,label:t("settings.security.passwordPolicy.historyCount")||"Password History Count",name:"passwordHistoryCount",type:"number",value:i.passwordHistoryCount,onChange:r,helperText:t("settings.security.passwordPolicy.historyCountHelp")||"Number of previous passwords to remember to prevent reuse"})}),e.jsx(s,{item:!0,xs:12,sm:6,children:e.jsx(o,{fullWidth:!0,label:t("settings.security.passwordPolicy.expiryDays")||"Password Expiry (days)",name:"passwordExpiryDays",type:"number",value:i.passwordExpiryDays,onChange:r,helperText:t("settings.security.passwordPolicy.expiryDaysHelp")||"Number of days after which passwords expire (0 to disable)"})}),e.jsxs(s,{item:!0,xs:12,children:[e.jsxs(c,{variant:"h5",component:"h2",gutterBottom:!0,sx:{mt:2},children:[e.jsx(N,{sx:{mr:1,verticalAlign:"middle"}}),t("settings.security.apiSecurity.title")||"API Security"]}),e.jsx(b,{sx:{mb:2}})]}),e.jsx(s,{item:!0,xs:12,children:e.jsx(o,{fullWidth:!0,label:t("settings.security.apiSecurity.corsAllowedOrigins")||"CORS Allowed Origins",name:"corsAllowedOrigins",value:i.corsAllowedOrigins,onChange:r,helperText:t("settings.security.apiSecurity.corsAllowedOriginsHelp")||"Comma-separated list of allowed origins for CORS (e.g., http://localhost:3000,https://yourdomain.com)"})}),e.jsx(s,{item:!0,xs:12,sm:6,children:e.jsxs(Z,{fullWidth:!0,children:[e.jsx(Q,{children:t("settings.security.apiSecurity.logLevel")||"Log Level"}),e.jsxs(U,{name:"logLevel",value:i.logLevel,label:t("settings.security.apiSecurity.logLevel")||"Log Level",onChange:r,children:[e.jsx(d,{value:"debug",children:"Debug"}),e.jsx(d,{value:"info",children:"Info"}),e.jsx(d,{value:"warn",children:"Warning"}),e.jsx(d,{value:"error",children:"Error"})]})]})}),e.jsx(s,{item:!0,xs:12,sm:6,children:e.jsx(P,{control:e.jsx(T,{checked:i.apiRateLimitingEnabled,onChange:r,name:"apiRateLimitingEnabled",color:"primary"}),label:t("settings.security.apiSecurity.rateLimitingEnabled")||"Enable API Rate Limiting"})}),i.apiRateLimitingEnabled&&e.jsxs(e.Fragment,{children:[e.jsx(s,{item:!0,xs:12,sm:6,children:e.jsx(o,{fullWidth:!0,label:t("settings.security.apiSecurity.rateLimitRequests")||"Rate Limit Requests",name:"apiRateLimitRequests",type:"number",value:i.apiRateLimitRequests,onChange:r,helperText:t("settings.security.apiSecurity.rateLimitRequestsHelp")||"Maximum number of requests in the time window"})}),e.jsx(s,{item:!0,xs:12,sm:6,children:e.jsx(o,{fullWidth:!0,label:t("settings.security.apiSecurity.rateLimitWindowMs")||"Rate Limit Window (ms)",name:"apiRateLimitWindowMs",type:"number",value:i.apiRateLimitWindowMs,onChange:r,helperText:t("settings.security.apiSecurity.rateLimitWindowMsHelp")||"Time window for rate limiting in milliseconds (e.g., 900000 for 15 minutes)"})})]}),e.jsx(s,{item:!0,xs:12,children:e.jsx(h,{sx:{display:"flex",justifyContent:"flex-end",mt:2},children:e.jsx(_,{type:"submit",variant:"contained",color:"primary",startIcon:e.jsx($,{}),disabled:S,children:S?e.jsxs(e.Fragment,{children:[e.jsx(E,{size:24,sx:{mr:1}}),t("common.saving")||"Saving..."]}):t("common.save")||"Save Changes"})})})]})})]}),e.jsx(J,{open:m.open,autoHideDuration:6e3,onClose:R,anchorOrigin:{vertical:"bottom",horizontal:"right"},children:e.jsx(j,{onClose:R,severity:m.severity,sx:{width:"100%"},children:m.message})})]})};export{ie as default};
//# sourceMappingURL=SecuritySettingsPage.D6hbTTNO.js.map
