import{r as l,j as e}from"./vendor.CTQIA7G6.js";import{s as g}from"./settingsService.DVLXOYtr.js";import{u as z}from"./index.WVN8qCy5.js";import{b as x,t as h,a as F,aN as D,q as G,W as f,G as n,bo as w,bp as I,K as o,an as O,I as U,ao as K,ap as J,B as R,bq as Q,aT as X,V as Y}from"./mui.D_tNY0b-.js";import"./firebase.BaqyMmVp.js";const te=()=>{const{t:s}=z(),[t,S]=l.useState({host:"",port:587,secure:!1,user:"",pass:"",fromName:"",fromEmail:"",replyTo:"",testEmailRecipient:"",isEnabled:!0}),[W,b]=l.useState(!0),[j,E]=l.useState(!1),[y,T]=l.useState(!1),[u,k]=l.useState(!1),[v,m]=l.useState(""),[C,p]=l.useState(""),[d,P]=l.useState({open:!1,message:"",severity:"info"});l.useEffect(()=>{(async()=>{try{b(!0);const a=await g.getSmtpConfig();console.log("Fetched SMTP settings:",a),S(a),m("")}catch(a){console.error("Error fetching SMTP settings:",a),m(s("settings.smtp.fetchError")||"Failed to load SMTP settings"),c(s("settings.smtp.fetchError")||"Failed to load SMTP settings","error")}finally{b(!1)}})()},[s]);const i=r=>{const{name:a,value:A,type:N,checked:H}=r.target;S(V=>({...V,[a]:N==="checkbox"?H:A}))},q=()=>{k(!u)},B=async r=>{r.preventDefault();try{E(!0),m(""),await g.updateSmtpConfig(t),p(s("settings.smtp.saveSuccess")||"SMTP settings updated successfully"),c(s("settings.smtp.saveSuccess")||"SMTP settings updated successfully","success")}catch(a){console.error("Error updating SMTP settings:",a),m(s("settings.smtp.saveError")||"Failed to update SMTP settings"),c(s("settings.smtp.saveError")||"Failed to update SMTP settings","error")}finally{E(!1)}},L=async()=>{try{T(!0),m(""),p("");const r=await g.testSmtpConfig({testEmail:t.testEmailRecipient});console.log("SMTP test result:",r),p(s("settings.smtp.testSuccess")||"Test email sent successfully"),c(s("settings.smtp.testSuccess")||"Test email sent successfully","success")}catch(r){console.error("Error testing SMTP connection:",r),m(s("settings.smtp.testError")||"Failed to send test email"),c(s("settings.smtp.testError")||"Failed to send test email","error")}finally{T(!1)}},c=(r,a)=>{P({open:!0,message:r,severity:a})},M=()=>{P({...d,open:!1})};return W?e.jsx(x,{sx:{display:"flex",justifyContent:"center",mt:4},children:e.jsx(h,{})}):e.jsxs(x,{sx:{p:3},children:[e.jsxs(F,{variant:"h4",component:"h1",gutterBottom:!0,children:[e.jsx(D,{sx:{mr:1,verticalAlign:"middle"}}),s("settings.smtp.title")||"SMTP Configuration"]}),e.jsxs(G,{sx:{p:3,mt:2},children:[v&&e.jsx(f,{severity:"error",sx:{mb:2},children:v}),C&&e.jsx(f,{severity:"success",sx:{mb:2},children:C}),e.jsx("form",{onSubmit:B,children:e.jsxs(n,{container:!0,spacing:3,children:[e.jsx(n,{item:!0,xs:12,children:e.jsx(w,{control:e.jsx(I,{checked:t.isEnabled,onChange:i,name:"isEnabled",color:"primary"}),label:s("settings.smtp.isEnabled")||"Enable Email Sending"})}),e.jsx(n,{item:!0,xs:12,sm:6,children:e.jsx(o,{fullWidth:!0,required:!0,label:s("settings.smtp.host")||"SMTP Host",name:"host",value:t.host,onChange:i,disabled:!t.isEnabled})}),e.jsx(n,{item:!0,xs:12,sm:6,children:e.jsx(o,{fullWidth:!0,required:!0,label:s("settings.smtp.port")||"SMTP Port",name:"port",type:"number",value:t.port,onChange:i,disabled:!t.isEnabled,helperText:s("settings.smtp.portHelp")||"Common ports: 25, 465 (SSL), 587 (TLS)"})}),e.jsx(n,{item:!0,xs:12,sm:6,children:e.jsx(o,{fullWidth:!0,required:!0,label:s("settings.smtp.user")||"SMTP Username",name:"user",value:t.user,onChange:i,disabled:!t.isEnabled})}),e.jsx(n,{item:!0,xs:12,sm:6,children:e.jsx(o,{fullWidth:!0,required:!0,label:s("settings.smtp.pass")||"SMTP Password",name:"pass",type:u?"text":"password",value:t.pass,onChange:i,disabled:!t.isEnabled,InputProps:{endAdornment:e.jsx(O,{position:"end",children:e.jsx(U,{onClick:q,edge:"end",children:u?e.jsx(K,{}):e.jsx(J,{})})})}})}),e.jsx(n,{item:!0,xs:12,sm:6,children:e.jsx(w,{control:e.jsx(I,{checked:t.secure,onChange:i,name:"secure",color:"primary",disabled:!t.isEnabled}),label:s("settings.smtp.secure")||"Use Secure Connection (SSL/TLS)"})}),e.jsx(n,{item:!0,xs:12,sm:6,children:e.jsx(o,{fullWidth:!0,required:!0,label:s("settings.smtp.fromEmail")||"From Email Address",name:"fromEmail",type:"email",value:t.fromEmail,onChange:i,disabled:!t.isEnabled})}),e.jsx(n,{item:!0,xs:12,sm:6,children:e.jsx(o,{fullWidth:!0,label:s("settings.smtp.fromName")||"From Name",name:"fromName",value:t.fromName,onChange:i,disabled:!t.isEnabled})}),e.jsx(n,{item:!0,xs:12,sm:6,children:e.jsx(o,{fullWidth:!0,label:s("settings.smtp.replyTo")||"Reply-To Email Address",name:"replyTo",type:"email",value:t.replyTo,onChange:i,disabled:!t.isEnabled})}),e.jsx(n,{item:!0,xs:12,children:e.jsx(F,{variant:"h6",gutterBottom:!0,sx:{mt:2},children:s("settings.smtp.testConnection")||"Test Email Configuration"})}),e.jsx(n,{item:!0,xs:12,sm:6,children:e.jsx(o,{fullWidth:!0,label:s("settings.smtp.testEmailRecipient")||"Test Email Recipient",name:"testEmailRecipient",type:"email",value:t.testEmailRecipient,onChange:i,disabled:!t.isEnabled,helperText:s("settings.smtp.testEmailRecipientHelp")||"Email address to receive the test message"})}),e.jsx(n,{item:!0,xs:12,sm:6,sx:{display:"flex",alignItems:"center"},children:e.jsx(R,{variant:"outlined",color:"secondary",startIcon:e.jsx(Q,{}),onClick:L,disabled:y||!t.isEnabled||!t.testEmailRecipient,sx:{mt:1},children:y?e.jsxs(e.Fragment,{children:[e.jsx(h,{size:24,sx:{mr:1}}),s("settings.smtp.testing")||"Testing..."]}):s("settings.smtp.testButton")||"Send Test Email"})}),e.jsx(n,{item:!0,xs:12,children:e.jsx(x,{sx:{display:"flex",justifyContent:"flex-end",mt:2},children:e.jsx(R,{type:"submit",variant:"contained",color:"primary",startIcon:e.jsx(X,{}),disabled:j||!t.isEnabled,children:j?e.jsxs(e.Fragment,{children:[e.jsx(h,{size:24,sx:{mr:1}}),s("common.saving")||"Saving..."]}):s("common.save")||"Save Settings"})})})]})})]}),e.jsx(Y,{open:d.open,autoHideDuration:6e3,onClose:M,anchorOrigin:{vertical:"bottom",horizontal:"right"},children:e.jsx(f,{onClose:M,severity:d.severity,sx:{width:"100%"},children:d.message})})]})};export{te as default};
//# sourceMappingURL=SmtpConfigPage.DegdsIxF.js.map
