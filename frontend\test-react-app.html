<!DOCTYPE html>
<html lang="fr">
<head>
  <meta charset="UTF-8">
  <meta name="viewport" content="width=device-width, initial-scale=1.0">
  <title>Test React App</title>
  <script src="https://unpkg.com/react@18/umd/react.development.js"></script>
  <script src="https://unpkg.com/react-dom@18/umd/react-dom.development.js"></script>
  <script src="https://unpkg.com/@babel/standalone/babel.min.js"></script>
  <style>
    body {
      font-family: Arial, sans-serif;
      margin: 0;
      padding: 0;
      background-color: #f5f5f5;
    }
    #root {
      min-height: 100vh;
      display: flex;
      flex-direction: column;
      align-items: center;
      justify-content: center;
    }
    .app-container {
      background-color: white;
      border-radius: 8px;
      box-shadow: 0 4px 10px rgba(0, 0, 0, 0.1);
      padding: 2rem;
      max-width: 600px;
      width: 100%;
      text-align: center;
    }
    .app-title {
      color: #4CAF50;
      margin-bottom: 1rem;
    }
    .app-button {
      background-color: #4CAF50;
      color: white;
      border: none;
      padding: 0.75rem 1.5rem;
      border-radius: 4px;
      font-weight: bold;
      cursor: pointer;
      margin-top: 1rem;
    }
    .app-button:hover {
      background-color: #388E3C;
    }
  </style>
</head>
<body>
  <div id="root"></div>

  <script type="text/babel">
    // Composant App simple
    function App() {
      const [count, setCount] = React.useState(0);

      const handleIncrement = () => {
        setCount(count + 1);
      };

      return (
        <div className="app-container">
          <h1 className="app-title">Test React App pour Poultray DZ</h1>
          <p>Cette application React fonctionne correctement.</p>
          <p>Compteur: {count}</p>
          <button className="app-button" onClick={handleIncrement}>Incrémenter</button>
        </div>
      );
    }

    // Rendu de l'application dans l'élément racine
    const root = ReactDOM.createRoot(document.getElementById('root'));
    root.render(
      <React.StrictMode>
        <App />
      </React.StrictMode>
    );
  </script>
</body>
</html>