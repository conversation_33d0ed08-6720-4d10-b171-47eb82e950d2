Write-Host "Testing login endpoint..."
$loginBody = @{
    email    = "<EMAIL>"
    password = "admin123"
} | ConvertTo-Json

$response = Invoke-WebRequest -Uri "http://localhost:3003/api/auth/login" `
    -Method Post `
    -ContentType "application/json" `
    -Body $loginBody

Write-Host "Response:"
$response.Content

# Store the token if login successful
$token = ($response.Content | ConvertFrom-Json).token

if ($token) {
    Write-Host "`nTesting protected endpoint (users list)..."
    $headers = @{
        "Authorization" = "Bearer $token"
        "Content-Type"  = "application/json"
    }

    $usersResponse = Invoke-WebRequest -Uri "http://localhost:3003/api/users" `
        -Method Get `
        -Headers $headers

    Write-Host "Users Response:"
    $usersResponse.Content
}
