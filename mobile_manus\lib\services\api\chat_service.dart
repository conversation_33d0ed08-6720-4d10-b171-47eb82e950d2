import 'package:dio/dio.dart';
import 'package:mobile_manus/models/chat_message.dart';
import 'package:mobile_manus/models/chat_thread.dart';

class ChatService {
  final Dio _dio = Dio();  final String baseUrl = 'http://192.168.1.102:3003/api';

  ChatService() {
    _dio.options.baseUrl = baseUrl;
    _dio.interceptors.add(InterceptorsWrapper(
      onRequest: (options, handler) {
        // Add auth token
        // options.headers['Authorization'] = 'Bearer YOUR_TOKEN_HERE';
        return handler.next(options);
      },
      onError: (DioException e, handler) {
        print('ChatService Error: ${e.message}');
        return handler.next(e);
      },
    ));
  }

  Future<List<ChatThread>> getChatThreads() async {
    try {
      final response = await _dio.get('/chat/threads');
      return (response.data as List)
          .map((json) => ChatThread.fromJson(json))
          .toList();
    } catch (e) {
      throw Exception('Failed to load chat threads: $e');
    }
  }

  Future<List<ChatMessage>> getChatMessages(int threadId) async {
    try {
      final response = await _dio.get('/chat/threads/$threadId/messages');
      return (response.data as List)
          .map((json) => ChatMessage.fromJson(json))
          .toList();
    } catch (e) {
      throw Exception('Failed to load chat messages: $e');
    }
  }

  Future<ChatMessage> sendMessage(int threadId, String content) async {
    try {
      final response = await _dio.post(
        '/chat/threads/$threadId/messages',
        data: {'content': content},
      );
      return ChatMessage.fromJson(response.data);
    } catch (e) {
      throw Exception('Failed to send message: $e');
    }
  }
}
