{"version": 3, "file": "LoginAsUser.BJn28_kZ.js", "sources": ["../../src/pages/admin/LoginAsUser.jsx"], "sourcesContent": ["import React, { useState, useEffect } from 'react';\r\nimport {\r\n  Box,\r\n  Typography,\r\n  Paper,\r\n  TextField,\r\n  Button,\r\n  CircularProgress,\r\n  Alert,\r\n  Autocomplete,\r\n  Avatar,\r\n  ListItem,\r\n  ListItemAvatar,\r\n  ListItemText,\r\n  Chip\r\n} from '@mui/material';\r\nimport { Login as LoginIcon, AdminPanelSettings as AdminPanelSettingsIcon, Person as PersonIcon } from '@mui/icons-material';\r\nimport axiosInstance from '../../utils/axiosConfig'; // Assurez-vous que le chemin est correct\r\nimport { useAuth } from '../../contexts/AuthContext'; // Contexte d'authentification\r\n\r\nconst LoginAsUser = () => {\r\n  const [users, setUsers] = useState([]);\r\n  const [selectedUser, setSelectedUser] = useState(null);\r\n  const [loadingUsers, setLoadingUsers] = useState(true);\r\n  const [loggingIn, setLoggingIn] = useState(false);\r\n  const [error, setError] = useState('');\r\n  const [success, setSuccess] = useState('');\r\n  const { user: adminUser, loginAs, logoutAs } = useAuth(); // Fonctions du contexte d'auth\r\n\r\n  useEffect(() => {\r\n    const fetchUsers = async () => {\r\n      try {\r\n        setLoadingUsers(true);\r\n        // Endpoint pour récupérer tous les utilisateurs (sauf l'admin actuel)\r\n        const response = await axiosInstance.get('/api/users?exclude_self=true'); \r\n        setUsers(response.data || []);\r\n        setError('');\r\n      } catch (err) {\r\n        setError(err.response?.data?.message || \"Erreur lors de la récupération des utilisateurs.\");\r\n        console.error(\"Erreur fetchUsers:\", err);\r\n      } finally {\r\n        setLoadingUsers(false);\r\n      }\r\n    };\r\n    if (adminUser?.role === 'Administrateur') { // S'assurer que seul l'admin peut voir ça\r\n        fetchUsers();\r\n    }\r\n  }, [adminUser]);\r\n\r\n  const handleUserSelect = (event, newValue) => {\r\n    setSelectedUser(newValue);\r\n  };\r\n\r\n  const handleLoginAs = async () => {\r\n    if (!selectedUser) {\r\n      setError(\"Veuillez sélectionner un utilisateur.\");\r\n      return;\r\n    }\r\n    setLoggingIn(true);\r\n    setError('');\r\n    setSuccess('');\r\n    try {\r\n      // L'appel API backend doit générer un token temporaire pour l'utilisateur cible\r\n      // et potentiellement stocker l'état de \"login as\" côté serveur.\r\n      const response = await axiosInstance.post(`/admin/login-as-user/${selectedUser._id}`);\r\n      const { token, user: targetUser, originalAdminToken, impersonationExpiresAt } = response.data;\r\n      // Utiliser la fonction du contexte d'authentification pour gérer le changement d'utilisateur\r\n      loginAs(targetUser, token, originalAdminToken, impersonationExpiresAt);\r\n\r\n      setSuccess(`Vous êtes maintenant connecté en tant que ${targetUser.nom} ${targetUser.prenom}.`);\r\n      // Redirection ou mise à jour de l'UI gérée par le contexte AuthProvider\r\n    } catch (err) {\r\n      setError(err.response?.data?.message || \"Erreur lors de la tentative de connexion en tant qu'utilisateur.\");\r\n      console.error(\"Erreur handleLoginAs:\", err);\r\n    } finally {\r\n      setLoggingIn(false);\r\n    }\r\n  };\r\n  \r\n  // Si l'admin est déjà en mode \"login as\"\r\n  if (adminUser && adminUser.isImpersonating) {\r\n    return (\r\n      <Paper elevation={3} sx={{ p: 4, m: 2, textAlign: 'center' }}>\r\n        <AdminPanelSettingsIcon sx={{ fontSize: 60, color: 'primary.main', mb: 2 }} />\r\n        <Typography variant=\"h5\" gutterBottom>\r\n          Mode \"Connexion en tant que\" Actif\r\n        </Typography>\r\n        <Typography variant=\"body1\" sx={{ mb: 2 }}>\r\n          Vous naviguez actuellement en tant que <Chip label={`${adminUser.nom} ${adminUser.prenom} (${adminUser.role})`} color=\"secondary\" />.\r\n        </Typography>\r\n        <Typography variant=\"caption\" display=\"block\" sx={{ mb: 3 }}>\r\n          La session expirera automatiquement ou vous pouvez revenir à votre compte administrateur.\r\n        </Typography>\r\n        <Button \r\n          variant=\"contained\" \r\n          color=\"error\"\r\n          onClick={() => logoutAs()} // Fonction pour arrêter l'impersonnalisation\r\n          startIcon={<PersonIcon />}\r\n        >\r\n          Revenir à mon compte Administrateur\r\n        </Button>\r\n      </Paper>\r\n    );\r\n  }\r\n\r\n  return (\r\n    <Paper elevation={3} sx={{ p: 4, m: 2 }}>\r\n      <Typography variant=\"h5\" gutterBottom sx={{ mb: 3, display: 'flex', alignItems: 'center' }}>\r\n        <AdminPanelSettingsIcon sx={{ mr: 1 }} /> Se Connecter en tant qu'Utilisateur\r\n      </Typography>\r\n      {error && <Alert severity=\"error\" sx={{ mb: 2 }}>{error}</Alert>}\r\n      {success && <Alert severity=\"success\" sx={{ mb: 2 }}>{success}</Alert>}\r\n\r\n      <Typography variant=\"body2\" sx={{mb:2}} color=\"text.secondary\">\r\n        Cette fonctionnalité permet à un administrateur de se connecter temporairement au compte d'un autre utilisateur pour des raisons de support ou de vérification. Toutes les actions effectuées seront journalisées.\r\n      </Typography>\r\n\r\n      <Autocomplete\r\n        options={users}\r\n        getOptionLabel={(option) => `${option.nom} ${option.prenom} (${option.email}) - ${option.role}`}\r\n        value={selectedUser}\r\n        onChange={handleUserSelect}\r\n        loading={loadingUsers}\r\n        isOptionEqualToValue={(option, value) => option._id === value._id}\r\n        renderOption={(props, option) => (\r\n          <ListItem {...props} key={option._id}>\r\n            <ListItemAvatar>\r\n              <Avatar src={option.photoDeProfil || undefined} >{option.nom?.[0]}{option.prenom?.[0]}</Avatar>\r\n            </ListItemAvatar>\r\n            <ListItemText \r\n                primary={`${option.nom} ${option.prenom}`} \r\n                secondary={`${option.email} - ${option.role}`}\r\n            />\r\n          </ListItem>\r\n        )}\r\n        renderInput={(params) => (\r\n          <TextField\r\n            {...params}\r\n            label=\"Sélectionner un utilisateur\"\r\n            variant=\"outlined\"\r\n            InputProps={{\r\n              ...params.InputProps,\r\n              endAdornment: (\r\n                <React.Fragment>\r\n                  {loadingUsers ? <CircularProgress color=\"inherit\" size={20} /> : null}\r\n                  {params.InputProps.endAdornment}\r\n                </React.Fragment>\r\n              ),\r\n            }}\r\n          />\r\n        )}\r\n        sx={{ mb: 3 }}\r\n      />\r\n\r\n      <Button\r\n        variant=\"contained\"\r\n        color=\"primary\"\r\n        startIcon={<LoginIcon />}\r\n        onClick={handleLoginAs}\r\n        disabled={!selectedUser || loggingIn || loadingUsers}\r\n        fullWidth\r\n      >\r\n        {loggingIn ? <CircularProgress size={24} color=\"inherit\" /> : `Se connecter en tant que ${selectedUser ? selectedUser.nom : '...'}`}\r\n      </Button>\r\n    </Paper>\r\n  );\r\n};\r\n\r\nexport default LoginAsUser;"], "names": ["LoginAsUser", "users", "setUsers", "useState", "selected<PERSON>ser", "setSelectedUser", "loadingUsers", "setLoadingUsers", "loggingIn", "setLoggingIn", "error", "setError", "success", "setSuccess", "adminUser", "loginAs", "logoutAs", "useAuth", "useEffect", "fetchUsers", "response", "axiosInstance", "err", "handleUserSelect", "event", "newValue", "handleLoginAs", "token", "targetUser", "originalAdminToken", "impersonationExpiresAt", "jsxs", "Paper", "jsx", "AdminPanelSettingsIcon", "Typography", "Chip", "<PERSON><PERSON>", "PersonIcon", "<PERSON><PERSON>", "Autocomplete", "option", "value", "props", "createElement", "ListItem", "ListItemAvatar", "Avatar", "ListItemText", "params", "TextField", "React", "CircularProgress", "LoginIcon"], "mappings": "4QAoBA,MAAMA,EAAc,IAAM,CACxB,KAAM,CAACC,EAAOC,CAAQ,EAAIC,EAAAA,SAAS,CAAA,CAAE,EAC/B,CAACC,EAAcC,CAAe,EAAIF,EAAAA,SAAS,IAAI,EAC/C,CAACG,EAAcC,CAAe,EAAIJ,EAAAA,SAAS,EAAI,EAC/C,CAACK,EAAWC,CAAY,EAAIN,EAAAA,SAAS,EAAK,EAC1C,CAACO,EAAOC,CAAQ,EAAIR,EAAAA,SAAS,EAAE,EAC/B,CAACS,EAASC,CAAU,EAAIV,EAAAA,SAAS,EAAE,EACnC,CAAE,KAAMW,EAAW,QAAAC,EAAS,SAAAC,CAAA,EAAaC,EAAA,EAE/CC,EAAAA,UAAU,IAAM,CACd,MAAMC,EAAa,SAAY,CAC7B,GAAI,CACFZ,EAAgB,EAAI,EAEpB,MAAMa,EAAW,MAAMC,EAAc,IAAI,8BAA8B,EACvEnB,EAASkB,EAAS,MAAQ,EAAE,EAC5BT,EAAS,EAAE,CAAA,OACJW,EAAK,CACZX,EAASW,EAAI,UAAU,MAAM,SAAW,kDAAkD,EAC1F,QAAQ,MAAM,qBAAsBA,CAAG,CAAA,QACzC,CACEf,EAAgB,EAAK,CAAA,CACvB,EAEEO,GAAW,OAAS,kBACpBK,EAAA,CACJ,EACC,CAACL,CAAS,CAAC,EAEd,MAAMS,EAAmB,CAACC,EAAOC,IAAa,CAC5CpB,EAAgBoB,CAAQ,CAAA,EAGpBC,EAAgB,SAAY,CAChC,GAAI,CAACtB,EAAc,CACjBO,EAAS,uCAAuC,EAChD,MAAA,CAEFF,EAAa,EAAI,EACjBE,EAAS,EAAE,EACXE,EAAW,EAAE,EACb,GAAI,CAGF,MAAMO,EAAW,MAAMC,EAAc,KAAK,wBAAwBjB,EAAa,GAAG,EAAE,EAC9E,CAAE,MAAAuB,EAAO,KAAMC,EAAY,mBAAAC,EAAoB,uBAAAC,CAAA,EAA2BV,EAAS,KAEzFL,EAAQa,EAAYD,EAAOE,EAAoBC,CAAsB,EAErEjB,EAAW,6CAA6Ce,EAAW,GAAG,IAAIA,EAAW,MAAM,GAAG,CAAA,OAEvFN,EAAK,CACZX,EAASW,EAAI,UAAU,MAAM,SAAW,kEAAkE,EAC1G,QAAQ,MAAM,wBAAyBA,CAAG,CAAA,QAC5C,CACEb,EAAa,EAAK,CAAA,CACpB,EAIF,OAAIK,GAAaA,EAAU,gBAEvBiB,EAAAA,KAACC,EAAA,CAAM,UAAW,EAAG,GAAI,CAAE,EAAG,EAAG,EAAG,EAAG,UAAW,QAAA,EAChD,SAAA,CAAAC,EAAAA,IAACC,EAAA,CAAuB,GAAI,CAAE,SAAU,GAAI,MAAO,eAAgB,GAAI,CAAA,CAAE,CAAG,QAC3EC,EAAA,CAAW,QAAQ,KAAK,aAAY,GAAC,SAAA,qCAEtC,EACAJ,OAACI,GAAW,QAAQ,QAAQ,GAAI,CAAE,GAAI,GAAK,SAAA,CAAA,0CACFF,EAAAA,IAACG,EAAA,CAAK,MAAO,GAAGtB,EAAU,GAAG,IAAIA,EAAU,MAAM,KAAKA,EAAU,IAAI,IAAK,MAAM,YAAY,EAAE,GAAA,EACtI,EACAmB,EAAAA,IAACE,EAAA,CAAW,QAAQ,UAAU,QAAQ,QAAQ,GAAI,CAAE,GAAI,CAAA,EAAK,SAAA,2FAAA,CAE7D,EACAF,EAAAA,IAACI,EAAA,CACC,QAAQ,YACR,MAAM,QACN,QAAS,IAAMrB,EAAA,EACf,gBAAYsB,EAAA,EAAW,EACxB,SAAA,qCAAA,CAAA,CAED,EACF,EAKFP,OAACC,EAAA,CAAM,UAAW,EAAG,GAAI,CAAE,EAAG,EAAG,EAAG,CAAA,EAClC,SAAA,CAAAD,EAAAA,KAACI,EAAA,CAAW,QAAQ,KAAK,aAAY,GAAC,GAAI,CAAE,GAAI,EAAG,QAAS,OAAQ,WAAY,UAC9E,SAAA,CAAAF,EAAAA,IAACC,EAAA,CAAuB,GAAI,CAAE,GAAI,GAAK,EAAE,sCAAA,EAC3C,EACCxB,GAASuB,EAAAA,IAACM,EAAA,CAAM,SAAS,QAAQ,GAAI,CAAE,GAAI,CAAA,EAAM,SAAA7B,CAAA,CAAM,EACvDE,GAAWqB,EAAAA,IAACM,EAAA,CAAM,SAAS,UAAU,GAAI,CAAE,GAAI,CAAA,EAAM,SAAA3B,CAAA,CAAQ,EAE9DqB,EAAAA,IAACE,EAAA,CAAW,QAAQ,QAAQ,GAAI,CAAC,GAAG,CAAA,EAAI,MAAM,iBAAiB,SAAA,oNAAA,CAE/D,EAEAF,EAAAA,IAACO,EAAA,CACC,QAASvC,EACT,eAAiBwC,GAAW,GAAGA,EAAO,GAAG,IAAIA,EAAO,MAAM,KAAKA,EAAO,KAAK,OAAOA,EAAO,IAAI,GAC7F,MAAOrC,EACP,SAAUmB,EACV,QAASjB,EACT,qBAAsB,CAACmC,EAAQC,IAAUD,EAAO,MAAQC,EAAM,IAC9D,aAAc,CAACC,EAAOF,IACpBG,EAAAA,cAACC,EAAA,CAAU,GAAGF,EAAO,IAAKF,EAAO,GAAA,QAC9BK,EAAA,CACC,SAAAf,EAAAA,KAACgB,GAAO,IAAKN,EAAO,eAAiB,OAAa,SAAA,CAAAA,EAAO,MAAM,CAAC,EAAGA,EAAO,SAAS,CAAC,CAAA,CAAA,CAAE,EACxF,EACAR,EAAAA,IAACe,EAAA,CACG,QAAS,GAAGP,EAAO,GAAG,IAAIA,EAAO,MAAM,GACvC,UAAW,GAAGA,EAAO,KAAK,MAAMA,EAAO,IAAI,EAAA,CAAA,CAEjD,EAEF,YAAcQ,GACZhB,EAAAA,IAACiB,EAAA,CACE,GAAGD,EACJ,MAAM,8BACN,QAAQ,WACR,WAAY,CACV,GAAGA,EAAO,WACV,aACElB,EAAAA,KAACoB,EAAM,SAAN,CACE,SAAA,CAAA7C,QAAgB8C,EAAA,CAAiB,MAAM,UAAU,KAAM,GAAI,EAAK,KAChEH,EAAO,WAAW,YAAA,CAAA,CACrB,CAAA,CAEJ,CAAA,EAGJ,GAAI,CAAE,GAAI,CAAA,CAAE,CAAA,EAGdhB,EAAAA,IAACI,EAAA,CACC,QAAQ,YACR,MAAM,UACN,gBAAYgB,EAAA,EAAU,EACtB,QAAS3B,EACT,SAAU,CAACtB,GAAgBI,GAAaF,EACxC,UAAS,GAER,SAAAE,EAAYyB,EAAAA,IAACmB,EAAA,CAAiB,KAAM,GAAI,MAAM,SAAA,CAAU,EAAK,4BAA4BhD,EAAeA,EAAa,IAAM,KAAK,EAAA,CAAA,CACnI,EACF,CAEJ"}