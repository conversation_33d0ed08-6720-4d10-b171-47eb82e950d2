import 'package:flutter/material.dart';
import 'package:mobile_manus/models/user_rating.dart';
import 'package:mobile_manus/services/api/rating_service.dart';

class RatingProvider with ChangeNotifier {
  final RatingService _ratingService = RatingService();

  List<UserRating> _userRatings = [];

  List<UserRating> get userRatings => _userRatings;

  Future<void> fetchUserRatings(int userId) async {
    try {
      _userRatings = await _ratingService.getUserRatings(userId);
      notifyListeners();
    } catch (e) {
      print('Error fetching user ratings: $e');
    }
  }

  Future<void> submitRating(UserRating rating) async {
    try {
      final newRating = await _ratingService.submitRating(rating);
      _userRatings.add(newRating);
      notifyListeners();
    } catch (e) {
      print('Error submitting rating: $e');
    }
  }
}


