import 'package:flutter/material.dart';
import 'package:flutter/services.dart';
import 'package:flutter_riverpod/flutter_riverpod.dart';
import 'package:flutter_localizations/flutter_localizations.dart';
import 'package:flutter_gen/gen_l10n/app_localizations.dart';
import 'package:shared_preferences/shared_preferences.dart';

import 'utils/app_theme.dart';
import 'providers/auth_provider.dart';
import 'providers/finance_provider.dart';
import 'screens/auth/login_screen.dart';
import 'screens/dashboard/dashboard_screen.dart';
import 'screens/splash/splash_screen.dart';
import 'screens/finance/finance_dashboard_screen.dart';
import 'screens/finance/transactions_list_screen.dart';
import 'screens/finance/add_edit_transaction_screen.dart';
import 'screens/finance/budget_planning_screen.dart';
import 'screens/finance/financial_reports_screen.dart';

void main() async {
  WidgetsFlutterBinding.ensureInitialized();
  
  // Configuration du système UI pour l'usage terrain
  AppTheme.configureSystemUI();
  
  // Initialisation des préférences partagées
  await SharedPreferences.getInstance();
  
  runApp(
    const ProviderScope(
      child: PoultryDzApp(),
    ),
  );
}

class PoultryDzApp extends ConsumerWidget {
  const PoultryDzApp({super.key});

  @override
  Widget build(BuildContext context, WidgetRef ref) {
    return MaterialApp(
      title: 'Poultry DZ',
      debugShowCheckedModeBanner: false,
      
      // Configuration du thème
      theme: AppTheme.lightTheme,
      
      // Configuration de l'internationalisation
      localizationsDelegates: const [
        AppLocalizations.delegate,
        GlobalMaterialLocalizations.delegate,
        GlobalWidgetsLocalizations.delegate,
        GlobalCupertinoLocalizations.delegate,
      ],
      supportedLocales: const [
        Locale('fr', 'DZ'), // Français Algérie (principal)
        Locale('ar', 'DZ'), // Arabe Algérie
        Locale('fr', ''), // Français générique
        Locale('ar', ''), // Arabe générique
      ],
      locale: const Locale('fr', 'DZ'), // Langue par défaut
      
      // Configuration de la navigation
      home: const AppNavigator(),
      
      // Configuration des routes nommées
      routes: {
        '/login': (context) => const LoginScreen(),
        '/dashboard': (context) => const DashboardScreen(),
        '/finance': (context) => const FinanceDashboardScreen(farmId: 1), // TODO: Get farmId from context
        '/finance/transactions': (context) => const TransactionsListScreen(farmId: 1),
        '/finance/add-transaction': (context) => const AddEditTransactionScreen(farmId: 1),
        '/finance/budget': (context) => const BudgetPlanningScreen(farmId: 1),
        '/finance/reports': (context) => const FinancialReportsScreen(farmId: 1),
      },
      
      // Gestionnaire de routes inconnues
      onUnknownRoute: (settings) {
        return MaterialPageRoute(
          builder: (context) => const LoginScreen(),
        );
      },
      
      // Configuration du builder pour les erreurs
      builder: (context, child) {
        // Configuration de la densité de pixels pour l'usage terrain
        return MediaQuery(
          data: MediaQuery.of(context).copyWith(
            textScaleFactor: MediaQuery.of(context).textScaleFactor.clamp(0.8, 1.3),
          ),
          child: child!,
        );
      },
    );
  }
}

/// Navigateur principal de l'application
class AppNavigator extends ConsumerStatefulWidget {
  const AppNavigator({super.key});

  @override
  ConsumerState<AppNavigator> createState() => _AppNavigatorState();
}

class _AppNavigatorState extends ConsumerState<AppNavigator> {
  @override
  void initState() {
    super.initState();
    // Initialisation de l'authentification au démarrage
    WidgetsBinding.instance.addPostFrameCallback((_) {
      ref.read(authProvider.notifier).initialize();
    });
  }

  @override
  Widget build(BuildContext context) {
    final authState = ref.watch(authProvider);
    
    // Affichage de l'écran de chargement pendant l'initialisation
    if (!authState.isInitialized) {
      return const SplashScreen();
    }
    
    // Navigation basée sur l'état d'authentification
    if (authState.isAuthenticated && authState.user != null) {
      return const DashboardScreen();
    } else {
      return const LoginScreen();
    }
  }
}

/// Configuration globale de l'application
class AppConfig {
  static const String appName = 'Poultry DZ';
  static const String appVersion = '1.0.0';
  static const String apiBaseUrl = 'http://localhost:3003';
  
  // Configuration pour l'usage terrain
  static const Duration networkTimeout = Duration(seconds: 30);
  static const Duration syncInterval = Duration(minutes: 5);
  static const int maxRetryAttempts = 3;
  
  // Configuration de l'interface utilisateur
  static const double minTouchTargetSize = 48.0;
  static const double fieldOptimizedPadding = 16.0;
  static const double cardElevation = 2.0;
  
  // Configuration de la sécurité
  static const Duration tokenRefreshThreshold = Duration(minutes: 5);
  static const Duration biometricTimeout = Duration(seconds: 30);
  
  // Configuration du stockage local
  static const String authTokenKey = 'auth_token';
  static const String refreshTokenKey = 'refresh_token';
  static const String userDataKey = 'user_data';
  static const String languageKey = 'selected_language';
  static const String themeKey = 'selected_theme';
  static const String biometricEnabledKey = 'biometric_enabled';
  
  // Configuration des notifications
  static const String fcmTopicPrefix = 'poultry_dz_';
  static const String notificationChannelId = 'poultry_dz_notifications';
  static const String notificationChannelName = 'Poultry DZ Notifications';
  static const String notificationChannelDescription = 'Notifications pour l\'application Poultry DZ';
  
  // Configuration des rôles utilisateur
  static const List<String> userRoles = [
    'eleveur',
    'veterinaire', 
    'marchand',
    'admin',
  ];
  
  // Configuration des langues supportées
  static const Map<String, String> supportedLanguages = {
    'fr': 'Français',
    'ar': 'العربية',
  };
  
  // Configuration des permissions
  static const List<String> requiredPermissions = [
    'android.permission.INTERNET',
    'android.permission.ACCESS_NETWORK_STATE',
    'android.permission.CAMERA',
    'android.permission.WRITE_EXTERNAL_STORAGE',
    'android.permission.READ_EXTERNAL_STORAGE',
    'android.permission.ACCESS_FINE_LOCATION',
    'android.permission.ACCESS_COARSE_LOCATION',
    'android.permission.USE_BIOMETRIC',
    'android.permission.USE_FINGERPRINT',
  ];
  
  // Configuration des fonctionnalités
  static const bool enableBiometricAuth = true;
  static const bool enableOfflineMode = true;
  static const bool enablePushNotifications = true;
  static const bool enableLocationServices = true;
  static const bool enableCameraFeatures = true;
  static const bool enableAnalytics = false; // Désactivé pour la confidentialité
  
  // Configuration du développement
  static const bool isDebugMode = true;
  static const bool enableLogging = true;
  static const bool enablePerformanceMonitoring = true;
  
  /// Obtient l'URL complète de l'API
  static String getApiUrl(String endpoint) {
    return '$apiBaseUrl$endpoint';
  }
  
  /// Vérifie si une fonctionnalité est activée
  static bool isFeatureEnabled(String feature) {
    switch (feature) {
      case 'biometric':
        return enableBiometricAuth;
      case 'offline':
        return enableOfflineMode;
      case 'notifications':
        return enablePushNotifications;
      case 'location':
        return enableLocationServices;
      case 'camera':
        return enableCameraFeatures;
      case 'analytics':
        return enableAnalytics;
      default:
        return false;
    }
  }
  
  /// Obtient la configuration pour un rôle utilisateur
  static Map<String, dynamic> getRoleConfig(String role) {
    switch (role.toLowerCase()) {
      case 'eleveur':
        return {
          'primaryColor': 'green',
          'features': ['livestock', 'health', 'feeding', 'reports'],
          'dashboardLayout': 'farm_focused',
        };
      case 'veterinaire':
        return {
          'primaryColor': 'blue',
          'features': ['consultations', 'prescriptions', 'appointments', 'reports'],
          'dashboardLayout': 'medical_focused',
        };
      case 'marchand':
        return {
          'primaryColor': 'orange',
          'features': ['inventory', 'orders', 'sales', 'customers', 'reports'],
          'dashboardLayout': 'commerce_focused',
        };
      case 'admin':
        return {
          'primaryColor': 'purple',
          'features': ['users', 'system', 'analytics', 'settings', 'reports'],
          'dashboardLayout': 'admin_focused',
        };
      default:
        return {
          'primaryColor': 'green',
          'features': ['basic'],
          'dashboardLayout': 'default',
        };
    }
  }
}
