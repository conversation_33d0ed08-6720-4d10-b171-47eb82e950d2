# mobile_manus - Application Mobile de Gestion Avicole

Bienvenue dans le dépôt de l'application mobile **mobile_manus**, une solution complète pour la gestion des exploitations avicoles, conçue pour les éleveurs, les vétérinaires et les marchands.

## Table des Matières

- [Fonctionnalités](#fonctionnalités)
- [Technologies Utilisées](#technologies-utilisées)
- [Prérequis](#prérequis)
- [Installation et Configuration](#installation-et-configuration)
  - [C<PERSON><PERSON> le Dépôt](#cloner-le-dépôt)
  - [Configuration de l'Environnement Flutter](#configuration-de-lenvironnement-flutter)
  - [Installation des Dépendances](#installation-des-dépendances)
  - [Configuration du Backend](#configuration-du-backend)
- [Développement avec VS Code](#développement-avec-vs-code)
- [Exécution de l'Application](#exécution-de-lapplication)
- [Changer le Nom de l'Application](#changer-le-nom-de-lapplication)
- [Internationalisation](#internationalisation)
- [Structure du Projet](#structure-du-projet)
- [Tests](#tests)
- [Déploiement](#déploiement)
- [Contribuer](#contribuer)
- [Licence](#licence)

## Fonctionnalités

L'application mobile_manus offre une gamme de fonctionnalités adaptées aux différents rôles :

- **Système d'Authentification Avancé** : Connexion sécurisée, gestion des sessions, persistance des données utilisateur.
- **Tableaux de Bord Spécialisés** :
  - **Éleveur** : Suivi des volailles, production, consommation d'aliments, ventes.
  - **Vétérinaire** : Gestion des consultations, prescriptions, patients, alertes sanitaires.
  - **Marchand** : Suivi des produits, commandes, stocks, analyses de ventes.
- **Gestion des Aliments** : Suivi détaillé des stocks, plans d'alimentation, analyse de consommation.
- **Marketplace Intégré** : Plateforme d'achat/vente d'animaux, d'aliments et d'équipements, avec recherche, filtres et gestion des annonces.
- **Synchronisation Hors Ligne** : Accès et modification des données sans connexion internet, avec synchronisation automatique dès le retour en ligne.
- **Services API Spécialisés** : Intégration robuste avec le backend pour chaque rôle.
- **Internationalisation** : Support multilingue (Arabe, Français, Anglais) avec gestion du sens d'écriture (RTL).

## Technologies Utilisées

- **Flutter 3.24.5** : Framework de développement d'applications mobiles multiplateformes.
- **Dart** : Langage de programmation.
- **Provider** : Pour la gestion d'état.
- **Dio** : Client HTTP pour les requêtes réseau.
- **sqflite** : Base de données SQLite pour le stockage local.
- **shared_preferences** : Pour la persistance des données simples.
- **connectivity_plus** : Pour la détection de la connectivité réseau.
- **workmanager** : Pour les tâches en arrière-plan.
- **fl_chart** : Pour les visualisations de données.

## Prérequis

Avant de commencer, assurez-vous d'avoir les éléments suivants installés sur votre machine :

- [**Flutter SDK**](https://flutter.dev/docs/get-started/install)
- [**VS Code**](https://code.visualstudio.com/download) (ou votre IDE Flutter préféré)
- [**Extensions Flutter et Dart pour VS Code**](https://marketplace.visualstudio.com/items?itemName=Dart-Code.flutter)
- [**Git**](https://git-scm.com/downloads)
- **Un backend fonctionnel** : L'application est conçue pour interagir avec un serveur backend. Assurez-vous que votre backend est opérationnel et accessible.

## Installation et Configuration

Suivez ces étapes pour configurer et exécuter l'application sur votre machine locale.

### Cloner le Dépôt

Ouvrez votre terminal ou invite de commande et exécutez la commande suivante :

```bash
git clone https://github.com/Poultraydz/Poultraydz-Trae.git
cd Poultraydz-Trae/mobile_manus
```

### Configuration de l'Environnement Flutter

Assurez-vous que votre environnement Flutter est correctement configuré en exécutant :

```bash
flutter doctor
```

Si `flutter doctor` signale des problèmes, suivez les instructions pour les résoudre.

### Installation des Dépendances

Une fois dans le répertoire `mobile_manus`, installez les dépendances du projet :

```bash
flutter pub get
```

### Configuration du Backend

L'application communique avec un backend via des appels API. Vous devrez configurer l'URL de base de votre API.

1. **Localisez les services API** : Les services API sont définis dans le dossier `lib/services/api/`.
   - `auth_service.dart`
   - `eleveur_service.dart`
   - `marchand_service.dart`
   - `marketplace_service.dart`
   - `veterinaire_service.dart`

2. **Modifiez l'URL de base** : Dans chaque fichier de service (par exemple, `auth_service.dart`), vous trouverez une ligne similaire à celle-ci :

   ```dart
   final String baseUrl = 'http://localhost:3000/api';
   ```

   Remplacez `'http://localhost:3000/api'` par l'URL de votre serveur backend. Si votre backend est en production, utilisez l'URL de production (par exemple, `'https://api.votre-domaine.com/api'`).

   **Exemple pour `auth_service.dart` :**

   ```dart
   // lib/services/api/auth_service.dart
   import 'package:dio/dio.dart';

   class AuthService {
     final Dio _dio = Dio();
     // Mettez à jour cette URL avec l'adresse de votre backend
     final String baseUrl = 'https://votre-backend-api.com/api'; 

     AuthService() {
       _dio.options.baseUrl = baseUrl;
       _dio.options.connectTimeout = const Duration(seconds: 5);
       _dio.options.receiveTimeout = const Duration(seconds: 3);
     }
     // ... le reste du code
   }
   ```

   Répétez cette étape pour tous les fichiers de service API mentionnés ci-dessus.

## Développement avec VS Code

VS Code est l'IDE recommandé pour le développement Flutter. Voici quelques étapes pour commencer :

1. **Ouvrir le Projet** : Ouvrez VS Code et sélectionnez `File > Open Folder...` (ou `Fichier > Ouvrir un dossier...`). Naviguez jusqu'au dossier `mobile_manus` que vous avez cloné et ouvrez-le.

2. **Exécuter `flutter pub get`** : VS Code devrait automatiquement vous inviter à exécuter `flutter pub get` si des dépendances sont manquantes. Si ce n'est pas le cas, ouvrez la palette de commandes (`Ctrl+Shift+P` ou `Cmd+Shift+P`) et tapez `Flutter: Get Packages`.

3. **Sélectionner un Appareil** : En bas à droite de la fenêtre VS Code, vous verrez un sélecteur d'appareil. Cliquez dessus et choisissez un émulateur Android, un simulateur iOS, ou un appareil physique connecté.

4. **Lancer le Débogage** : Pour exécuter l'application en mode débogage, appuyez sur `F5` ou allez dans le menu `Run > Start Debugging`.

## Exécution de l'Application

Pour exécuter l'application sur un appareil connecté ou un émulateur :

```bash
flutter run
```

Pour construire une version de production (APK pour Android, IPA pour iOS) :

```bash
flutter build apk --release
# ou pour iOS
flutter build ipa --release
```

## Changer le Nom de l'Application

Le nom de l'application (celui qui apparaît sous l'icône sur l'écran d'accueil de l'appareil) est configuré dans plusieurs fichiers :

- **Android** : Modifiez le fichier `android/app/src/main/AndroidManifest.xml`.
  Recherchez la balise `<application>` et modifiez l'attribut `android:label` :
  ```xml
  <application
      android:label=


"@string/app_name"
      android:icon="@mipmap/ic_launcher">
      ...
  </application>
  ```
  Ensuite, modifiez le fichier `android/app/src/main/res/values/strings.xml` et changez la valeur de `app_name` :
  ```xml
  <resources>
      <string name="app_name">Nouveau Nom de l'App</string>
  </resources>
  ```

- **iOS** : Modifiez le fichier `ios/Runner/Info.plist`.
  Recherchez la clé `CFBundleName` et modifiez sa valeur :
  ```xml
  <key>CFBundleName</key>
  <string>Nouveau Nom de l'App</string>
  ```

- **Général (pour Flutter)** : Le nom de l'application peut également être défini dans `pubspec.yaml` sous la clé `name`. Bien que cela ne change pas directement le nom affiché sur l'appareil, c'est une bonne pratique de le maintenir cohérent.
  ```yaml
  name: nouveau_nom_app
  ```

Après avoir effectué ces modifications, exécutez `flutter clean` puis `flutter pub get` et reconstruisez l'application.

## Internationalisation

L'application supporte plusieurs langues (Arabe, Français, Anglais) et la gestion du sens d'écriture (RTL pour l'arabe).

- **Fichiers de Localisation** : Les chaînes de caractères sont définies dans le dossier `lib/l10n/`.
  - `app_localizations_en.dart` (Anglais)
  - `app_localizations_fr.dart` (Français)
  - `app_localizations_ar.dart` (Arabe)
  - `app_localizations.dart` (Fichier de base généré)

- **Ajouter une Nouvelle Langue** :
  1. Créez un nouveau fichier `app_localizations_xx.dart` (où `xx` est le code de la langue, ex: `es` pour l'espagnol) dans `lib/l10n/`.
  2. Ajoutez les traductions pour toutes les clés existantes.
  3. Mettez à jour `lib/l10n/app_localizations.dart` si de nouvelles clés sont ajoutées.
  4. Assurez-vous que `l10n.yaml` est configuré pour générer les fichiers de localisation correctement.

## Structure du Projet

Le projet suit une architecture modulaire pour faciliter la maintenance et l'évolutivité :

```
mobile_manus/
├── android/             # Fichiers spécifiques à Android
├── ios/                 # Fichiers spécifiques à iOS
├── lib/                 # Code source de l'application Flutter
│   ├── models/          # Définitions des modèles de données (User, Eleveur, etc.)
│   ├── providers/       # Gestion d'état (Provider pattern)
│   ├── services/        # Logique métier et interaction avec les API/BDD
│   │   ├── api/         # Services pour les appels API REST
│   │   ├── database/    # Gestion de la base de données locale (SQLite)
│   │   └── sync/        # Logique de synchronisation hors ligne
│   ├── screens/         # Définition des écrans de l'application
│   │   ├── auth/        # Écrans d'authentification (login, register)
│   │   ├── dashboard/   # Tableaux de bord spécifiques aux rôles
│   │   ├── feed/        # Écrans de gestion des aliments
│   │   ├── marketplace/ # Écrans du marketplace
│   │   └── home/        # Écran d'accueil et navigation principale
│   ├── l10n/            # Fichiers d'internationalisation
│   └── main.dart        # Point d'entrée principal de l'application
├── pubspec.yaml         # Dépendances et métadonnées du projet
├── analysis_options.yaml# Options d'analyse de code Dart
├── l10n.yaml            # Configuration de la génération des localisations
├── README.md            # Ce fichier
├── RAPPORT_FINAL.md     # Rapport de développement détaillé
└── ... (autres fichiers de configuration)
```

## Tests

Pour exécuter les tests unitaires et widget :

```bash
flutter test
```

Des tests d'intégration et d'interface utilisateur devront être ajoutés pour une couverture complète.

## Déploiement

Pour préparer l'application au déploiement sur les stores (Google Play Store, Apple App Store) :

1. **Mettre à jour les informations de l'application** : Modifiez `pubspec.yaml` pour la version et le numéro de build.
2. **Générer les icônes et splash screen** : Utilisez des outils comme `flutter_launcher_icons` et `flutter_native_splash` pour générer les assets nécessaires.
3. **Build de production** :
   ```bash
   flutter build apk --release
   flutter build appbundle --release # Pour Google Play Store
   flutter build ipa --release       # Pour Apple App Store
   ```
4. **Signer l'application** : Suivez les guides officiels de Flutter pour la signature de votre application Android et iOS.

## Contribuer

Les contributions sont les bienvenues ! Si vous souhaitez contribuer à ce projet, veuillez suivre ces étapes :

1. Forker le dépôt.
2. Créer une nouvelle branche (`git checkout -b feature/nouvelle-fonctionnalite`).
3. Effectuer vos modifications et commiter (`git commit -am 'Ajouter une nouvelle fonctionnalité'`).
4. Pousser la branche (`git push origin feature/nouvelle-fonctionnalite`).
5. Créer une Pull Request.

## Licence

Ce projet est sous licence [MIT License](LICENSE).

