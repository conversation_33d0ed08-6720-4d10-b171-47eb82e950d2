enum FarmRole {
  eleveur("<PERSON><PERSON>eur"),
  veterinaire("Vé<PERSON><PERSON><PERSON>"),
  marchand("Marchand"),
  acheteur("Acheteur"),
  admin("Admin");

  final String displayName;
  const FarmRole(this.displayName);

  factory FarmRole.fromString(String role) {
    switch (role) {
      case "eleveur":
        return FarmRole.eleveur;
      case "veterinaire":
        return FarmRole.veterinaire;
      case "marchand":
        return FarmRole.marchand;
      case "acheteur":
        return FarmRole.acheteur;
      case "admin":
        return FarmRole.admin;
      default:
        throw Exception("Unknown FarmRole: $role");
    }
  }
}


