class FeedConsumptionLog {
  final int id;
  final int farmId;
  final DateTime date;
  final String feedType;
  final double quantity;
  final String unit;
  final String? notes;

  FeedConsumptionLog({
    required this.id,
    required this.farmId,
    required this.date,
    required this.feedType,
    required this.quantity,
    required this.unit,
    this.notes,
  });

  factory FeedConsumptionLog.fromJson(Map<String, dynamic> json) {
    return FeedConsumptionLog(
      id: json["id"],
      farmId: json["farm_id"],
      date: DateTime.parse(json["date"]),
      feedType: json["feed_type"],
      quantity: json["quantity"].toDouble(),
      unit: json["unit"],
      notes: json["notes"],
    );
  }

  Map<String, dynamic> toJson() {
    return {
      "id": id,
      "farm_id": farmId,
      "date": date.toIso8601String(),
      "feed_type": feedType,
      "quantity": quantity,
      "unit": unit,
      "notes": notes,
    };
  }
}


