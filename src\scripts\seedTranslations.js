const pool = require('../config/database');

async function seedBasicTranslations() {
  try {
    console.log('🌱 Seeding basic translations...');

    // Check if translations already exist
    const existingTranslations = await pool.query('SELECT COUNT(*) FROM translations');
    const count = existingTranslations.rows && existingTranslations.rows[0] ? parseInt(existingTranslations.rows[0].count) : 0;
    if (count > 0) {
      console.log('✅ Translations already exist, skipping seed');
      return;
    }

    // Basic translations for French
    const frenchTranslations = [
      { key: 'app.title', value: 'Poultray DZ', language: 'fr', category: 'general' },
      { key: 'welcome', value: 'Bienvenue', language: 'fr', category: 'general' },
      { key: 'nav.dashboard', value: 'Tableau de bord', language: 'fr', category: 'navigation' },
      { key: 'nav.volailles', value: 'Volailles', language: 'fr', category: 'navigation' },
      { key: 'nav.ventes', value: 'Ventes', language: 'fr', category: 'navigation' },
      { key: 'common.save', value: 'Enregistrer', language: 'fr', category: 'common' },
      { key: 'common.cancel', value: 'Annuler', language: 'fr', category: 'common' },
      { key: 'common.error', value: 'Erreur', language: 'fr', category: 'common' },
      { key: 'common.success', value: 'Succès', language: 'fr', category: 'common' }
    ];

    // Basic translations for Arabic
    const arabicTranslations = [
      { key: 'app.title', value: 'دواجن الجزائر', language: 'ar', category: 'general' },
      { key: 'welcome', value: 'مرحبا', language: 'ar', category: 'general' },
      { key: 'nav.dashboard', value: 'لوحة التحكم', language: 'ar', category: 'navigation' },
      { key: 'nav.volailles', value: 'الدواجن', language: 'ar', category: 'navigation' },
      { key: 'nav.ventes', value: 'المبيعات', language: 'ar', category: 'navigation' },
      { key: 'common.save', value: 'حفظ', language: 'ar', category: 'common' },
      { key: 'common.cancel', value: 'إلغاء', language: 'ar', category: 'common' },
      { key: 'common.error', value: 'خطأ', language: 'ar', category: 'common' },
      { key: 'common.success', value: 'نجاح', language: 'ar', category: 'common' }
    ];

    // Combine all translations
    const allTranslations = [...frenchTranslations, ...arabicTranslations];

    // Insert translations
    for (const translation of allTranslations) {
      await pool.query(
        `INSERT INTO translations (key, value, language, category, created_at, updated_at)
         VALUES ($1, $2, $3, $4, NOW(), NOW())`,
        [translation.key, translation.value, translation.language, translation.category]
      );
    }

    console.log(`✅ Successfully seeded ${allTranslations.length} translations`);
    console.log('   - French translations: ' + frenchTranslations.length);
    console.log('   - Arabic translations: ' + arabicTranslations.length);

  } catch (error) {
    console.error('❌ Error seeding translations:', error);
    throw error;
  }
}

// Run if called directly
if (require.main === module) {
  seedBasicTranslations()
    .then(() => {
      console.log('🎉 Translation seeding completed');
      process.exit(0);
    })
    .catch((error) => {
      console.error('💥 Translation seeding failed:', error);
      process.exit(1);
    });
}

module.exports = { seedBasicTranslations };
