class Veterinaire {
  final int id;
  final int userId;
  final String numeroOrdre;
  final List<String> specialites;
  final Map<String, dynamic> disponibilites;

  Veterinaire({
    required this.id,
    required this.userId,
    required this.numeroOrdre,
    required this.specialites,
    required this.disponibilites,
  });

  factory Veterinaire.fromJson(Map<String, dynamic> json) {
    return Veterinaire(
      id: json["id"],
      userId: json["user_id"],
      numeroOrdre: json["numero_ordre"],
      specialites: List<String>.from(json["specialites"]),
      disponibilites: Map<String, dynamic>.from(json["disponibilites"]),
    );
  }

  Map<String, dynamic> toJson() {
    return {
      "id": id,
      "user_id": userId,
      "numero_ordre": numeroOrdre,
      "specialites": specialites,
      "disponibilites": disponibilites,
    };
  }
}


