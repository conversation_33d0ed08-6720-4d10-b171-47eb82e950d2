import 'package:flutter/material.dart';
import 'package:shared_preferences/shared_preferences.dart';
import 'package:mobile_manus/services/api/auth_service.dart';
import 'package:mobile_manus/models/user.dart';

class AuthProvider with ChangeNotifier {
  User? _user;
  String? _token;
  bool _isLoading = false;

  User? get user => _user;
  String? get token => _token;
  bool get isAuthenticated => _user != null && _token != null;
  bool get isLoading => _isLoading;

  final AuthService _authService = AuthService();

  AuthProvider() {
    _loadStoredAuth();
  }

  Future<void> _loadStoredAuth() async {
    final prefs = await SharedPreferences.getInstance();
    final storedToken = prefs.getString('auth_token');
    final storedUserJson = prefs.getString('user_data');

    if (storedToken != null && storedUserJson != null) {
      // Validate token with backend
      final isValid = await _authService.validateToken(storedToken);
      if (isValid) {
        _token = storedToken;
        _user = User.fromJson(Map<String, dynamic>.from(
          Map.from(storedUserJson as Map)
        ));
        notifyListeners();
      } else {
        // Token is invalid, clear stored data
        await _clearStoredAuth();
      }
    }
  }

  Future<void> _saveAuth(String token, User user) async {
    final prefs = await SharedPreferences.getInstance();
    await prefs.setString('auth_token', token);
    await prefs.setString('user_data', user.toJson().toString());
  }

  Future<void> _clearStoredAuth() async {
    final prefs = await SharedPreferences.getInstance();
    await prefs.remove('auth_token');
    await prefs.remove('user_data');
  }

  Future<void> login(String email, String password) async {
    _isLoading = true;
    notifyListeners();

    try {
      final response = await _authService.login(email, password);
      
      if (response['token'] != null && response['user'] != null) {
        _token = response['token'];
        _user = User.fromJson(response['user']);
        
        // Save authentication data locally
        await _saveAuth(_token!, _user!);
        
        _isLoading = false;
        notifyListeners();
      } else {
        throw Exception('Invalid response format');
      }
    } catch (e) {
      _user = null;
      _token = null;
      _isLoading = false;
      notifyListeners();
      rethrow;
    }
  }

  Future<void> register(Map<String, dynamic> userData) async {
    _isLoading = true;
    notifyListeners();

    try {
      final response = await _authService.register(userData);
      
      if (response['token'] != null && response['user'] != null) {
        _token = response['token'];
        _user = User.fromJson(response['user']);
        
        // Save authentication data locally
        await _saveAuth(_token!, _user!);
        
        _isLoading = false;
        notifyListeners();
      } else {
        throw Exception('Invalid response format');
      }
    } catch (e) {
      _user = null;
      _token = null;
      _isLoading = false;
      notifyListeners();
      rethrow;
    }
  }

  Future<void> logout() async {
    if (_token != null) {
      await _authService.logout(_token!);
    }
    
    _user = null;
    _token = null;
    await _clearStoredAuth();
    notifyListeners();
  }

  String? getUserRole() {
    return _user?.role;
  }

  bool hasRole(String role) {
    return _user?.role == role;
  }

  bool hasAnyRole(List<String> roles) {
    return _user != null && roles.contains(_user!.role);
  }
}

