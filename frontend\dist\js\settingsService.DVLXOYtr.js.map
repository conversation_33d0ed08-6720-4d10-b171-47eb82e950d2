{"version": 3, "file": "settingsService.DVLXOYtr.js", "sources": ["../../src/services/settingsService.js"], "sourcesContent": ["import axiosInstance from '../utils/axiosConfig';\r\n\r\nconst BASE_URL = '/admin/settings';\r\n\r\n// SMTP Settings\r\nconst fetchSmtpSettings = async () => {\r\n  try {\r\n    const response = await axiosInstance.get(`${BASE_URL}/smtp`);\r\n    return response.data;\r\n  } catch (error) {\r\n    console.error('Error fetching SMTP settings:', error);\r\n    throw error;\r\n  }\r\n};\r\n\r\n// Alias for fetchSmtpSettings\r\nconst getSmtpConfig = async () => {\r\n  return fetchSmtpSettings();\r\n};\r\n\r\nconst updateSmtpSettings = async (settings) => {\r\n  try {\r\n    const response = await axiosInstance.post(`${BASE_URL}/smtp`, settings);\r\n    return response.data;\r\n  } catch (error) {\r\n    console.error('Error updating SMTP settings:', error);\r\n    throw error;\r\n  }\r\n};\r\n\r\n// Security Settings\r\nconst fetchSecuritySettings = async () => {\r\n  try {\r\n    const response = await axiosInstance.get(`${BASE_URL}/security`);\r\n    return response.data;\r\n  } catch (error) {\r\n    console.error('Error fetching security settings:', error);\r\n    throw error;\r\n  }\r\n};\r\n\r\n// Alias for fetchSecuritySettings for new components\r\nconst getSecuritySettings = async () => {\r\n  return fetchSecuritySettings();\r\n};\r\n\r\nconst updateSecuritySettings = async (settings) => {\r\n  try {\r\n    const response = await axiosInstance.post(`${BASE_URL}/security`, settings);\r\n    return response.data;\r\n  } catch (error) {\r\n    console.error('Error updating security settings:', error);\r\n    throw error;\r\n  }\r\n};\r\n\r\n// General Settings\r\nconst fetchGeneralSettings = async () => {\r\n  try {\r\n    const response = await axiosInstance.get(`${BASE_URL}/general`);\r\n    return response.data;\r\n  } catch (error) {\r\n    console.error('Error fetching general settings:', error);\r\n    throw error;\r\n  }\r\n};\r\n\r\nconst updateGeneralSettings = async (settings) => {\r\n  try {\r\n    const response = await axiosInstance.post(`${BASE_URL}/general`, settings);\r\n    return response.data;\r\n  } catch (error) {\r\n    console.error('Error updating general settings:', error);\r\n    throw error;\r\n  }\r\n};\r\n\r\n// API Keys\r\nconst fetchApiKeys = async () => {\r\n  try {\r\n    const response = await axiosInstance.get(`${BASE_URL}/api-keys`);\r\n    return response.data;\r\n  } catch (error) {\r\n    console.error('Error fetching API keys:', error);\r\n    throw error;\r\n  }\r\n};\r\n\r\nconst updateApiKeys = async (apiKeys) => {\r\n  try {\r\n    const response = await axiosInstance.post(`${BASE_URL}/api-keys`, { apiKeys });\r\n    return response.data;\r\n  } catch (error) {\r\n    console.error('Error updating API keys:', error);\r\n    throw error;\r\n  }\r\n};\r\n\r\n// Test SMTP Configuration\r\nconst testSmtpConfig = async (config) => {\r\n  try {\r\n    const response = await axiosInstance.post(`${BASE_URL}/smtp/test`, config);\r\n    return response.data;\r\n  } catch (error) {\r\n    console.error('Error testing SMTP configuration:', error);\r\n    throw error;\r\n  }\r\n};\r\n\r\nconst settingsService = {\r\n  fetchSmtpSettings,\r\n  updateSmtpSettings,\r\n  fetchSecuritySettings,\r\n  getSecuritySettings,\r\n  updateSecuritySettings,\r\n  fetchGeneralSettings,\r\n  updateGeneralSettings,\r\n  fetchApiKeys,\r\n  updateApiKeys,\r\n  testSmtpConfig,\r\n  getSmtpConfig\r\n};\r\n\r\nexport default settingsService;\r\n"], "names": ["BASE_URL", "fetchSmtpSettings", "axiosInstance", "error", "getSmtpConfig", "updateSmtpSettings", "settings", "fetchSecuritySettings", "getSecuritySettings", "updateSecuritySettings", "fetchGeneralSettings", "updateGeneralSettings", "fetchApiKeys", "updateApiKeys", "<PERSON><PERSON><PERSON><PERSON><PERSON>", "testSmtpConfig", "config", "settingsService"], "mappings": "wCAEA,MAAMA,EAAW,kBAGXC,EAAoB,SAAY,CACpC,GAAI,CAEF,OADiB,MAAMC,EAAc,IAAI,GAAGF,CAAQ,OAAO,GAC3C,IAAA,OACTG,EAAO,CACd,cAAQ,MAAM,gCAAiCA,CAAK,EAC9CA,CAAA,CAEV,EAGMC,EAAgB,SACbH,EAAA,EAGHI,EAAqB,MAAOC,GAAa,CAC7C,GAAI,CAEF,OADiB,MAAMJ,EAAc,KAAK,GAAGF,CAAQ,QAASM,CAAQ,GACtD,IAAA,OACTH,EAAO,CACd,cAAQ,MAAM,gCAAiCA,CAAK,EAC9CA,CAAA,CAEV,EAGMI,EAAwB,SAAY,CACxC,GAAI,CAEF,OADiB,MAAML,EAAc,IAAI,GAAGF,CAAQ,WAAW,GAC/C,IAAA,OACTG,EAAO,CACd,cAAQ,MAAM,oCAAqCA,CAAK,EAClDA,CAAA,CAEV,EAGMK,EAAsB,SACnBD,EAAA,EAGHE,EAAyB,MAAOH,GAAa,CACjD,GAAI,CAEF,OADiB,MAAMJ,EAAc,KAAK,GAAGF,CAAQ,YAAaM,CAAQ,GAC1D,IAAA,OACTH,EAAO,CACd,cAAQ,MAAM,oCAAqCA,CAAK,EAClDA,CAAA,CAEV,EAGMO,EAAuB,SAAY,CACvC,GAAI,CAEF,OADiB,MAAMR,EAAc,IAAI,GAAGF,CAAQ,UAAU,GAC9C,IAAA,OACTG,EAAO,CACd,cAAQ,MAAM,mCAAoCA,CAAK,EACjDA,CAAA,CAEV,EAEMQ,EAAwB,MAAOL,GAAa,CAChD,GAAI,CAEF,OADiB,MAAMJ,EAAc,KAAK,GAAGF,CAAQ,WAAYM,CAAQ,GACzD,IAAA,OACTH,EAAO,CACd,cAAQ,MAAM,mCAAoCA,CAAK,EACjDA,CAAA,CAEV,EAGMS,EAAe,SAAY,CAC/B,GAAI,CAEF,OADiB,MAAMV,EAAc,IAAI,GAAGF,CAAQ,WAAW,GAC/C,IAAA,OACTG,EAAO,CACd,cAAQ,MAAM,2BAA4BA,CAAK,EACzCA,CAAA,CAEV,EAEMU,EAAgB,MAAOC,GAAY,CACvC,GAAI,CAEF,OADiB,MAAMZ,EAAc,KAAK,GAAGF,CAAQ,YAAa,CAAE,QAAAc,EAAS,GAC7D,IAAA,OACTX,EAAO,CACd,cAAQ,MAAM,2BAA4BA,CAAK,EACzCA,CAAA,CAEV,EAGMY,EAAiB,MAAOC,GAAW,CACvC,GAAI,CAEF,OADiB,MAAMd,EAAc,KAAK,GAAGF,CAAQ,aAAcgB,CAAM,GACzD,IAAA,OACTb,EAAO,CACd,cAAQ,MAAM,oCAAqCA,CAAK,EAClDA,CAAA,CAEV,EAEMc,EAAkB,CACtB,kBAAAhB,EACA,mBAAAI,EACA,sBAAAE,EACA,oBAAAC,EACA,uBAAAC,EACA,qBAAAC,EACA,sBAAAC,EACA,aAAAC,EACA,cAAAC,EACA,eAAAE,EACA,cAAAX,CACF"}