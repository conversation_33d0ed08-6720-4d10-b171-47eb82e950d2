import React from 'react';
import ReactDOM from 'react-dom/client';
import App from './App.jsx';
import './index.css';

console.log('🔧 main.jsx: Starting React app initialization...');

// Make sure the root element exists
const rootElement = document.getElementById('root');
if (!rootElement) {
  console.error('❌ Root element not found!');
  const root = document.createElement('div');
  root.id = 'root';
  document.body.appendChild(root);
  console.log('✅ Created root element');
}

const renderApp = () => {
  console.log('🎯 main.jsx: Rendering app...');
  try {
    const root = ReactDOM.createRoot(rootElement || document.getElementById('root'));

    root.render(
      <React.StrictMode>
        <App />
      </React.StrictMode>
    );
    console.log('✅ main.jsx: App rendered successfully');
  } catch (error) {
    console.error('❌ main.jsx: Error rendering app:', error);
  }
};

// Ensure the DOM is fully loaded before rendering
console.log('🔍 main.jsx: Document ready state:', document.readyState);
if (document.readyState === 'loading') {
  console.log('⏳ main.jsx: Waiting for DOM to load...');
  document.addEventListener('DOMContentLoaded', renderApp);
} else {
  console.log('🚀 main.jsx: DOM already loaded, rendering immediately...');
  renderApp();
}
