import React from 'react';
import ReactDOM from 'react-dom/client';
import { <PERSON><PERSON><PERSON><PERSON>outer as Router } from 'react-router-dom';
import { ThemeProvider } from '@mui/material/styles';
import { CssBaseline } from '@mui/material';
import { theme } from './theme';
import { LanguageProvider } from './contexts/LanguageContext';
import { AuthProvider } from './contexts/AuthContext';
import ErrorBoundary from './components/ErrorBoundary';
import App from './App.jsx';
import './index.css';

// Make sure the root element exists
const rootElement = document.getElementById('root');
if (!rootElement) {
  const root = document.createElement('div');
  root.id = 'root';
  document.body.appendChild(root);
}

const renderApp = () => {
  const root = ReactDOM.createRoot(rootElement || document.getElementById('root'));

  root.render(
    <React.StrictMode>
      <ErrorBoundary>
        <Router>
          <LanguageProvider>
            <AuthProvider>
              <ThemeProvider theme={theme}>
                <CssBaseline />
                <App />
              </ThemeProvider>
            </AuthProvider>
          </LanguageProvider>
        </Router>
      </ErrorBoundary>
    </React.StrictMode>
  );
};

// Ensure the DOM is fully loaded before rendering
if (document.readyState === 'loading') {
  document.addEventListener('DOMContentLoaded', renderApp);
} else {
  renderApp();
}
