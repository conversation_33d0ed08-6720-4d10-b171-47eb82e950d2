import 'package:flutter_riverpod/flutter_riverpod.dart';
import 'package:flutter/foundation.dart';
import '../models/user.dart';
import '../services/auth_service.dart';

// Auth service provider
final authServiceProvider = Provider<AuthService>((ref) {
  return AuthService();
});

// Auth state
class AuthState {
  final User? user;
  final bool isLoading;
  final bool isAuthenticated;
  final String? error;
  final bool biometricEnabled;
  final bool isInitialized;

  const AuthState({
    this.user,
    this.isLoading = false,
    this.isAuthenticated = false,
    this.error,
    this.biometricEnabled = false,
    this.isInitialized = false,
  });

  AuthState copyWith({
    User? user,
    bool? isLoading,
    bool? isAuthenticated,
    String? error,
    bool? biometricEnabled,
    bool? isInitialized,
    bool clearError = false,
    bool clearUser = false,
  }) {
    return AuthState(
      user: clearUser ? null : (user ?? this.user),
      isLoading: isLoading ?? this.isLoading,
      isAuthenticated: isAuthenticated ?? this.isAuthenticated,
      error: clearError ? null : (error ?? this.error),
      biometricEnabled: biometricEnabled ?? this.biometricEnabled,
      isInitialized: isInitialized ?? this.isInitialized,
    );
  }

  @override
  bool operator ==(Object other) {
    if (identical(this, other)) return true;
    return other is AuthState &&
        other.user == user &&
        other.isLoading == isLoading &&
        other.isAuthenticated == isAuthenticated &&
        other.error == error &&
        other.biometricEnabled == biometricEnabled &&
        other.isInitialized == isInitialized;
  }

  @override
  int get hashCode {
    return user.hashCode ^
        isLoading.hashCode ^
        isAuthenticated.hashCode ^
        error.hashCode ^
        biometricEnabled.hashCode ^
        isInitialized.hashCode;
  }

  @override
  String toString() {
    return 'AuthState(user: $user, isLoading: $isLoading, isAuthenticated: $isAuthenticated, error: $error, biometricEnabled: $biometricEnabled, isInitialized: $isInitialized)';
  }
}

// Auth notifier
class AuthNotifier extends StateNotifier<AuthState> {
  final AuthService _authService;

  AuthNotifier(this._authService) : super(const AuthState()) {
    _initialize();
  }

  // Initialize auth service
  Future<void> _initialize() async {
    try {
      state = state.copyWith(isLoading: true);
      
      await _authService.initialize();
      
      final user = _authService.currentUser;
      final isAuthenticated = _authService.isAuthenticated;
      final biometricEnabled = await _authService.isBiometricEnabled();
      
      state = state.copyWith(
        user: user,
        isAuthenticated: isAuthenticated,
        biometricEnabled: biometricEnabled,
        isInitialized: true,
        isLoading: false,
        clearError: true,
      );
    } catch (e) {
      if (kDebugMode) {
        print('Auth initialization error: $e');
      }
      state = state.copyWith(
        isLoading: false,
        isInitialized: true,
        error: 'Failed to initialize authentication',
      );
    }
  }

  // Login with email and password
  Future<bool> login(String email, String password, {bool rememberMe = false}) async {
    try {
      state = state.copyWith(isLoading: true, clearError: true);
      
      final authResponse = await _authService.login(email, password, rememberMe: rememberMe);
      
      state = state.copyWith(
        user: authResponse.user,
        isAuthenticated: true,
        isLoading: false,
      );
      
      return true;
    } catch (e) {
      state = state.copyWith(
        isLoading: false,
        error: e.toString().replaceFirst('Exception: ', ''),
      );
      return false;
    }
  }

  // Register new user
  Future<bool> register(RegisterRequest request) async {
    try {
      state = state.copyWith(isLoading: true, clearError: true);
      
      final authResponse = await _authService.register(request);
      
      state = state.copyWith(
        user: authResponse.user,
        isAuthenticated: true,
        isLoading: false,
      );
      
      return true;
    } catch (e) {
      state = state.copyWith(
        isLoading: false,
        error: e.toString().replaceFirst('Exception: ', ''),
      );
      return false;
    }
  }

  // Login with biometric
  Future<bool> loginWithBiometric() async {
    try {
      state = state.copyWith(isLoading: true, clearError: true);
      
      final authResponse = await _authService.loginWithBiometric();
      
      if (authResponse != null) {
        state = state.copyWith(
          user: authResponse.user,
          isAuthenticated: true,
          isLoading: false,
        );
        return true;
      } else {
        state = state.copyWith(
          isLoading: false,
          error: 'Biometric authentication failed',
        );
        return false;
      }
    } catch (e) {
      state = state.copyWith(
        isLoading: false,
        error: e.toString().replaceFirst('Exception: ', ''),
      );
      return false;
    }
  }

  // Enable biometric authentication
  Future<bool> enableBiometric(String email, String password) async {
    try {
      final success = await _authService.enableBiometric(email, password);
      
      if (success) {
        state = state.copyWith(biometricEnabled: true);
      }
      
      return success;
    } catch (e) {
      state = state.copyWith(
        error: 'Failed to enable biometric authentication',
      );
      return false;
    }
  }

  // Disable biometric authentication
  Future<void> disableBiometric() async {
    try {
      await _authService.disableBiometric();
      state = state.copyWith(biometricEnabled: false);
    } catch (e) {
      state = state.copyWith(
        error: 'Failed to disable biometric authentication',
      );
    }
  }

  // Logout
  Future<void> logout() async {
    try {
      state = state.copyWith(isLoading: true);
      
      await _authService.logout();
      
      state = state.copyWith(
        user: null,
        isAuthenticated: false,
        isLoading: false,
        clearError: true,
        clearUser: true,
      );
    } catch (e) {
      state = state.copyWith(
        isLoading: false,
        error: 'Failed to logout',
      );
    }
  }

  // Update profile
  Future<bool> updateProfile(Map<String, dynamic> updates) async {
    try {
      state = state.copyWith(isLoading: true, clearError: true);
      
      final updatedUser = await _authService.updateProfile(updates);
      
      state = state.copyWith(
        user: updatedUser,
        isLoading: false,
      );
      
      return true;
    } catch (e) {
      state = state.copyWith(
        isLoading: false,
        error: e.toString().replaceFirst('Exception: ', ''),
      );
      return false;
    }
  }

  // Change password
  Future<bool> changePassword(String currentPassword, String newPassword) async {
    try {
      state = state.copyWith(isLoading: true, clearError: true);
      
      await _authService.changePassword(currentPassword, newPassword);
      
      state = state.copyWith(isLoading: false);
      return true;
    } catch (e) {
      state = state.copyWith(
        isLoading: false,
        error: e.toString().replaceFirst('Exception: ', ''),
      );
      return false;
    }
  }

  // Forgot password
  Future<bool> forgotPassword(String email) async {
    try {
      state = state.copyWith(isLoading: true, clearError: true);
      
      await _authService.forgotPassword(email);
      
      state = state.copyWith(isLoading: false);
      return true;
    } catch (e) {
      state = state.copyWith(
        isLoading: false,
        error: e.toString().replaceFirst('Exception: ', ''),
      );
      return false;
    }
  }

  // Reset password
  Future<bool> resetPassword(String token, String newPassword) async {
    try {
      state = state.copyWith(isLoading: true, clearError: true);
      
      await _authService.resetPassword(token, newPassword);
      
      state = state.copyWith(isLoading: false);
      return true;
    } catch (e) {
      state = state.copyWith(
        isLoading: false,
        error: e.toString().replaceFirst('Exception: ', ''),
      );
      return false;
    }
  }

  // Clear error
  void clearError() {
    state = state.copyWith(clearError: true);
  }

  // Auto refresh token
  Future<void> autoRefreshToken() async {
    try {
      await _authService.autoRefreshToken();
      
      // Update user if token was refreshed
      final user = _authService.currentUser;
      if (user != null && user != state.user) {
        state = state.copyWith(user: user);
      }
    } catch (e) {
      if (kDebugMode) {
        print('Auto refresh token error: $e');
      }
      // If refresh fails, logout user
      await logout();
    }
  }

  // Check if should refresh token
  bool shouldRefreshToken() {
    return _authService.shouldRefreshToken();
  }
}

// Auth provider
final authProvider = StateNotifierProvider<AuthNotifier, AuthState>((ref) {
  final authService = ref.read(authServiceProvider);
  return AuthNotifier(authService);
});

// Computed providers
final currentUserProvider = Provider<User?>((ref) {
  return ref.watch(authProvider).user;
});

final isAuthenticatedProvider = Provider<bool>((ref) {
  return ref.watch(authProvider).isAuthenticated;
});

final isLoadingProvider = Provider<bool>((ref) {
  return ref.watch(authProvider).isLoading;
});

final authErrorProvider = Provider<String?>((ref) {
  return ref.watch(authProvider).error;
});

final biometricEnabledProvider = Provider<bool>((ref) {
  return ref.watch(authProvider).biometricEnabled;
});

final isInitializedProvider = Provider<bool>((ref) {
  return ref.watch(authProvider).isInitialized;
});

// User role providers
final isEleveurProvider = Provider<bool>((ref) {
  final user = ref.watch(currentUserProvider);
  return user?.isEleveur ?? false;
});

final isVeterinaireProvider = Provider<bool>((ref) {
  final user = ref.watch(currentUserProvider);
  return user?.isVeterinaire ?? false;
});

final isMarchandProvider = Provider<bool>((ref) {
  final user = ref.watch(currentUserProvider);
  return user?.isMarchand ?? false;
});

final isAdminProvider = Provider<bool>((ref) {
  final user = ref.watch(currentUserProvider);
  return user?.isAdmin ?? false;
});