class PriceData {
  final String region;
  final DateTime date;
  final double price;
  final String poultryType;

  PriceData({
    required this.region,
    required this.date,
    required this.price,
    required this.poultryType,
  });

  factory PriceData.fromJson(Map<String, dynamic> json) {
    return PriceData(
      region: json["region"],
      date: DateTime.parse(json["date"]),
      price: json["price"].toDouble(),
      poultryType: json["poultry_type"],
    );
  }

  Map<String, dynamic> toJson() {
    return {
      "region": region,
      "date": date.toIso8601String(),
      "price": price,
      "poultry_type": poultryType,
    };
  }
}


