const axios = require('axios');
const os = require('os');

async function testApiAccess() {
  // Get all network interfaces
  const interfaces = os.networkInterfaces();
  const networkAddresses = [];

  // Collect all IPv4 addresses
  Object.keys(interfaces).forEach((ifname) => {
    interfaces[ifname].forEach((iface) => {
      if (iface.family === 'IPv4' && !iface.internal) {
        networkAddresses.push(iface.address);
      }
    });
  });

  console.log('\n🔍 Testing API accessibility...');
  console.log('Network addresses found:', networkAddresses);

  // Test endpoints to check
  const endpoints = [
    '',              // Root endpoint
    '/api',          // API root
    '/api/health',   // Health check
    '/api-docs'      // Swagger documentation
  ];

  // Test each endpoint on each address
  for (const address of [...networkAddresses, 'localhost']) {
    console.log(`\n📡 Testing endpoints on ${address}:`);

    for (const endpoint of endpoints) {
      const url = `http://${address}:3003${endpoint}`;
      try {
        const start = Date.now();
        const response = await axios.get(url);
        const duration = Date.now() - start;

        console.log(`✅ ${url}`);
        console.log(`   Response time: ${duration}ms`);
        console.log(`   Status: ${response.status}`);
      } catch (error) {
        console.log(`❌ ${url}`);
        console.log(`   Error: ${error.message}`);
        if (error.response) {
          console.log(`   Status: ${error.response.status}`);
          console.log(`   Data:`, error.response.data);
        }
      }
    }
  }
}

// Run the test
testApiAccess().catch(console.error);
