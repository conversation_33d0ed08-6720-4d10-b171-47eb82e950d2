class FeedPlan {
  final int id;
  final int farmId;
  final DateTime startDate;
  final DateTime endDate;
  final double targetQuantity;
  final String frequency;
  final String status;
  final String? description;

  FeedPlan({
    required this.id,
    required this.farmId,
    required this.startDate,
    required this.endDate,
    required this.targetQuantity,
    required this.frequency,
    required this.status,
    this.description,
  });

  factory FeedPlan.fromJson(Map<String, dynamic> json) {
    return FeedPlan(
      id: json["id"],
      farmId: json["farm_id"],
      startDate: DateTime.parse(json["start_date"]),
      endDate: DateTime.parse(json["end_date"]),
      targetQuantity: json["target_quantity"].toDouble(),
      frequency: json["frequency"],
      status: json["status"],
      description: json["description"],
    );
  }

  Map<String, dynamic> toJson() {
    return {
      "id": id,
      "farm_id": farmId,
      "start_date": startDate.toIso8601String(),
      "end_date": endDate.toIso8601String(),
      "target_quantity": targetQuantity,
      "frequency": frequency,
      "status": status,
      "description": description,
    };
  }
}


