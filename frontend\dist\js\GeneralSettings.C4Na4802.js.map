{"version": 3, "file": "GeneralSettings.C4Na4802.js", "sources": ["../../src/pages/admin/GeneralSettings.jsx"], "sourcesContent": ["import React, { useState, useEffect } from 'react';\r\nimport {\r\n  Box,\r\n  Typography,\r\n  Paper,\r\n  TextField,\r\n  Button,\r\n  CircularProgress,\r\n  Alert,\r\n  Grid,\r\n  FormControlLabel,\r\n  Switch,\r\n  Divider,\r\n  InputAdornment,\r\n  MenuItem,\r\n  Snackbar\r\n} from '@mui/material';\r\nimport {\r\n  Save as SaveIcon,\r\n  Settings as SettingsIcon,\r\n  Language as LanguageIcon,\r\n  ColorLens as ColorLensIcon\r\n} from '@mui/icons-material';\r\nimport settingsService from '../../services/settingsService';\r\nimport { useLanguage } from '../../contexts/LanguageContext';\r\n\r\nfunction GeneralSettings() {\r\n  const { t, language, changeLanguage } = useLanguage();\r\n\r\n  const [settings, setSettings] = useState({\r\n    siteName: 'Poultray DZ',\r\n    siteDescription: '',\r\n    contactEmail: '',\r\n    contactPhone: '',\r\n    address: '',\r\n    logo: '',\r\n    favicon: '',\r\n    primaryColor: '#2c5530',\r\n    secondaryColor: '#e7eae2',\r\n    defaultLanguage: 'fr',\r\n    availableLanguages: ['fr', 'ar', 'en'],\r\n    dateFormat: 'DD/MM/YYYY',\r\n    timeFormat: 'HH:mm',\r\n    timezone: 'Africa/Algiers',\r\n    maintenanceMode: false,\r\n    maintenanceMessage: '',\r\n    allowUserRegistration: true,\r\n    defaultUserRole: 'user',\r\n    footerText: '© Poultray DZ',\r\n    maxUploadSize: 5,\r\n    socialLinks: {},\r\n  });\r\n\r\n  const [loading, setLoading] = useState(true);\r\n  const [saving, setSaving] = useState(false);\r\n  const [error, setError] = useState('');\r\n  const [success, setSuccess] = useState('');\r\n  const [toast, setToast] = useState({ open: false, message: '', severity: 'info' });\r\n\r\n  useEffect(() => {\r\n    const fetchSettings = async () => {\r\n      try {\r\n        setLoading(true);\r\n        const data = await settingsService.fetchGeneralSettings();\r\n        if (data) {\r\n          setSettings(prevSettings => ({\r\n            ...prevSettings,\r\n            ...data\r\n          }));\r\n        }\r\n        setError('');\r\n      } catch (err) {\r\n        setError(err.response?.data?.message || t('errors.fetchingGeneralSettings', 'Error fetching general settings'));\r\n        console.error(\"Error fetchGeneralSettings:\", err);\r\n      } finally {\r\n        setLoading(false);\r\n      }\r\n    };\r\n    fetchSettings();\r\n  }, [t]);\r\n\r\n  const handleChange = (e) => {\r\n    const { name, value, type, checked } = e.target;\r\n    setSettings(prev => ({\r\n      ...prev,\r\n      [name]: type === 'checkbox' || type === 'switch' ? checked : value\r\n    }));\r\n  };\r\n\r\n  const handleSubmit = async (e) => {\r\n    e.preventDefault();\r\n    setSaving(true);\r\n    setError('');\r\n    setSuccess('');\r\n\r\n    try {\r\n      const response = await settingsService.updateGeneralSettings(settings);\r\n      setSuccess(t('success.generalSettingsSaved', 'General settings saved successfully!'));\r\n      setToast({\r\n        open: true,\r\n        message: t('success.generalSettingsSaved', 'General settings saved successfully!'),\r\n        severity: 'success'\r\n      });\r\n    } catch (err) {\r\n      setError(err.response?.data?.message || t('errors.savingGeneralSettings', 'Error saving general settings'));\r\n      setToast({\r\n        open: true,\r\n        message: err.response?.data?.message || t('errors.savingGeneralSettings', 'Error saving general settings'),\r\n        severity: 'error'\r\n      });\r\n      console.error(\"Error handleSubmit:\", err);\r\n    } finally {\r\n      setSaving(false);\r\n    }\r\n  };\r\n\r\n  const handleCloseToast = () => {\r\n    setToast({ ...toast, open: false });\r\n  };\r\n\r\n  const timezones = [\r\n    'Africa/Algiers',\r\n    'Europe/Paris',\r\n    'Europe/London',\r\n    'America/New_York',\r\n    'Asia/Dubai',\r\n    'Asia/Tokyo',\r\n    'Australia/Sydney'\r\n  ];\r\n\r\n  const dateFormats = [\r\n    'DD/MM/YYYY',\r\n    'MM/DD/YYYY',\r\n    'YYYY-MM-DD',\r\n    'DD-MM-YYYY',\r\n    'DD.MM.YYYY'\r\n  ];\r\n\r\n  const timeFormats = [\r\n    'HH:mm',\r\n    'hh:mm A',\r\n    'HH:mm:ss'\r\n  ];\r\n\r\n  const languages = [\r\n    { code: 'fr', name: 'Français' },\r\n    { code: 'ar', name: 'العربية' },\r\n    { code: 'en', name: 'English' }\r\n  ];\r\n\r\n  if (loading) {\r\n    return (\r\n      <Box display=\"flex\" justifyContent=\"center\" alignItems=\"center\" minHeight=\"200px\">\r\n        <CircularProgress />\r\n      </Box>\r\n    );\r\n  }\r\n\r\n  return (\r\n    <Paper elevation={3} sx={{ p: 4, m: 2 }}>\r\n      <Typography variant=\"h5\" gutterBottom sx={{ mb: 3 }}>\r\n        {t('settings.general.title', 'General Settings')}\r\n      </Typography>\r\n\r\n      {error && <Alert severity=\"error\" sx={{ mb: 2 }}>{error}</Alert>}\r\n      {success && <Alert severity=\"success\" sx={{ mb: 2 }}>{success}</Alert>}\r\n\r\n      <form onSubmit={handleSubmit}>\r\n        <Typography variant=\"h6\" gutterBottom sx={{ mt: 2 }}>\r\n          {t('settings.general.siteIdentity', 'Site Identity')}\r\n        </Typography>\r\n        <Divider sx={{ mb: 3 }} />\r\n\r\n        <Grid container spacing={3}>\r\n          <Grid item xs={12} sm={6}>\r\n            <TextField\r\n              fullWidth\r\n              label={t('settings.general.siteName', 'Site Name')}\r\n              name=\"siteName\"\r\n              value={settings.siteName}\r\n              onChange={handleChange}\r\n              variant=\"outlined\"\r\n              required\r\n            />\r\n          </Grid>\r\n\r\n          <Grid item xs={12} sm={6}>\r\n            <TextField\r\n              fullWidth\r\n              label={t('settings.general.siteDescription', 'Site Description')}\r\n              name=\"siteDescription\"\r\n              value={settings.siteDescription}\r\n              onChange={handleChange}\r\n              variant=\"outlined\"\r\n              multiline\r\n              rows={2}\r\n            />\r\n          </Grid>\r\n\r\n          <Grid item xs={12} sm={6}>\r\n            <TextField\r\n              fullWidth\r\n              label={t('settings.general.contactEmail', 'Contact Email')}\r\n              name=\"contactEmail\"\r\n              value={settings.contactEmail}\r\n              onChange={handleChange}\r\n              variant=\"outlined\"\r\n              type=\"email\"\r\n            />\r\n          </Grid>\r\n\r\n          <Grid item xs={12} sm={6}>\r\n            <TextField\r\n              fullWidth\r\n              label={t('settings.general.contactPhone', 'Contact Phone')}\r\n              name=\"contactPhone\"\r\n              value={settings.contactPhone}\r\n              onChange={handleChange}\r\n              variant=\"outlined\"\r\n            />\r\n          </Grid>\r\n\r\n          <Grid item xs={12}>\r\n            <TextField\r\n              fullWidth\r\n              label={t('settings.general.address', 'Address')}\r\n              name=\"address\"\r\n              value={settings.address}\r\n              onChange={handleChange}\r\n              variant=\"outlined\"\r\n              multiline\r\n              rows={2}\r\n            />\r\n          </Grid>\r\n        </Grid>\r\n\r\n        <Typography variant=\"h6\" gutterBottom sx={{ mt: 4 }}>\r\n          {t('settings.general.appearance', 'Appearance')}\r\n        </Typography>\r\n        <Divider sx={{ mb: 3 }} />\r\n\r\n        <Grid container spacing={3}>\r\n          <Grid item xs={12} sm={6}>\r\n            <TextField\r\n              fullWidth\r\n              label={t('settings.general.logo', 'Logo URL')}\r\n              name=\"logo\"\r\n              value={settings.logo}\r\n              onChange={handleChange}\r\n              variant=\"outlined\"\r\n              placeholder=\"https://example.com/logo.png\"\r\n            />\r\n          </Grid>\r\n\r\n          <Grid item xs={12} sm={6}>\r\n            <TextField\r\n              fullWidth\r\n              label={t('settings.general.favicon', 'Favicon URL')}\r\n              name=\"favicon\"\r\n              value={settings.favicon}\r\n              onChange={handleChange}\r\n              variant=\"outlined\"\r\n              placeholder=\"https://example.com/favicon.ico\"\r\n            />\r\n          </Grid>\r\n\r\n          <Grid item xs={12} sm={6}>\r\n            <TextField\r\n              fullWidth\r\n              label={t('settings.general.primaryColor', 'Primary Color')}\r\n              name=\"primaryColor\"\r\n              value={settings.primaryColor}\r\n              onChange={handleChange}\r\n              variant=\"outlined\"\r\n              InputProps={{\r\n                startAdornment: (\r\n                  <InputAdornment position=\"start\">\r\n                    <Box\r\n                      sx={{\r\n                        width: 20,\r\n                        height: 20,\r\n                        backgroundColor: settings.primaryColor,\r\n                        borderRadius: '4px',\r\n                        border: '1px solid #ccc'\r\n                      }}\r\n                    />\r\n                  </InputAdornment>\r\n                ),\r\n              }}\r\n            />\r\n          </Grid>\r\n\r\n          <Grid item xs={12} sm={6}>\r\n            <TextField\r\n              fullWidth\r\n              label={t('settings.general.secondaryColor', 'Secondary Color')}\r\n              name=\"secondaryColor\"\r\n              value={settings.secondaryColor}\r\n              onChange={handleChange}\r\n              variant=\"outlined\"\r\n              InputProps={{\r\n                startAdornment: (\r\n                  <InputAdornment position=\"start\">\r\n                    <Box\r\n                      sx={{\r\n                        width: 20,\r\n                        height: 20,\r\n                        backgroundColor: settings.secondaryColor,\r\n                        borderRadius: '4px',\r\n                        border: '1px solid #ccc'\r\n                      }}\r\n                    />\r\n                  </InputAdornment>\r\n                ),\r\n              }}\r\n            />\r\n          </Grid>\r\n\r\n          <Grid item xs={12} sm={6}>\r\n            <TextField\r\n              fullWidth\r\n              label={t('settings.general.footerText', 'Footer Text')}\r\n              name=\"footerText\"\r\n              value={settings.footerText}\r\n              onChange={handleChange}\r\n              variant=\"outlined\"\r\n            />\r\n          </Grid>\r\n\r\n          <Grid item xs={12} sm={6}>\r\n            <TextField\r\n              fullWidth\r\n              label={t('settings.general.maxUploadSize', 'Max Upload Size (MB)')}\r\n              name=\"maxUploadSize\"\r\n              value={settings.maxUploadSize}\r\n              onChange={handleChange}\r\n              variant=\"outlined\"\r\n              type=\"number\"\r\n              InputProps={{\r\n                inputProps: { min: 1, max: 100 }\r\n              }}\r\n            />\r\n          </Grid>\r\n        </Grid>\r\n\r\n        <Typography variant=\"h6\" gutterBottom sx={{ mt: 4 }}>\r\n          {t('settings.general.localization', 'Localization')}\r\n        </Typography>\r\n        <Divider sx={{ mb: 3 }} />\r\n\r\n        <Grid container spacing={3}>\r\n          <Grid item xs={12} sm={6}>\r\n            <TextField\r\n              select\r\n              fullWidth\r\n              label={t('settings.general.defaultLanguage', 'Default Language')}\r\n              name=\"defaultLanguage\"\r\n              value={settings.defaultLanguage}\r\n              onChange={handleChange}\r\n              variant=\"outlined\"\r\n            >\r\n              {languages.map((lang) => (\r\n                <MenuItem key={lang.code} value={lang.code}>\r\n                  {lang.name}\r\n                </MenuItem>\r\n              ))}\r\n            </TextField>\r\n          </Grid>\r\n\r\n          <Grid item xs={12} sm={6}>\r\n            <TextField\r\n              select\r\n              fullWidth\r\n              label={t('settings.general.timezone', 'Timezone')}\r\n              name=\"timezone\"\r\n              value={settings.timezone}\r\n              onChange={handleChange}\r\n              variant=\"outlined\"\r\n            >\r\n              {timezones.map((tz) => (\r\n                <MenuItem key={tz} value={tz}>\r\n                  {tz}\r\n                </MenuItem>\r\n              ))}\r\n            </TextField>\r\n          </Grid>\r\n\r\n          <Grid item xs={12} sm={6}>\r\n            <TextField\r\n              select\r\n              fullWidth\r\n              label={t('settings.general.dateFormat', 'Date Format')}\r\n              name=\"dateFormat\"\r\n              value={settings.dateFormat}\r\n              onChange={handleChange}\r\n              variant=\"outlined\"\r\n            >\r\n              {dateFormats.map((format) => (\r\n                <MenuItem key={format} value={format}>\r\n                  {format}\r\n                </MenuItem>\r\n              ))}\r\n            </TextField>\r\n          </Grid>\r\n\r\n          <Grid item xs={12} sm={6}>\r\n            <TextField\r\n              select\r\n              fullWidth\r\n              label={t('settings.general.timeFormat', 'Time Format')}\r\n              name=\"timeFormat\"\r\n              value={settings.timeFormat}\r\n              onChange={handleChange}\r\n              variant=\"outlined\"\r\n            >\r\n              {timeFormats.map((format) => (\r\n                <MenuItem key={format} value={format}>\r\n                  {format}\r\n                </MenuItem>\r\n              ))}\r\n            </TextField>\r\n          </Grid>\r\n        </Grid>\r\n\r\n        <Typography variant=\"h6\" gutterBottom sx={{ mt: 4 }}>\r\n          {t('settings.general.siteOperation', 'Site Operation')}\r\n        </Typography>\r\n        <Divider sx={{ mb: 3 }} />\r\n\r\n        <Grid container spacing={3}>\r\n          <Grid item xs={12} sm={6}>\r\n            <FormControlLabel\r\n              control={\r\n                <Switch\r\n                  checked={settings.maintenanceMode}\r\n                  onChange={handleChange}\r\n                  name=\"maintenanceMode\"\r\n                  color=\"warning\"\r\n                />\r\n              }\r\n              label={t('settings.general.maintenanceMode', 'Maintenance Mode')}\r\n            />\r\n          </Grid>\r\n\r\n          <Grid item xs={12} sm={6}>\r\n            <FormControlLabel\r\n              control={\r\n                <Switch\r\n                  checked={settings.allowUserRegistration}\r\n                  onChange={handleChange}\r\n                  name=\"allowUserRegistration\"\r\n                  color=\"primary\"\r\n                />\r\n              }\r\n              label={t('settings.general.allowRegistration', 'Allow User Registration')}\r\n            />\r\n          </Grid>\r\n\r\n          {settings.maintenanceMode && (\r\n            <Grid item xs={12}>\r\n              <TextField\r\n                fullWidth\r\n                label={t('settings.general.maintenanceMessage', 'Maintenance Message')}\r\n                name=\"maintenanceMessage\"\r\n                value={settings.maintenanceMessage}\r\n                onChange={handleChange}\r\n                variant=\"outlined\"\r\n                multiline\r\n                rows={2}\r\n                placeholder={t('settings.general.maintenanceMessagePlaceholder', 'Site under maintenance. Please check back later.')}\r\n              />\r\n            </Grid>\r\n          )}\r\n        </Grid>\r\n\r\n        <Box sx={{ mt: 4, display: 'flex', justifyContent: 'flex-end' }}>\r\n          <Button\r\n            type=\"submit\"\r\n            variant=\"contained\"\r\n            color=\"primary\"\r\n            startIcon={<SaveIcon />}\r\n            disabled={saving}\r\n          >\r\n            {saving ? <CircularProgress size={24} /> : t('actions.saveSettings', 'Save Settings')}\r\n          </Button>\r\n        </Box>\r\n      </form>\r\n\r\n      <Snackbar\r\n        open={toast.open}\r\n        autoHideDuration={6000}\r\n        onClose={handleCloseToast}\r\n        message={toast.message}\r\n        severity={toast.severity}\r\n      />\r\n    </Paper>\r\n  );\r\n}\r\n\r\nexport default GeneralSettings;\r\n"], "names": ["GeneralSettings", "language", "changeLanguage", "useLanguage", "settings", "setSettings", "useState", "loading", "setLoading", "saving", "setSaving", "error", "setError", "success", "setSuccess", "toast", "setToast", "useEffect", "data", "settingsService", "prevSettings", "err", "handleChange", "e", "name", "value", "type", "checked", "prev", "handleSubmit", "response", "handleCloseToast", "timezones", "dateFormats", "timeFormats", "languages", "jsx", "Box", "CircularProgress", "jsxs", "Paper", "Typography", "<PERSON><PERSON>", "Divider", "Grid", "TextField", "InputAdornment", "lang", "MenuItem", "tz", "format", "FormControlLabel", "Switch", "<PERSON><PERSON>", "SaveIcon", "Snackbar"], "mappings": "sTA0BA,SAASA,GAAkB,CACzB,KAAM,CAAE,EAAG,SAAAC,EAAU,eAAAC,CAAA,EAAmBC,EAAA,EAElC,CAACC,EAAUC,CAAW,EAAIC,WAAS,CACvC,SAAU,cACV,gBAAiB,GACjB,aAAc,GACd,aAAc,GACd,QAAS,GACT,KAAM,GACN,QAAS,GACT,aAAc,UACd,eAAgB,UAChB,gBAAiB,KACjB,mBAAoB,CAAC,KAAM,KAAM,IAAI,EACrC,WAAY,aACZ,WAAY,QACZ,SAAU,iBACV,gBAAiB,GACjB,mBAAoB,GACpB,sBAAuB,GACvB,gBAAiB,OACjB,WAAY,gBACZ,cAAe,EACf,YAAa,CAAA,CAAC,CACf,EAEK,CAACC,EAASC,CAAU,EAAIF,EAAAA,SAAS,EAAI,EACrC,CAACG,EAAQC,CAAS,EAAIJ,EAAAA,SAAS,EAAK,EACpC,CAACK,EAAOC,CAAQ,EAAIN,EAAAA,SAAS,EAAE,EAC/B,CAACO,EAASC,CAAU,EAAIR,EAAAA,SAAS,EAAE,EACnC,CAACS,EAAOC,CAAQ,EAAIV,EAAAA,SAAS,CAAE,KAAM,GAAO,QAAS,GAAI,SAAU,MAAA,CAAQ,EAEjFW,EAAAA,UAAU,IAAM,EACQ,SAAY,CAChC,GAAI,CACFT,EAAW,EAAI,EACf,MAAMU,EAAO,MAAMC,EAAgB,qBAAA,EAC/BD,GACFb,EAAYe,IAAiB,CAC3B,GAAGA,EACH,GAAGF,CAAA,EACH,EAEJN,EAAS,EAAE,CAAA,OACJS,EAAK,CACZT,EAASS,EAAI,UAAU,MAAM,SAAW,EAAE,iCAAkC,iCAAiC,CAAC,EAC9G,QAAQ,MAAM,8BAA+BA,CAAG,CAAA,QAClD,CACEb,EAAW,EAAK,CAAA,CAClB,GAEF,CAAc,EACb,CAAC,CAAC,CAAC,EAEN,MAAMc,EAAgBC,GAAM,CAC1B,KAAM,CAAE,KAAAC,EAAM,MAAAC,EAAO,KAAAC,EAAM,QAAAC,CAAA,EAAYJ,EAAE,OACzClB,EAAYuB,IAAS,CACnB,GAAGA,EACH,CAACJ,CAAI,EAAGE,IAAS,YAAcA,IAAS,SAAWC,EAAUF,CAAA,EAC7D,CAAA,EAGEI,EAAe,MAAON,GAAM,CAChCA,EAAE,eAAA,EACFb,EAAU,EAAI,EACdE,EAAS,EAAE,EACXE,EAAW,EAAE,EAEb,GAAI,CACF,MAAMgB,EAAW,MAAMX,EAAgB,sBAAsBf,CAAQ,EACrEU,EAAW,EAAE,+BAAgC,sCAAsC,CAAC,EACpFE,EAAS,CACP,KAAM,GACN,QAAS,EAAE,+BAAgC,sCAAsC,EACjF,SAAU,SAAA,CACX,CAAA,OACMK,EAAK,CACZT,EAASS,EAAI,UAAU,MAAM,SAAW,EAAE,+BAAgC,+BAA+B,CAAC,EAC1GL,EAAS,CACP,KAAM,GACN,QAASK,EAAI,UAAU,MAAM,SAAW,EAAE,+BAAgC,+BAA+B,EACzG,SAAU,OAAA,CACX,EACD,QAAQ,MAAM,sBAAuBA,CAAG,CAAA,QAC1C,CACEX,EAAU,EAAK,CAAA,CACjB,EAGIqB,EAAmB,IAAM,CAC7Bf,EAAS,CAAE,GAAGD,EAAO,KAAM,GAAO,CAAA,EAG9BiB,EAAY,CAChB,iBACA,eACA,gBACA,mBACA,aACA,aACA,kBAAA,EAGIC,EAAc,CAClB,aACA,aACA,aACA,aACA,YAAA,EAGIC,EAAc,CAClB,QACA,UACA,UAAA,EAGIC,EAAY,CAChB,CAAE,KAAM,KAAM,KAAM,UAAA,EACpB,CAAE,KAAM,KAAM,KAAM,SAAA,EACpB,CAAE,KAAM,KAAM,KAAM,SAAA,CAAU,EAGhC,OAAI5B,EAEA6B,EAAAA,IAACC,EAAA,CAAI,QAAQ,OAAO,eAAe,SAAS,WAAW,SAAS,UAAU,QACxE,SAAAD,EAAAA,IAACE,EAAA,CAAA,CAAiB,EACpB,EAKFC,OAACC,EAAA,CAAM,UAAW,EAAG,GAAI,CAAE,EAAG,EAAG,EAAG,CAAA,EAClC,SAAA,CAAAJ,EAAAA,IAACK,EAAA,CAAW,QAAQ,KAAK,aAAY,GAAC,GAAI,CAAE,GAAI,CAAA,EAC7C,SAAA,EAAE,yBAA0B,kBAAkB,EACjD,EAEC9B,GAASyB,EAAAA,IAACM,EAAA,CAAM,SAAS,QAAQ,GAAI,CAAE,GAAI,CAAA,EAAM,SAAA/B,CAAA,CAAM,EACvDE,GAAWuB,EAAAA,IAACM,EAAA,CAAM,SAAS,UAAU,GAAI,CAAE,GAAI,CAAA,EAAM,SAAA7B,CAAA,CAAQ,EAE9D0B,EAAAA,KAAC,OAAA,CAAK,SAAUV,EACd,SAAA,CAAAO,EAAAA,IAACK,EAAA,CAAW,QAAQ,KAAK,aAAY,GAAC,GAAI,CAAE,GAAI,CAAA,EAC7C,SAAA,EAAE,gCAAiC,eAAe,EACrD,QACCE,EAAA,CAAQ,GAAI,CAAE,GAAI,GAAK,EAExBJ,EAAAA,KAACK,EAAA,CAAK,UAAS,GAAC,QAAS,EACvB,SAAA,CAAAR,MAACQ,GAAK,KAAI,GAAC,GAAI,GAAI,GAAI,EACrB,SAAAR,EAAAA,IAACS,EAAA,CACC,UAAS,GACT,MAAO,EAAE,4BAA6B,WAAW,EACjD,KAAK,WACL,MAAOzC,EAAS,SAChB,SAAUkB,EACV,QAAQ,WACR,SAAQ,EAAA,CAAA,EAEZ,QAECsB,EAAA,CAAK,KAAI,GAAC,GAAI,GAAI,GAAI,EACrB,SAAAR,EAAAA,IAACS,EAAA,CACC,UAAS,GACT,MAAO,EAAE,mCAAoC,kBAAkB,EAC/D,KAAK,kBACL,MAAOzC,EAAS,gBAChB,SAAUkB,EACV,QAAQ,WACR,UAAS,GACT,KAAM,CAAA,CAAA,EAEV,QAECsB,EAAA,CAAK,KAAI,GAAC,GAAI,GAAI,GAAI,EACrB,SAAAR,EAAAA,IAACS,EAAA,CACC,UAAS,GACT,MAAO,EAAE,gCAAiC,eAAe,EACzD,KAAK,eACL,MAAOzC,EAAS,aAChB,SAAUkB,EACV,QAAQ,WACR,KAAK,OAAA,CAAA,EAET,QAECsB,EAAA,CAAK,KAAI,GAAC,GAAI,GAAI,GAAI,EACrB,SAAAR,EAAAA,IAACS,EAAA,CACC,UAAS,GACT,MAAO,EAAE,gCAAiC,eAAe,EACzD,KAAK,eACL,MAAOzC,EAAS,aAChB,SAAUkB,EACV,QAAQ,UAAA,CAAA,EAEZ,EAEAc,EAAAA,IAACQ,EAAA,CAAK,KAAI,GAAC,GAAI,GACb,SAAAR,EAAAA,IAACS,EAAA,CACC,UAAS,GACT,MAAO,EAAE,2BAA4B,SAAS,EAC9C,KAAK,UACL,MAAOzC,EAAS,QAChB,SAAUkB,EACV,QAAQ,WACR,UAAS,GACT,KAAM,CAAA,CAAA,CACR,CACF,CAAA,EACF,EAEAc,EAAAA,IAACK,EAAA,CAAW,QAAQ,KAAK,aAAY,GAAC,GAAI,CAAE,GAAI,CAAA,EAC7C,SAAA,EAAE,8BAA+B,YAAY,EAChD,QACCE,EAAA,CAAQ,GAAI,CAAE,GAAI,GAAK,EAExBJ,EAAAA,KAACK,EAAA,CAAK,UAAS,GAAC,QAAS,EACvB,SAAA,CAAAR,MAACQ,GAAK,KAAI,GAAC,GAAI,GAAI,GAAI,EACrB,SAAAR,EAAAA,IAACS,EAAA,CACC,UAAS,GACT,MAAO,EAAE,wBAAyB,UAAU,EAC5C,KAAK,OACL,MAAOzC,EAAS,KAChB,SAAUkB,EACV,QAAQ,WACR,YAAY,8BAAA,CAAA,EAEhB,QAECsB,EAAA,CAAK,KAAI,GAAC,GAAI,GAAI,GAAI,EACrB,SAAAR,EAAAA,IAACS,EAAA,CACC,UAAS,GACT,MAAO,EAAE,2BAA4B,aAAa,EAClD,KAAK,UACL,MAAOzC,EAAS,QAChB,SAAUkB,EACV,QAAQ,WACR,YAAY,iCAAA,CAAA,EAEhB,QAECsB,EAAA,CAAK,KAAI,GAAC,GAAI,GAAI,GAAI,EACrB,SAAAR,EAAAA,IAACS,EAAA,CACC,UAAS,GACT,MAAO,EAAE,gCAAiC,eAAe,EACzD,KAAK,eACL,MAAOzC,EAAS,aAChB,SAAUkB,EACV,QAAQ,WACR,WAAY,CACV,eACEc,EAAAA,IAACU,EAAA,CAAe,SAAS,QACvB,SAAAV,EAAAA,IAACC,EAAA,CACC,GAAI,CACF,MAAO,GACP,OAAQ,GACR,gBAAiBjC,EAAS,aAC1B,aAAc,MACd,OAAQ,gBAAA,CACV,CAAA,CACF,CACF,CAAA,CAEJ,CAAA,EAEJ,QAECwC,EAAA,CAAK,KAAI,GAAC,GAAI,GAAI,GAAI,EACrB,SAAAR,EAAAA,IAACS,EAAA,CACC,UAAS,GACT,MAAO,EAAE,kCAAmC,iBAAiB,EAC7D,KAAK,iBACL,MAAOzC,EAAS,eAChB,SAAUkB,EACV,QAAQ,WACR,WAAY,CACV,eACEc,EAAAA,IAACU,EAAA,CAAe,SAAS,QACvB,SAAAV,EAAAA,IAACC,EAAA,CACC,GAAI,CACF,MAAO,GACP,OAAQ,GACR,gBAAiBjC,EAAS,eAC1B,aAAc,MACd,OAAQ,gBAAA,CACV,CAAA,CACF,CACF,CAAA,CAEJ,CAAA,EAEJ,QAECwC,EAAA,CAAK,KAAI,GAAC,GAAI,GAAI,GAAI,EACrB,SAAAR,EAAAA,IAACS,EAAA,CACC,UAAS,GACT,MAAO,EAAE,8BAA+B,aAAa,EACrD,KAAK,aACL,MAAOzC,EAAS,WAChB,SAAUkB,EACV,QAAQ,UAAA,CAAA,EAEZ,QAECsB,EAAA,CAAK,KAAI,GAAC,GAAI,GAAI,GAAI,EACrB,SAAAR,EAAAA,IAACS,EAAA,CACC,UAAS,GACT,MAAO,EAAE,iCAAkC,sBAAsB,EACjE,KAAK,gBACL,MAAOzC,EAAS,cAChB,SAAUkB,EACV,QAAQ,WACR,KAAK,SACL,WAAY,CACV,WAAY,CAAE,IAAK,EAAG,IAAK,GAAA,CAAI,CACjC,CAAA,CACF,CACF,CAAA,EACF,EAEAc,EAAAA,IAACK,EAAA,CAAW,QAAQ,KAAK,aAAY,GAAC,GAAI,CAAE,GAAI,CAAA,EAC7C,SAAA,EAAE,gCAAiC,cAAc,EACpD,QACCE,EAAA,CAAQ,GAAI,CAAE,GAAI,GAAK,EAExBJ,EAAAA,KAACK,EAAA,CAAK,UAAS,GAAC,QAAS,EACvB,SAAA,CAAAR,MAACQ,GAAK,KAAI,GAAC,GAAI,GAAI,GAAI,EACrB,SAAAR,EAAAA,IAACS,EAAA,CACC,OAAM,GACN,UAAS,GACT,MAAO,EAAE,mCAAoC,kBAAkB,EAC/D,KAAK,kBACL,MAAOzC,EAAS,gBAChB,SAAUkB,EACV,QAAQ,WAEP,SAAAa,EAAU,IAAKY,GACdX,EAAAA,IAACY,EAAA,CAAyB,MAAOD,EAAK,KACnC,SAAAA,EAAK,IAAA,EADOA,EAAK,IAEpB,CACD,CAAA,CAAA,EAEL,QAECH,EAAA,CAAK,KAAI,GAAC,GAAI,GAAI,GAAI,EACrB,SAAAR,EAAAA,IAACS,EAAA,CACC,OAAM,GACN,UAAS,GACT,MAAO,EAAE,4BAA6B,UAAU,EAChD,KAAK,WACL,MAAOzC,EAAS,SAChB,SAAUkB,EACV,QAAQ,WAEP,SAAAU,EAAU,IAAKiB,GACdb,EAAAA,IAACY,GAAkB,MAAOC,EACvB,SAAAA,CAAA,EADYA,CAEf,CACD,CAAA,CAAA,EAEL,QAECL,EAAA,CAAK,KAAI,GAAC,GAAI,GAAI,GAAI,EACrB,SAAAR,EAAAA,IAACS,EAAA,CACC,OAAM,GACN,UAAS,GACT,MAAO,EAAE,8BAA+B,aAAa,EACrD,KAAK,aACL,MAAOzC,EAAS,WAChB,SAAUkB,EACV,QAAQ,WAEP,SAAAW,EAAY,IAAKiB,GAChBd,EAAAA,IAACY,GAAsB,MAAOE,EAC3B,SAAAA,CAAA,EADYA,CAEf,CACD,CAAA,CAAA,EAEL,QAECN,EAAA,CAAK,KAAI,GAAC,GAAI,GAAI,GAAI,EACrB,SAAAR,EAAAA,IAACS,EAAA,CACC,OAAM,GACN,UAAS,GACT,MAAO,EAAE,8BAA+B,aAAa,EACrD,KAAK,aACL,MAAOzC,EAAS,WAChB,SAAUkB,EACV,QAAQ,WAEP,SAAAY,EAAY,IAAKgB,GAChBd,EAAAA,IAACY,GAAsB,MAAOE,EAC3B,SAAAA,CAAA,EADYA,CAEf,CACD,CAAA,CAAA,CACH,CACF,CAAA,EACF,EAEAd,EAAAA,IAACK,EAAA,CAAW,QAAQ,KAAK,aAAY,GAAC,GAAI,CAAE,GAAI,CAAA,EAC7C,SAAA,EAAE,iCAAkC,gBAAgB,EACvD,QACCE,EAAA,CAAQ,GAAI,CAAE,GAAI,GAAK,EAExBJ,EAAAA,KAACK,EAAA,CAAK,UAAS,GAAC,QAAS,EACvB,SAAA,CAAAR,MAACQ,GAAK,KAAI,GAAC,GAAI,GAAI,GAAI,EACrB,SAAAR,EAAAA,IAACe,EAAA,CACC,QACEf,EAAAA,IAACgB,EAAA,CACC,QAAShD,EAAS,gBAClB,SAAUkB,EACV,KAAK,kBACL,MAAM,SAAA,CAAA,EAGV,MAAO,EAAE,mCAAoC,kBAAkB,CAAA,CAAA,EAEnE,QAECsB,EAAA,CAAK,KAAI,GAAC,GAAI,GAAI,GAAI,EACrB,SAAAR,EAAAA,IAACe,EAAA,CACC,QACEf,EAAAA,IAACgB,EAAA,CACC,QAAShD,EAAS,sBAClB,SAAUkB,EACV,KAAK,wBACL,MAAM,SAAA,CAAA,EAGV,MAAO,EAAE,qCAAsC,yBAAyB,CAAA,CAAA,EAE5E,EAEClB,EAAS,iBACRgC,EAAAA,IAACQ,GAAK,KAAI,GAAC,GAAI,GACb,SAAAR,EAAAA,IAACS,EAAA,CACC,UAAS,GACT,MAAO,EAAE,sCAAuC,qBAAqB,EACrE,KAAK,qBACL,MAAOzC,EAAS,mBAChB,SAAUkB,EACV,QAAQ,WACR,UAAS,GACT,KAAM,EACN,YAAa,EAAE,iDAAkD,kDAAkD,CAAA,CAAA,CACrH,CACF,CAAA,EAEJ,EAEAc,EAAAA,IAACC,EAAA,CAAI,GAAI,CAAE,GAAI,EAAG,QAAS,OAAQ,eAAgB,UAAA,EACjD,SAAAD,EAAAA,IAACiB,EAAA,CACC,KAAK,SACL,QAAQ,YACR,MAAM,UACN,gBAAYC,EAAA,EAAS,EACrB,SAAU7C,EAET,SAAAA,QAAU6B,EAAA,CAAiB,KAAM,GAAI,EAAK,EAAE,uBAAwB,eAAe,CAAA,CAAA,CACtF,CACF,CAAA,EACF,EAEAF,EAAAA,IAACmB,EAAA,CACC,KAAMxC,EAAM,KACZ,iBAAkB,IAClB,QAASgB,EACT,QAAShB,EAAM,QACf,SAAUA,EAAM,QAAA,CAAA,CAClB,EACF,CAEJ"}