import React, { Suspense, lazy } from 'react';
import { Routes, Route, Navigate } from 'react-router-dom';
import { ThemeProvider } from '@mui/material/styles';
import { theme } from './theme';
import CssBaseline from '@mui/material/CssBaseline';
import Navbar from './components/Navbar';
import Home from './pages/Home';
import EleveursList from './pages/EleveursList';
import VolaillesList from './pages/VolaillesList';
import Dashboard from './pages/Dashboard';
import Login from './components/auth/Login';
import Register from './components/auth/Register';
import DashboardLayout from './layouts/DashboardLayout';
import HomePageManager from './components/HomePageManager';
import ErrorBoundary from './components/ErrorBoundary';
import Notifications from './pages/admin/Notifications';

// Pages publiques
import LandingPage from './components/landing/LandingPage';

// Pages privées
import Profile from './pages/admin/Profile';

// Pages dashboards
import AdminDashboard from './pages/dashboards/AdminDashboard';
import EleveurDashboard from './pages/dashboards/EleveurDashboard';
import VeterinaireDashboard from './pages/dashboards/VeterinaireDashboard';
import MarchandDashboard from './pages/dashboards/MarchandDashboard';

// Pages admin
import UsersManagement from './pages/admin/UsersManagement';
import Blog from './pages/admin/Blog';
import AiBlogGenerator from './pages/admin/AiBlogGenerator';
import AiDataAnalysis from './pages/admin/AiDataAnalysis';
import PageContentGenerator from './pages/admin/PageContentGenerator';
import TranslationsManager from './pages/admin/TranslationsManager';
import RolesPlans from './pages/admin/RolesPlans';
import NavigationDebugger from './components/NavigationDebugger';

// Lazy load admin components
const ApiConfig = lazy(() => import('./pages/admin/ApiConfig'));
const SmtpConfig = lazy(() => import('./pages/admin/SmtpConfigPage'));
const GeneralSettings = lazy(() => import('./pages/admin/GeneralSettings'));
const SecuritySettings = lazy(() => import('./pages/admin/SecuritySettingsPage'));
const LoginAsUser = lazy(() => import('./pages/admin/LoginAsUser'));

function App() {
  console.log('🚀 App component rendering...');

  return (
    <ThemeProvider theme={theme}>
      <CssBaseline />
      <div style={{ padding: '20px' }}>
        <h1>React App is Working!</h1>
        <p>If you see this, React is properly initialized.</p>
        <ErrorBoundary>
          <Routes>
            {/* Public Routes */}
            <Route path="/" element={
              <div>
                <h2>Home Page</h2>
                <p>This is the landing page</p>
                <LandingPage />
              </div>
            } />
            <Route path="/login" element={<Login />} />
            <Route path="/register" element={<Register />} />

            {/* Protected Routes */}
            <Route path="/admin/*" element={
              <DashboardLayout requiredRole="admin">
                <Routes>
                  <Route path="dashboard" element={<AdminDashboard />} />
                  <Route path="users" element={<UsersManagement />} />
                  <Route path="blog" element={<Blog />} />
                  <Route path="ai-blog" element={<AiBlogGenerator />} />
                  <Route path="ai-analysis" element={<AiDataAnalysis />} />
                  <Route path="content" element={<PageContentGenerator />} />
                  <Route path="translations" element={<TranslationsManager />} />
                  <Route path="roles" element={<RolesPlans />} />
                  <Route path="notifications" element={<Notifications />} />
                  <Route path="profile" element={<Profile />} />
                  <Route path="api-config" element={
                    <Suspense fallback={<div>Loading...</div>}>
                      <ApiConfig />
                    </Suspense>
                  } />
                  <Route path="smtp" element={
                    <Suspense fallback={<div>Loading...</div>}>
                      <SmtpConfig />
                    </Suspense>
                  } />
                  <Route path="settings" element={
                    <Suspense fallback={<div>Loading...</div>}>
                      <GeneralSettings />
                    </Suspense>
                  } />
                  <Route path="security" element={
                    <Suspense fallback={<div>Loading...</div>}>
                      <SecuritySettings />
                    </Suspense>
                  } />
                  <Route path="impersonate" element={
                    <Suspense fallback={<div>Loading...</div>}>
                      <LoginAsUser />
                    </Suspense>
                  } />
                  <Route path="debug" element={<NavigationDebugger />} />
                </Routes>
              </DashboardLayout>
            } />

            <Route path="/eleveur/*" element={
              <DashboardLayout requiredRole="eleveur">
                <Routes>
                  <Route path="dashboard" element={<EleveurDashboard />} />
                  <Route path="profile" element={<Profile />} />
                </Routes>
              </DashboardLayout>
            } />

            <Route path="/veterinaire/*" element={
              <DashboardLayout requiredRole="veterinaire">
                <Routes>
                  <Route path="dashboard" element={<VeterinaireDashboard />} />
                  <Route path="profile" element={<Profile />} />
                </Routes>
              </DashboardLayout>
            } />

            <Route path="/marchand/*" element={
              <DashboardLayout requiredRole="marchand">
                <Routes>
                  <Route path="dashboard" element={<MarchandDashboard />} />
                  <Route path="profile" element={<Profile />} />
                </Routes>
              </DashboardLayout>
            } />

            {/* Redirect unknown routes to home */}
            <Route path="*" element={<Navigate to="/" replace />} />
          </Routes>
        </ErrorBoundary>
      </div>
    </ThemeProvider>
  );
}

export default App;
