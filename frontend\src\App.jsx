import React from 'react';

function App() {
  console.log('🚀 App component rendering...');

  return (
    <div style={{ padding: '20px', fontFamily: 'Arial, sans-serif' }}>
      <h1 style={{ color: 'green' }}>✅ React App is Working!</h1>
      <p>If you see this, React is properly initialized and rendering.</p>
      <div style={{ marginTop: '20px', padding: '10px', backgroundColor: '#f0f0f0', borderRadius: '5px' }}>
        <h3>Debug Information:</h3>
        <p><strong>Timestamp:</strong> {new Date().toLocaleString()}</p>
        <p><strong>React Version:</strong> {React.version}</p>
        <p><strong>Environment:</strong> {import.meta.env.MODE}</p>
      </div>
    </div>
  );
}

export default App;
