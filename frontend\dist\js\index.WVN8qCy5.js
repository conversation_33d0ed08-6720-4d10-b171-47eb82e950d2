const __vite__mapDeps=(i,m=__vite__mapDeps,d=(m.f||(m.f=["js/ApiConfig.CQDJF2D9.js","js/vendor.CTQIA7G6.js","js/mui.D_tNY0b-.js","js/firebase.BaqyMmVp.js","js/SmtpConfigPage.DegdsIxF.js","js/settingsService.DVLXOYtr.js","js/GeneralSettings.C4Na4802.js","js/SecuritySettingsPage.D6hbTTNO.js","js/LoginAsUser.BJn28_kZ.js"])))=>i.map(i=>d[i]);
import{u as pe,r as i,j as e,v as ke,L as ce,w as qe,x as Ot,N as Me,O as ur,y as hr,z as es,D as xr,A as mr,B as pr,S as gr,E as jr,F as fr,H as vr,I as br,K as yr,P as Cr,J as Sr,M as wr,Q as Re,U as bs,V as Ye,X as Qe,Y as Je,W as We,Z as Ut,$ as ys,a0 as Cs,a1 as Ss,a2 as dt,a3 as Ht,a4 as At,a5 as Ir,a6 as ts,a7 as Ar,a8 as Dr,a9 as kr,aa as F,ab as Pr}from"./vendor.CTQIA7G6.js";import{i as Er,g as Tr,c as Mr,u as Lr,s as Rr,a as Wr,b as zr}from"./firebase.BaqyMmVp.js";import{s as he,T as ut,B as M,A as Gt,a as c,b as u,D as Zt,c as de,I as B,d as re,M as it,e as I,P as be,L as ws,f as Is,h as ss,i as Bt,j as oe,u as $t,k as Nr,C as ee,l as _r,m as Br,n as le,o as ae,p as Z,q as _,r as $r,G as n,t as H,v as qr,w as Fr,S as Vr,x as Or,N as Ur,y as Hr,z as Y,E as As,F as Ds,H as ks,J as Ps,K as A,O as Q,Q as J,R as K,U as Gr,V as kt,W as te,X as Zr,Y as Yr,Z as Qr,_ as Jr,$ as Es,a0 as Ts,a1 as tt,a2 as ze,a3 as Ne,a4 as _e,a5 as ie,a6 as S,a7 as Be,a8 as Oe,a9 as st,aa as Ce,ab as Se,ac as we,ad as Ie,ae as ye,af as De,ag as xt,ah as ge,ai as Fe,aj as Le,ak as ve,al as ue,am as je,an as Yt,ao as Qt,ap as rt,aq as Ms,ar as Kr,as as mt,at as Mt,au as Xr,av as en,aw as tn,ax as sn,ay as ot,az as lt,aA as Ze,aB as ct,aC as Jt,aD as Ke,aE as qt,aF as rn,aG as Pt,aH as nn,aI as se,aJ as Ls,aK as an,aL as on,aM as Rs,aN as It,aO as rs,aP as Lt,aQ as ln,aR as cn,aS as dn,aT as Xe,aU as un,aV as hn,aW as et,aX as Rt,aY as Kt,aZ as Ws,a_ as zs,a$ as ns,b0 as xn,b1 as as,b2 as is,b3 as mn,b4 as pn,b5 as Wt,b6 as gn,b7 as jn,b8 as fn,b9 as vn,ba as ht,bb as Ns,bc as Xt,bd as bn,be as os,bf as yn,bg as Cn,bh as Sn,bi as wn,bj as In,bk as An,bl as ls,bm as Dn,bn as kn}from"./mui.D_tNY0b-.js";(function(){const r=document.createElement("link").relList;if(r&&r.supports&&r.supports("modulepreload"))return;for(const s of document.querySelectorAll('link[rel="modulepreload"]'))x(s);new MutationObserver(s=>{for(const d of s)if(d.type==="childList")for(const h of d.addedNodes)h.tagName==="LINK"&&h.rel==="modulepreload"&&x(h)}).observe(document,{childList:!0,subtree:!0});function l(s){const d={};return s.integrity&&(d.integrity=s.integrity),s.referrerPolicy&&(d.referrerPolicy=s.referrerPolicy),s.crossOrigin==="use-credentials"?d.credentials="include":s.crossOrigin==="anonymous"?d.credentials="omit":d.credentials="same-origin",d}function x(s){if(s.ep)return;s.ep=!0;const d=l(s);fetch(s.href,d)}})();const Pn="modulepreload",En=function(t){return"/"+t},cs={},Ve=function(r,l,x){let s=Promise.resolve();if(l&&l.length>0){let h=function(p){return Promise.all(p.map(a=>Promise.resolve(a).then(D=>({status:"fulfilled",value:D}),D=>({status:"rejected",reason:D}))))};document.getElementsByTagName("link");const j=document.querySelector("meta[property=csp-nonce]"),g=j?.nonce||j?.getAttribute("nonce");s=h(l.map(p=>{if(p=En(p),p in cs)return;cs[p]=!0;const a=p.endsWith(".css"),D=a?'[rel="stylesheet"]':"";if(document.querySelector(`link[href="${p}"]${D}`))return;const E=document.createElement("link");if(E.rel=a?"stylesheet":Pn,a||(E.as="script"),E.crossOrigin="",E.href=p,g&&E.setAttribute("nonce",g),document.head.appendChild(E),a)return new Promise((v,f)=>{E.addEventListener("load",v),E.addEventListener("error",()=>f(new Error(`Unable to preload CSS for ${p}`)))})}))}function d(h){const j=new Event("vite:preloadError",{cancelable:!0});if(j.payload=h,window.dispatchEvent(j),!j.defaultPrevented)throw h}return s.then(h=>{for(const j of h||[])j.status==="rejected"&&d(j.reason);return r().catch(d)})},Tn="/api",Mn=3,Ln=1e3,N=pe.create({baseURL:Tn,timeout:3e4,headers:{"Content-Type":"application/json"}}),Rn=t=>new Promise(r=>setTimeout(r,t));N.interceptors.request.use(t=>{t.retryCount=t.retryCount||0;const r=localStorage.getItem("token");return r&&(t.headers.Authorization=`Bearer ${r}`,t.headers["x-auth-token"]=r),t.url.startsWith("/api/api/")&&(t.url=t.url.replace("/api/api/","/api/")),t},t=>Promise.reject(t));N.interceptors.response.use(t=>t,async t=>{const r=t.config;return!r||r.retryCount>=Mn?Promise.reject(t):(r.retryCount+=1,await Rn(Ln*r.retryCount),N(r))});const Wn={apiKey:"AIzaSyBZ9KIB7945XD6qJphq7dsaMJSh8DRBuX8",authDomain:"poultray-dz.firebaseapp.com",projectId:"poultray-dz",storageBucket:"poultray-dz.firebasestorage.app",messagingSenderId:"272222587457",appId:"1:272222587457:web:0b6775e967c60267e9d4c0",measurementId:"G-5GZQC73FJ8"},zn=Er(Wn),pt=Tr(zn),Ee=2e4,Te=(t,r,l)=>{let x;const s=new Promise((d,h)=>{x=setTimeout(()=>{h(new Error(l||`L'opération a dépassé le délai de ${r/1e3} secondes`))},r)});return Promise.race([t,s]).finally(()=>clearTimeout(x))};class Nn{async register(r){try{const l=await Te(Mr(pt,r.email,r.password),Ee,"L'inscription a pris trop de temps, veuillez réessayer");return await Te(Lr(l.user,{displayName:`${r.first_name} ${r.last_name}`}),Ee,"La mise à jour du profil a pris trop de temps"),(await Te(N.post("/auth/register",{...r,firebase_uid:l.user.uid}),Ee,"La synchronisation avec le serveur a pris trop de temps")).data}catch(l){throw console.error("Erreur lors de l'inscription:",l),this.handleFirebaseError(l)}}async login(r,l){try{const x=await Te(Rr(pt,r,l),Ee,"La connexion a pris trop de temps, veuillez réessayer"),s=await Te(x.user.getIdToken(),Ee,"L'obtention du token a pris trop de temps"),d=await Te(N.post("/auth/login",{email:r,firebase_token:s,firebase_uid:x.user.uid,display_name:x.user.displayName}),Ee,"La synchronisation avec le serveur a pris trop de temps"),h=d.data.token;return localStorage.setItem("token",h),localStorage.setItem("user_credentials",JSON.stringify({email:r,password:l})),{token:h,user:d.data.user}}catch(x){throw console.error("Erreur lors de la connexion:",x),this.handleFirebaseError(x)}}async logout(){try{await Te(Wr(pt),Ee,"La déconnexion a pris trop de temps"),localStorage.removeItem("token"),localStorage.removeItem("user_credentials")}catch(r){throw console.error("Erreur lors de la déconnexion:",r),this.handleFirebaseError(r)}}async resetPassword(r){try{await Te(zr(pt,r),Ee,"La réinitialisation du mot de passe a pris trop de temps")}catch(l){throw console.error("Erreur lors de la réinitialisation du mot de passe:",l),this.handleFirebaseError(l)}}handleFirebaseError(r){let l="Une erreur est survenue";switch(r.code){case"auth/email-already-in-use":l="Cette adresse email est déjà utilisée";break;case"auth/invalid-email":l="Adresse email invalide";break;case"auth/operation-not-allowed":l="Opération non autorisée";break;case"auth/weak-password":l="Le mot de passe est trop faible";break;case"auth/user-disabled":l="Ce compte a été désactivé";break;case"auth/user-not-found":l="Aucun compte ne correspond à cette adresse email";break;case"auth/wrong-password":l="Mot de passe incorrect";break;case"auth/too-many-requests":l="Trop de tentatives de connexion. Veuillez réessayer plus tard";break;case"auth/network-request-failed":l="Problème de connexion réseau. Vérifiez votre connexion internet";break;default:r.message&&r.message.includes("timeout")&&(l=r.message);break}return new Error(l)}}const zt=new Nn,_s=i.createContext(),fe=()=>i.useContext(_s),_n=({children:t})=>{const[r,l]=i.useState(null),[x,s]=i.useState(localStorage.getItem("token")),[d,h]=i.useState(!0),[j,g]=i.useState(null);i.useEffect(()=>{if(!x){h(!1);return}},[x]),i.useEffect(()=>{(async()=>{if(console.log("🔍 AuthContext: Chargement utilisateur, token:",!!x),!x){console.log("❌ AuthContext: Pas de token, arrêt du chargement"),h(!1);return}try{console.log("📡 AuthContext: Appel API /auth/user");const o=await N.get("/auth/user");console.log("✅ AuthContext: Utilisateur chargé:",o.data);const k=o.data;k.role&&typeof k.role=="object"&&(k.role=k.role.name),console.log("🔧 AuthContext: Rôle formaté:",k.role),l(k),g(null)}catch(o){console.error("❌ AuthContext: Erreur chargement utilisateur:",o),g("Session expirée. Veuillez vous reconnecter."),localStorage.removeItem("token"),s(null),l(null)}finally{console.log("🏁 AuthContext: Fin du chargement"),h(!1)}})()},[x]);const p=async(m,o)=>{try{h(!0),g(null),console.log("🔐 AuthContext: Début de la connexion pour:",m);const k=await zt.login(m,o);return console.log("✅ AuthContext: Données d'authentification reçues:",{hasToken:!!k.token,hasUser:!!k.user,userRole:k.user?.role}),s(k.token),l(k.user),console.log("🎯 AuthContext: État mis à jour - utilisateur connecté avec le rôle:",k.user?.role),k.user}catch(k){throw console.error("❌ AuthContext: Erreur de connexion:",k),g(k.message||"Erreur de connexion"),k}finally{h(!1)}},a=async m=>{try{return h(!0),await zt.register(m)}catch(o){throw g(o.message||"Erreur d'inscription"),o}finally{h(!1)}},D=(m,o,k,V)=>{localStorage.setItem("token",o),localStorage.setItem("originalAdminToken",k),localStorage.setItem("impersonationExpiresAt",V),s(o),l({...m,isImpersonating:!0})},E=async()=>{const m=localStorage.getItem("originalAdminToken");m&&(localStorage.setItem("token",m),localStorage.removeItem("originalAdminToken"),localStorage.removeItem("impersonationExpiresAt"),s(m),await v(m))},v=async m=>{try{h(!0);const o=await N.get("/auth/user",{headers:{Authorization:`Bearer ${m}`}});l(o.data),g(null)}catch{g("Session expirée. Veuillez vous reconnecter."),s(null),l(null),localStorage.removeItem("token")}finally{h(!1)}},C={user:r,token:x,loading:d,error:j,login:p,register:a,logout:async()=>{try{await zt.logout(),s(null),l(null),localStorage.removeItem("originalAdminToken"),localStorage.removeItem("impersonationExpiresAt")}catch(m){console.error("Erreur lors de la déconnexion:",m),g(m.message||"Erreur lors de la déconnexion")}},updateProfile:async m=>{try{h(!0);const o=await N.put("/api/auth/profile",m);return l(o.data),o.data}catch(o){throw g(o.response?.data?.message||"Erreur de mise à jour du profil"),o}finally{h(!1)}},changePassword:async(m,o)=>{try{return h(!0),(await N.put("/api/auth/change-password",{oldPassword:m,newPassword:o})).data}catch(k){throw g(k.response?.data?.message||"Erreur de changement de mot de passe"),k}finally{h(!1)}},hasRole:m=>r&&r.role===m,isAuthenticated:()=>!!r,setError:g,loginAs:D,logoutAs:E};return e.jsx(_s.Provider,{value:C,children:t})},Bn=he(ut)(({theme:t})=>({display:"flex",justifyContent:"space-between",padding:t.spacing(0,3)})),gt=he(M)(({theme:t})=>({color:"white",marginLeft:t.spacing(2),"&:hover":{backgroundColor:"rgba(255, 255, 255, 0.1)"}}));function jt({children:t}){const{user:r,isAuthenticated:l,logout:x}=fe(),s=ke(),[d,h]=i.useState(null),j=D=>{h(D.currentTarget)},g=()=>{h(null)},p=()=>{g(),x(),s("/login")},a=()=>{if(r){const D=typeof r.role=="object"&&r.role!==null?r.role.name:r.role;s(`/${D}/dashboard`)}else s("/login")};return e.jsxs(e.Fragment,{children:[e.jsx(Gt,{position:"static",children:e.jsxs(Bn,{children:[e.jsx(c,{variant:"h6",component:ce,to:"/",sx:{color:"white",textDecoration:"none",fontWeight:"bold"},children:"Poultray DZ"}),e.jsxs(u,{children:[e.jsx(gt,{component:ce,to:"/eleveurs",children:"Éleveurs"}),e.jsx(gt,{component:ce,to:"/volailles",children:"Volailles"}),l()?e.jsxs(e.Fragment,{children:[e.jsx(gt,{onClick:a,startIcon:e.jsx(Zt,{}),sx:{backgroundColor:"rgba(255, 255, 255, 0.1)"},children:"Tableau de bord"}),e.jsx(de,{title:"Profil",children:e.jsx(B,{onClick:j,sx:{ml:2,color:"white"},children:e.jsx(re,{sx:{width:32,height:32,bgcolor:"secondary.main"},children:r?.first_name?r.first_name[0]:r?.username?r.username[0]:"U"})})}),e.jsxs(it,{anchorEl:d,open:!!d,onClose:g,PaperProps:{elevation:0,sx:{overflow:"visible",filter:"drop-shadow(0px 2px 8px rgba(0,0,0,0.32))",mt:1.5}},children:[e.jsxs(I,{onClick:()=>{g(),s(`/${r.role}/profile`)},children:[e.jsx(be,{sx:{mr:1}})," Profil"]}),e.jsxs(I,{onClick:p,children:[e.jsx(ws,{sx:{mr:1}})," Déconnexion"]})]})]}):e.jsx(gt,{component:ce,to:"/login",startIcon:e.jsx(Is,{}),children:"Connexion"})]})]})}),t]})}const $n="data:image/svg+xml;base64,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",qn="data:image/svg+xml;base64,PHN2ZyB4bWxucz0iaHR0cDovL3d3dy53My5vcmcvMjAwMC9zdmciIHdpZHRoPSIyMDAiIGhlaWdodD0iMjAwIiB2aWV3Qm94PSIwIDAgMjAwIDIwMCI+DQogIDxkZWZzPg0KICAgIDxsaW5lYXJHcmFkaWVudCBpZD0iZ3JhZGllbnQiIHgxPSIwJSIgeTE9IjAlIiB4Mj0iMTAwJSIgeTI9IjEwMCUiPg0KICAgICAgPHN0b3Agb2Zmc2V0PSIwJSIgc3RvcC1jb2xvcj0iIzRjYWY1MCIgLz4NCiAgICAgIDxzdG9wIG9mZnNldD0iMTAwJSIgc3RvcC1jb2xvcj0iIzM1N2EzOCIgLz4NCiAgICA8L2xpbmVhckdyYWRpZW50Pg0KICA8L2RlZnM+DQogIDxjaXJjbGUgY3g9IjEwMCIgY3k9IjEwMCIgcj0iOTAiIGZpbGw9IndoaXRlIiBzdHJva2U9InVybCgjZ3JhZGllbnQpIiBzdHJva2Utd2lkdGg9IjUiIC8+DQogIDxnIHRyYW5zZm9ybT0idHJhbnNsYXRlKDUwLCA1MCkiPg0KICAgIDwhLS0gU3R5bGl6ZWQgY2hpY2tlbiBzaWxob3VldHRlIC0tPg0KICAgIDxwYXRoIGQ9Ik0yNSwwIEM0MCwwIDUwLDEwIDYwLDMwIEM3MCw1MCA3MCw3MCA2MCw5MCBDNTAsMTEwIDMwLDEwMCAyMCw4MCBDMTAsNjAgMTAsNDAgMjAsMjAgQzI1LDEwIDM1LDAgNTAsMCBaIiBmaWxsPSJ1cmwoI2dyYWRpZW50KSIgLz4NCiAgICA8IS0tIEVnZyBzaGFwZSAtLT4NCiAgICA8ZWxsaXBzZSBjeD0iNzAiIGN5PSI1MCIgcng9IjIwIiByeT0iMzAiIGZpbGw9IndoaXRlIiBzdHJva2U9IiM0Y2FmNTAiIHN0cm9rZS13aWR0aD0iMyIgLz4NCiAgPC9nPg0KICA8dGV4dCB4PSIxMDAiIHk9IjE2MCIgZm9udC1mYW1pbHk9IkFyaWFsIiBmb250LXNpemU9IjE4IiBmb250LXdlaWdodD0iYm9sZCIgdGV4dC1hbmNob3I9Im1pZGRsZSIgZmlsbD0iIzRjYWY1MCI+UG91bHRyYXkgRFo8L3RleHQ+DQo8L3N2Zz4NCg==",Bs={appName:"Poultray DZ",loading:"Chargement...",save:"Enregistrer",cancel:"Annuler",delete:"Supprimer",edit:"Modifier",view:"Voir",search:"Rechercher",filter:"Filtrer",add:"Ajouter",submit:"Soumettre",reset:"Réinitialiser",back:"Retour",next:"Suivant",previous:"Précédent",yes:"Oui",no:"Non",confirm:"Confirmer",success:"Succès",error:"Erreur",warning:"Avertissement",info:"Information"},$s={login:"Connexion",logout:"Déconnexion",register:"Inscription",forgotPassword:"Mot de passe oublié",resetPassword:"Réinitialiser le mot de passe",changePassword:"Changer le mot de passe",email:"Adresse email",password:"Mot de passe",confirmPassword:"Confirmer le mot de passe",username:"Nom d'utilisateur",firstName:"Prénom",lastName:"Nom",role:"Rôle",rememberMe:"Se souvenir de moi",loginButton:"Se connecter",registerButton:"S'inscrire",alreadyHaveAccount:"Vous avez déjà un compte ?",dontHaveAccount:"Vous n'avez pas de compte ?",loginSuccess:"Connexion réussie",loginError:"Erreur de connexion",registerSuccess:"Inscription réussie",registerError:"Erreur d'inscription",logoutSuccess:"Déconnexion réussie",passwordMismatch:"Les mots de passe ne correspondent pas",passwordChanged:"Mot de passe modifié avec succès",passwordResetSent:"Instructions de réinitialisation envoyées à votre email",invalidCredentials:"Email ou mot de passe incorrect"},qs={dashboard:"Tableau de bord",overview:"Aperçu",statistics:"Statistiques",reports:"Rapports",analytics:"Analyses",welcome:"Bienvenue",quickActions:"Actions rapides",recentActivity:"Activité récente",notifications:"Notifications",settings:"Paramètres",profile:"Profil",help:"Aide"},Fs={adminPanel:"Panneau d'administration",users:"Utilisateurs",roles:"Rôles",permissions:"Permissions",settings:"Paramètres",logs:"Journaux",system:"Système",maintenance:"Maintenance",backups:"Sauvegardes",translations:"Traductions",blog:"Blog",ai:"Intelligence Artificielle",aiTools:"Outils IA",blogGenerator:"Générateur de blog",dataAnalysis:"Analyse de données",pageContent:"Contenu de page"},Vs={dashboard:"Tableau de bord éleveur",myVolailles:"Mes volailles",mySales:"Mes ventes",myStatistics:"Mes statistiques",addVolaille:"Ajouter des volailles",editVolaille:"Modifier des volailles",deleteVolaille:"Supprimer des volailles",volailleDetails:"Détails des volailles",volailleStats:"Statistiques des volailles",volailleHealth:"Santé des volailles",volailleFeeding:"Alimentation des volailles",volailleGrowth:"Croissance des volailles",volailleSales:"Ventes de volailles"},Os={dashboard:"Tableau de bord vétérinaire",consultations:"Consultations",prescriptions:"Prescriptions",appointments:"Rendez-vous",patients:"Patients",medicalRecords:"Dossiers médicaux",treatments:"Traitements",vaccines:"Vaccins",diseases:"Maladies",addConsultation:"Ajouter une consultation",editConsultation:"Modifier une consultation",deleteConsultation:"Supprimer une consultation",addPrescription:"Ajouter une prescription",editPrescription:"Modifier une prescription",deletePrescription:"Supprimer une prescription"},Us={dashboard:"Tableau de bord marchand",products:"Produits",orders:"Commandes",sales:"Ventes",customers:"Clients",inventory:"Inventaire",shipping:"Expédition",returns:"Retours",addProduct:"Ajouter un produit",editProduct:"Modifier un produit",deleteProduct:"Supprimer un produit",productDetails:"Détails du produit",orderDetails:"Détails de la commande",processOrder:"Traiter la commande",cancelOrder:"Annuler la commande",refundOrder:"Rembourser la commande"},Hs={volailles:"Volailles",espece:"Espèce",race:"Race",age:"Âge",poids:"Poids",quantite:"Quantité",prix:"Prix",valeur:"Valeur",statut:"Statut",description:"Description",dateAjout:"Date d'ajout",eleveur:"Éleveur",poulets:"Poulets",dindes:"Dindes",canards:"Canards",cailles:"Cailles",oies:"Oies",pintades:"Pintades",disponible:"Disponible",vendu:"Vendu",reserve:"Réservé",malade:"Malade",traitement:"En traitement",quarantaine:"En quarantaine"},Gs={newMessage:"Nouveau message",newOrder:"Nouvelle commande",orderStatus:"Statut de commande mis à jour",paymentReceived:"Paiement reçu",appointmentReminder:"Rappel de rendez-vous",systemUpdate:"Mise à jour système",lowStock:"Stock faible",criticalAlert:"Alerte critique",markAllAsRead:"Marquer tout comme lu",clearAll:"Tout effacer",viewAll:"Voir tout"},Zs={required:"Ce champ est requis",invalidEmail:"Adresse email invalide",minLength:"Doit contenir au moins {min} caractères",maxLength:"Ne doit pas dépasser {max} caractères",passwordMismatch:"Les mots de passe ne correspondent pas",serverError:"Erreur serveur, veuillez réessayer plus tard",notFound:"Non trouvé",unauthorized:"Non autorisé",forbidden:"Accès refusé",badRequest:"Requête invalide",conflict:"Conflit de données",networkError:"Erreur réseau, veuillez vérifier votre connexion",fetchingSmtpSettings:"Erreur lors de la récupération des paramètres SMTP",updatingSmtpSettings:"Erreur lors de la mise à jour des paramètres SMTP",fetchingSecuritySettings:"Erreur lors de la récupération des paramètres de sécurité",updatingSecuritySettings:"Erreur lors de la mise à jour des paramètres de sécurité",fetchingGeneralSettings:"Erreur lors de la récupération des paramètres généraux",updatingGeneralSettings:"Erreur lors de la mise à jour des paramètres généraux"},Ys={smtpSettingsSaved:"Paramètres SMTP enregistrés avec succès",securitySettingsSaved:"Paramètres de sécurité enregistrés avec succès",generalSettingsSaved:"Paramètres généraux enregistrés avec succès"},Qs={title:"Paramètres",general:{title:"Paramètres généraux",siteName:"Nom du site",siteDescription:"Description du site",contactEmail:"Email de contact",contactPhone:"Téléphone de contact",address:"Adresse",logo:"Logo",favicon:"Favicon",primaryColor:"Couleur principale",secondaryColor:"Couleur secondaire",defaultLanguage:"Langue par défaut",availableLanguages:"Langues disponibles",dateFormat:"Format de date",timeFormat:"Format d'heure",timezone:"Fuseau horaire",appearance:"Apparence",localization:"Localisation",maintenance:"Maintenance",registration:"Inscription",social:"Médias sociaux",maintenanceMode:"Mode maintenance",maintenanceMessage:"Message de maintenance",allowUserRegistration:"Autoriser l'inscription des utilisateurs",defaultUserRole:"Rôle par défaut des utilisateurs",footerText:"Texte de pied de page",maxUploadSize:"Taille maximale d'upload (MB)",socialLinks:"Liens sociaux",facebook:"Facebook",twitter:"Twitter",instagram:"Instagram",linkedin:"LinkedIn",youtube:"YouTube"},smtp:{title:"Configuration SMTP",host:"Hôte SMTP",port:"Port SMTP",secure:"Connexion sécurisée",user:"Nom d'utilisateur",pass:"Mot de passe",fromName:"Nom d'expéditeur",fromEmail:"Email d'expéditeur",replyTo:"Répondre à",testEmailRecipient:"Destinataire de l'email de test",isEnabled:"Activer SMTP",testEmailButton:"Tester la configuration",testEmailSuccess:"Email de test envoyé avec succès",testEmailError:"Erreur lors de l'envoi de l'email de test"},security:{title:"Paramètres de sécurité",fetchError:"Erreur lors de la récupération des paramètres de sécurité",saveError:"Erreur lors de la mise à jour des paramètres de sécurité",saveSuccess:"Paramètres de sécurité enregistrés avec succès",authentication:{title:"Authentification",enable2FA:"Activer l'authentification à deux facteurs",sessionTimeout:"Délai d'expiration de la session (minutes)",maxLoginAttempts:"Nombre maximum de tentatives de connexion",lockoutDuration:"Durée de verrouillage (minutes)"},passwordPolicy:{title:"Politique de mot de passe",complexityRegex:"Expression régulière pour la complexité du mot de passe",complexityRegexHelp:"Expression régulière pour la validation du mot de passe (ex: ^(?=.*[a-z])(?=.*[A-Z])(?=.*\\d)[a-zA-Z\\d]{8,}$)",historyCount:"Nombre d'historique de mots de passe",historyCountHelp:"Nombre de mots de passe précédents à mémoriser pour éviter la réutilisation",expiryDays:"Expiration du mot de passe (jours)",expiryDaysHelp:"Nombre de jours après lesquels les mots de passe expirent (0 pour désactiver)"},apiSecurity:{title:"Sécurité de l'API",corsAllowedOrigins:"Origines CORS autorisées",corsAllowedOriginsHelp:"Liste d'origines autorisées pour CORS (ex: http://localhost:3000,https://votredomaine.com)",logLevel:"Niveau de journalisation",rateLimitingEnabled:"Activer la limitation du taux d'API",rateLimitRequests:"Nombre de requêtes API autorisées",rateLimitRequestsHelp:"Nombre maximum de requêtes dans la fenêtre de temps",rateLimitWindowMs:"Fenêtre de limitation (ms)",rateLimitWindowMsHelp:"Fenêtre de temps pour la limitation en millisecondes (ex: 900000 pour 15 minutes)"}}},Ft={common:Bs,auth:$s,dashboard:qs,admin:Fs,eleveur:Vs,veterinaire:Os,marchand:Us,volailles:Hs,notifications:Gs,errors:Zs,success:Ys,settings:Qs},Fn=Object.freeze(Object.defineProperty({__proto__:null,admin:Fs,auth:$s,common:Bs,dashboard:qs,default:Ft,eleveur:Vs,errors:Zs,marchand:Us,notifications:Gs,settings:Qs,success:Ys,veterinaire:Os,volailles:Hs},Symbol.toStringTag,{value:"Module"})),Js={appName:"بولتري دي زد",loading:"جاري التحميل...",save:"حفظ",cancel:"إلغاء",delete:"حذف",edit:"تعديل",view:"عرض",search:"بحث",filter:"تصفية",add:"إضافة",submit:"إرسال",reset:"إعادة تعيين",back:"رجوع",next:"التالي",previous:"السابق",yes:"نعم",no:"لا",confirm:"تأكيد",success:"نجاح",error:"خطأ",warning:"تحذير",info:"معلومات"},Ks={login:"تسجيل الدخول",logout:"تسجيل الخروج",register:"تسجيل",forgotPassword:"نسيت كلمة المرور",resetPassword:"إعادة تعيين كلمة المرور",changePassword:"تغيير كلمة المرور",email:"البريد الإلكتروني",password:"كلمة المرور",confirmPassword:"تأكيد كلمة المرور",username:"اسم المستخدم",firstName:"الاسم الأول",lastName:"اسم العائلة",role:"الدور",rememberMe:"تذكرني",loginButton:"تسجيل الدخول",registerButton:"تسجيل",alreadyHaveAccount:"لديك حساب بالفعل؟",dontHaveAccount:"ليس لديك حساب؟",loginSuccess:"تم تسجيل الدخول بنجاح",loginError:"خطأ في تسجيل الدخول",registerSuccess:"تم التسجيل بنجاح",registerError:"خطأ في التسجيل",logoutSuccess:"تم تسجيل الخروج بنجاح",passwordMismatch:"كلمات المرور غير متطابقة",passwordChanged:"تم تغيير كلمة المرور بنجاح",passwordResetSent:"تم إرسال تعليمات إعادة تعيين كلمة المرور إلى بريدك الإلكتروني",invalidCredentials:"البريد الإلكتروني أو كلمة المرور غير صحيحة"},Xs={dashboard:"لوحة التحكم",overview:"نظرة عامة",statistics:"إحصائيات",reports:"تقارير",analytics:"تحليلات",welcome:"مرحبا",quickActions:"إجراءات سريعة",recentActivity:"النشاط الأخير",notifications:"إشعارات",settings:"إعدادات",profile:"الملف الشخصي",help:"مساعدة"},er={adminPanel:"لوحة الإدارة",users:"المستخدمون",roles:"الأدوار",permissions:"الصلاحيات",settings:"الإعدادات",logs:"السجلات",system:"النظام",maintenance:"الصيانة",backups:"النسخ الاحتياطية",translations:"الترجمات",blog:"المدونة",ai:"الذكاء الاصطناعي",aiTools:"أدوات الذكاء الاصطناعي",blogGenerator:"مولد المدونة",dataAnalysis:"تحليل البيانات",pageContent:"محتوى الصفحة"},tr={dashboard:"لوحة تحكم المربي",myVolailles:"دواجني",mySales:"مبيعاتي",myStatistics:"إحصائياتي",addVolaille:"إضافة دواجن",editVolaille:"تعديل الدواجن",deleteVolaille:"حذف الدواجن",volailleDetails:"تفاصيل الدواجن",volailleStats:"إحصائيات الدواجن",volailleHealth:"صحة الدواجن",volailleFeeding:"تغذية الدواجن",volailleGrowth:"نمو الدواجن",volailleSales:"مبيعات الدواجن"},sr={dashboard:"لوحة تحكم الطبيب البيطري",consultations:"الاستشارات",prescriptions:"الوصفات الطبية",appointments:"المواعيد",patients:"المرضى",medicalRecords:"السجلات الطبية",treatments:"العلاجات",vaccines:"اللقاحات",diseases:"الأمراض",addConsultation:"إضافة استشارة",editConsultation:"تعديل استشارة",deleteConsultation:"حذف استشارة",addPrescription:"إضافة وصفة طبية",editPrescription:"تعديل وصفة طبية",deletePrescription:"حذف وصفة طبية"},rr={dashboard:"لوحة تحكم التاجر",products:"المنتجات",orders:"الطلبات",sales:"المبيعات",customers:"العملاء",inventory:"المخزون",shipping:"الشحن",returns:"المرتجعات",addProduct:"إضافة منتج",editProduct:"تعديل منتج",deleteProduct:"حذف منتج",productDetails:"تفاصيل المنتج",orderDetails:"تفاصيل الطلب",processOrder:"معالجة الطلب",cancelOrder:"إلغاء الطلب",refundOrder:"استرداد الطلب"},nr={volailles:"الدواجن",espece:"النوع",race:"السلالة",age:"العمر",poids:"الوزن",quantite:"الكمية",prix:"السعر",valeur:"القيمة",statut:"الحالة",description:"الوصف",dateAjout:"تاريخ الإضافة",eleveur:"المربي",poulets:"دجاج",dindes:"ديك رومي",canards:"بط",cailles:"سمان",oies:"إوز",pintades:"دجاج غينيا",disponible:"متاح",vendu:"مباع",reserve:"محجوز",malade:"مريض",traitement:"قيد العلاج",quarantaine:"في الحجر الصحي"},ar={newMessage:"رسالة جديدة",newOrder:"طلب جديد",orderStatus:"تم تحديث حالة الطلب",paymentReceived:"تم استلام الدفع",appointmentReminder:"تذكير بالموعد",systemUpdate:"تحديث النظام",lowStock:"مخزون منخفض",criticalAlert:"تنبيه حرج",markAllAsRead:"تعليم الكل كمقروء",clearAll:"مسح الكل",viewAll:"عرض الكل"},ir={title:"الإعدادات",general:{title:"الإعدادات العامة",siteName:"اسم الموقع",siteDescription:"وصف الموقع",contactEmail:"البريد الإلكتروني للاتصال",contactPhone:"هاتف الاتصال",address:"العنوان",logo:"الشعار",favicon:"أيقونة الموقع",primaryColor:"اللون الأساسي",secondaryColor:"اللون الثانوي",defaultLanguage:"اللغة الافتراضية",availableLanguages:"اللغات المتاحة",dateFormat:"تنسيق التاريخ",timeFormat:"تنسيق الوقت",timezone:"المنطقة الزمنية",appearance:"المظهر",localization:"التوطين",maintenance:"الصيانة",registration:"التسجيل",social:"وسائل التواصل الاجتماعي",maintenanceMode:"وضع الصيانة",maintenanceMessage:"رسالة الصيانة",allowUserRegistration:"السماح بتسجيل المستخدمين",defaultUserRole:"دور المستخدم الافتراضي",footerText:"نص التذييل",maxUploadSize:"الحد الأقصى لحجم التحميل (ميجابايت)",socialLinks:"روابط التواصل الاجتماعي",facebook:"فيسبوك",twitter:"تويتر",instagram:"انستغرام",linkedin:"لينكد إن",youtube:"يوتيوب"},smtp:{title:"إعدادات SMTP",host:"مضيف SMTP",port:"منفذ SMTP",secure:"اتصال آمن",user:"اسم المستخدم",pass:"كلمة المرور",fromName:"اسم المرسل",fromEmail:"بريد المرسل",replyTo:"الرد إلى",testEmailRecipient:"مستلم البريد الاختباري",isEnabled:"تفعيل SMTP",testEmailButton:"اختبار الإعدادات",testEmailSuccess:"تم إرسال بريد الاختبار بنجاح",testEmailError:"خطأ في إرسال بريد الاختبار"},security:{title:"إعدادات الأمان",fetchError:"خطأ في جلب إعدادات الأمان",saveError:"خطأ في تحديث إعدادات الأمان",saveSuccess:"تم حفظ إعدادات الأمان بنجاح",authentication:{title:"المصادقة",enable2FA:"تفعيل المصادقة الثنائية",sessionTimeout:"مهلة الجلسة (دقائق)",maxLoginAttempts:"الحد الأقصى لمحاولات تسجيل الدخول",lockoutDuration:"مدة القفل (دقائق)"},passwordPolicy:{title:"سياسة كلمة المرور",complexityRegex:"تعبير منتظم لتعقيد كلمة المرور",complexityRegexHelp:"تعبير منتظم للتحقق من كلمة المرور (مثال: ^(?=.*[a-z])(?=.*[A-Z])(?=.*\\d)[a-zA-Z\\d]{8,}$)",historyCount:"عدد سجل كلمات المرور",historyCountHelp:"عدد كلمات المرور السابقة التي يجب تذكرها لمنع إعادة الاستخدام",expiryDays:"انتهاء صلاحية كلمة المرور (أيام)",expiryDaysHelp:"عدد الأيام التي تنتهي بعدها صلاحية كلمات المرور (0 للتعطيل)"},apiSecurity:{title:"أمان API",corsAllowedOrigins:"أصول CORS المسموح بها",corsAllowedOriginsHelp:"قائمة الأصول المسموح بها لـ CORS (مثال: http://localhost:3000,https://yourdomain.com)",logLevel:"مستوى السجل",rateLimitingEnabled:"تفعيل تحديد معدل API",rateLimitRequests:"عدد طلبات API المسموح بها",rateLimitRequestsHelp:"الحد الأقصى لعدد الطلبات في نافذة الوقت",rateLimitWindowMs:"نافذة تحديد المعدل (مللي ثانية)",rateLimitWindowMsHelp:"نافذة زمنية للحد من المعدل بالميللي ثانية (مثال: 900000 لـ 15 دقيقة)"}}},or={required:"هذا الحقل مطلوب",invalidEmail:"بريد إلكتروني غير صحيح",minLength:"يجب أن يحتوي على الأقل على {min} أحرف",maxLength:"يجب ألا يتجاوز {max} أحرف",passwordMismatch:"كلمات المرور غير متطابقة",serverError:"خطأ في الخادم، يرجى المحاولة لاحقاً",notFound:"غير موجود",unauthorized:"غير مصرح",forbidden:"محظور",badRequest:"طلب غير صحيح",conflict:"تعارض في البيانات",networkError:"خطأ في الشبكة، يرجى التحقق من اتصالك",fetchingSmtpSettings:"خطأ في جلب إعدادات SMTP",updatingSmtpSettings:"خطأ في تحديث إعدادات SMTP",fetchingSecuritySettings:"خطأ في جلب إعدادات الأمان",updatingSecuritySettings:"خطأ في تحديث إعدادات الأمان",fetchingGeneralSettings:"خطأ في جلب الإعدادات العامة",updatingGeneralSettings:"خطأ في تحديث الإعدادات العامة"},lr={smtpSettingsSaved:"تم حفظ إعدادات SMTP بنجاح",securitySettingsSaved:"تم حفظ إعدادات الأمان بنجاح",generalSettingsSaved:"تم حفظ الإعدادات العامة بنجاح"},Vt={common:Js,auth:Ks,dashboard:Xs,admin:er,eleveur:tr,veterinaire:sr,marchand:rr,volailles:nr,notifications:ar,settings:ir,errors:or,success:lr},Vn=Object.freeze(Object.defineProperty({__proto__:null,admin:er,auth:Ks,common:Js,dashboard:Xs,default:Vt,eleveur:tr,errors:or,marchand:rr,notifications:ar,settings:ir,success:lr,veterinaire:sr,volailles:nr},Symbol.toStringTag,{value:"Module"})),cr=i.createContext(),Et=()=>i.useContext(cr),On=({children:t})=>{const[r,l]=i.useState(localStorage.getItem("language")||"fr"),[x,s]=i.useState(r==="ar"?Vt:Ft),[d,h]=i.useState(r==="ar"?"rtl":"ltr");i.useEffect(()=>{s(r==="ar"?Vt:Ft),h(r==="ar"?"rtl":"ltr"),localStorage.setItem("language",r),document.documentElement.dir=r==="ar"?"rtl":"ltr",document.documentElement.lang=r},[r]);const p={language:r,changeLanguage:a=>{(a==="fr"||a==="ar")&&l(a)},t:a=>{const D=a.split(".");let E=x;for(const v of D)if(E&&E[v])E=E[v];else return a;return E},direction:d,isRTL:r==="ar"};return e.jsx(cr.Provider,{value:p,children:t})},ds=({color:t="inherit",variant:r="icon"})=>{const{language:l,changeLanguage:x}=Et(),[s,d]=React.useState(null),h=a=>{d(a.currentTarget)},j=()=>{d(null)},g=a=>{x(a),j()},p=a=>{switch(a){case"fr":return"Français";case"ar":return"العربية";default:return a}};return e.jsxs(u,{children:[r==="icon"?e.jsx(B,{color:t,onClick:h,"aria-controls":"language-menu","aria-haspopup":"true",children:e.jsx(ss,{})}):e.jsxs(u,{onClick:h,sx:{display:"flex",alignItems:"center",cursor:"pointer",color:t},children:[e.jsx(ss,{sx:{mr:.5}}),e.jsx(c,{variant:"body2",children:p(l)})]}),e.jsxs(it,{id:"language-menu",anchorEl:s,keepMounted:!0,open:!!s,onClose:j,children:[e.jsx(I,{onClick:()=>g("fr"),selected:l==="fr",children:"Français"}),e.jsx(I,{onClick:()=>g("ar"),selected:l==="ar",children:"العربية"})]})]})},dr={fr:{navbar:{home:"Accueil",features:"Fonctionnalités",pricing:"Tarifs",faq:"FAQ",contact:"Contact",createAccount:"Créer un compte",login:"Connexion"},hero:{title:"Révolutionnez votre élevage de volailles",subtitle:"La première plateforme algérienne dédiée à la gestion et à la commercialisation des volailles.",createAccount:"Créer un compte",contactUs:"Contactez-nous"},about:{title:"À Propos de Poultray DZ",paragraph1:"Poultray DZ est née de la volonté de moderniser le secteur avicole en Algérie. Notre mission est de connecter tous les acteurs de la filière avicole - éleveurs, vétérinaires, fournisseurs et acheteurs - sur une plateforme unique et intuitive.",paragraph2:"Grâce à nos outils innovants et notre expertise du secteur, nous aidons les professionnels à optimiser leur production, améliorer la santé des volailles et faciliter la commercialisation des produits.",stats:{activeBreeder:"Éleveurs Actifs",poultryBatches:"Lots de Volailles",veterinarians:"Vétérinaires",coveredRegions:"Wilayas Couvertes"}},features:{title:"Nos Fonctionnalités",breederManagement:{title:"Gestion des Éleveurs",description:"Suivez et gérez facilement tous vos éleveurs partenaires avec des profils détaillés et des statistiques en temps réel."},veterinaryMonitoring:{title:"Suivi Vétérinaire",description:"Accédez à des services vétérinaires à distance et suivez la santé de vos volailles avec des rapports détaillés."},marketplace:{title:"Marketplace",description:"Vendez et achetez des volailles directement sur la plateforme avec un système de paiement sécurisé."},smartAgriculture:{title:"Agriculture Intelligente",description:"Optimisez votre production grâce à des outils d'analyse avancés et des recommandations personnalisées."},notifications:{title:"Notifications",description:"Restez informé des événements importants avec notre système de notifications en temps réel."},artificialIntelligence:{title:"Intelligence Artificielle",description:"Bénéficiez de prédictions et d'analyses basées sur l'IA pour améliorer votre productivité."}},testimonials:{title:"Ce que disent nos utilisateurs",testimonial1:{name:"Ahmed Benali",role:"Éleveur de poulets de chair",content:"Depuis que j'utilise Poultray DZ, la gestion de mon élevage est devenue beaucoup plus simple. Je peux suivre la croissance de mes volailles et anticiper les problèmes avant qu'ils ne surviennent."},testimonial2:{name:"Samira Hadj",role:"Vétérinaire avicole",content:"La plateforme me permet de suivre à distance plusieurs élevages et d'intervenir rapidement en cas de besoin. Un outil indispensable pour les professionnels de la santé animale."},testimonial3:{name:"Karim Meziane",role:"Distributeur de produits avicoles",content:"Grâce à Poultray DZ, j'ai pu élargir ma clientèle et optimiser mes livraisons. La marketplace est intuitive et sécurisée."}},pricing:{title:"Nos Offres",free:{title:"Gratuit",price:"0 DA",features:["Accès limité au tableau de bord","Gestion de base des éleveurs","Notifications essentielles","Support communautaire"],buttonText:"Commencer"},standard:{title:"Standard",price:"2 500 DA/mois",features:["Tableau de bord complet","Gestion avancée des éleveurs","Suivi vétérinaire de base","Marketplace avec commission réduite","Support par email"],buttonText:"Essai Gratuit"},premium:{title:"Premium",price:"5 000 DA/mois",features:["Toutes les fonctionnalités Standard","Analyses IA avancées","Suivi vétérinaire illimité","Marketplace sans commission","Support prioritaire 24/7","Formation personnalisée"],buttonText:"Contacter les Ventes"}},faq:{title:"Questions Fréquentes",questions:[{question:"Comment puis-je m'inscrire sur Poultray DZ ?",answer:`L'inscription est simple et rapide. Cliquez sur le bouton "Créer un compte" en haut de la page, remplissez le formulaire avec vos informations et choisissez votre type de profil (éleveur, vétérinaire, acheteur, etc.).`},{question:"Quels types d'élevages sont supportés par la plateforme ?",answer:"Poultray DZ prend en charge tous les types d'élevages avicoles : poulets de chair, poules pondeuses, dindes, cailles, et autres volailles. Chaque type d'élevage bénéficie de fonctionnalités spécifiques adaptées à ses besoins."},{question:"Comment fonctionne le système de suivi vétérinaire ?",answer:"Les éleveurs peuvent solliciter des consultations vétérinaires directement via la plateforme. Les vétérinaires reçoivent les demandes, consultent les données de l'élevage et peuvent fournir des recommandations ou planifier des visites si nécessaire."},{question:"La plateforme est-elle accessible sur mobile ?",answer:"Oui, Poultray DZ est entièrement responsive et fonctionne sur tous les appareils. Nous proposons également une application mobile dédiée pour Android et iOS pour une expérience optimisée."},{question:"Comment sont sécurisées mes données sur la plateforme ?",answer:"Nous utilisons des protocoles de cryptage avancés pour protéger toutes les données. Nos serveurs sont sécurisés et nous effectuons des sauvegardes régulières. Nous respectons également les normes internationales de protection des données."}]},contact:{title:"Contactez-Nous",subtitle:"Nous sommes là pour vous aider",form:{firstName:"Prénom",lastName:"Nom",email:"Email",phone:"Téléphone",subject:"Sujet",message:"Message",send:"Envoyer",success:"Votre message a été envoyé avec succès!"},address:"Adresse",phone:"Téléphone",email:"Email"},footer:{about:"À propos",aboutText:"Poultray DZ est la première plateforme algérienne dédiée à la gestion et à la commercialisation des volailles.",links:"Liens Rapides",services:"Services",legal:"Mentions Légales",terms:"Conditions d'utilisation",privacy:"Politique de confidentialité",copyright:"© 2023 Poultray DZ. Tous droits réservés."}},ar:{navbar:{home:"الرئيسية",features:"المميزات",pricing:"الأسعار",faq:"الأسئلة الشائعة",contact:"اتصل بنا",createAccount:"إنشاء حساب",login:"تسجيل الدخول"},hero:{title:"ثورة في تربية الدواجن",subtitle:"المنصة الجزائرية الأولى المخصصة لإدارة وتسويق الدواجن.",createAccount:"إنشاء حساب",contactUs:"اتصل بنا"},about:{title:"نبذة عن بولتري دي زد",paragraph1:"ولدت بولتري دي زد من الرغبة في تحديث قطاع الدواجن في الجزائر. مهمتنا هي ربط جميع الفاعلين في سلسلة الدواجن - المربين والأطباء البيطريين والموردين والمشترين - على منصة واحدة وبديهية.",paragraph2:"بفضل أدواتنا المبتكرة وخبرتنا في القطاع، نساعد المهنيين على تحسين إنتاجهم، وتحسين صحة الدواجن وتسهيل تسويق المنتجات.",stats:{activeBreeder:"مربي نشط",poultryBatches:"دفعات الدواجن",veterinarians:"الأطباء البيطريين",coveredRegions:"الولايات المغطاة"}},features:{title:"مميزاتنا",breederManagement:{title:"إدارة المربين",description:"تتبع وإدارة جميع شركائك المربين بسهولة مع ملفات تعريف مفصلة وإحصاءات في الوقت الحقيقي."},veterinaryMonitoring:{title:"المراقبة البيطرية",description:"الوصول إلى الخدمات البيطرية عن بعد ومراقبة صحة الدواجن مع تقارير مفصلة."},marketplace:{title:"السوق",description:"بيع وشراء الدواجن مباشرة على المنصة مع نظام دفع آمن."},smartAgriculture:{title:"الزراعة الذكية",description:"تحسين إنتاجك من خلال أدوات التحليل المتقدمة والتوصيات المخصصة."},notifications:{title:"الإشعارات",description:"البقاء على اطلاع بالأحداث المهمة مع نظام الإشعارات في الوقت الحقيقي."},artificialIntelligence:{title:"الذكاء الاصطناعي",description:"الاستفادة من التنبؤات والتحليلات القائمة على الذكاء الاصطناعي لتحسين إنتاجيتك."}},testimonials:{title:"ما يقوله مستخدمونا",testimonial1:{name:"أحمد بن علي",role:"مربي دجاج اللحم",content:"منذ أن بدأت استخدام بولتري دي زد، أصبحت إدارة تربيتي أسهل بكثير. يمكنني متابعة نمو الدواجن وتوقع المشاكل قبل حدوثها."},testimonial2:{name:"سميرة حاج",role:"طبيبة بيطرية للدواجن",content:"تتيح لي المنصة متابعة العديد من المزارع عن بعد والتدخل بسرعة عند الحاجة. أداة لا غنى عنها لمتخصصي صحة الحيوان."},testimonial3:{name:"كريم مزيان",role:"موزع منتجات الدواجن",content:"بفضل بولتري دي زد، تمكنت من توسيع قاعدة عملائي وتحسين عمليات التسليم. السوق سهل الاستخدام وآمن."}},pricing:{title:"عروضنا",free:{title:"مجاني",price:"0 دج",features:["وصول محدود إلى لوحة التحكم","إدارة أساسية للمربين","إشعارات أساسية","دعم المجتمع"],buttonText:"ابدأ الآن"},standard:{title:"قياسي",price:"2500 دج/شهر",features:["لوحة تحكم كاملة","إدارة متقدمة للمربين","متابعة بيطرية أساسية","سوق بعمولة مخفضة","دعم عبر البريد الإلكتروني"],buttonText:"تجربة مجانية"},premium:{title:"بريميوم",price:"5000 دج/شهر",features:["جميع ميزات الباقة القياسية","تحليلات ذكاء اصطناعي متقدمة","متابعة بيطرية غير محدودة","سوق بدون عمولة","دعم ذو أولوية 24/7","تدريب مخصص"],buttonText:"اتصل بالمبيعات"}},faq:{title:"الأسئلة الشائعة",questions:[{question:"كيف يمكنني التسجيل في بولتري دي زد؟",answer:'التسجيل بسيط وسريع. انقر على زر "إنشاء حساب" في أعلى الصفحة، املأ النموذج بمعلوماتك واختر نوع ملفك الشخصي (مربي، طبيب بيطري، مشتري، إلخ).'},{question:"ما هي أنواع تربية الدواجن التي تدعمها المنصة؟",answer:"تدعم بولتري دي زد جميع أنواع تربية الدواجن: دجاج اللحم، الدجاج البياض، الديك الرومي، السمان، وغيرها من الدواجن. كل نوع من أنواع التربية يستفيد من ميزات محددة مصممة لتلبية احتياجاته."},{question:"كيف يعمل نظام المراقبة البيطرية؟",answer:"يمكن للمربين طلب استشارات بيطرية مباشرة عبر المنصة. يتلقى الأطباء البيطريون الطلبات، ويراجعون بيانات التربية ويمكنهم تقديم توصيات أو تخطيط زيارات إذا لزم الأمر."},{question:"هل المنصة متاحة على الهاتف المحمول؟",answer:"نعم، بولتري دي زد متوافقة تمامًا مع جميع الأجهزة. نقدم أيضًا تطبيقًا مخصصًا للهاتف المحمول لنظامي Android و iOS لتجربة محسنة."},{question:"كيف يتم تأمين بياناتي على المنصة؟",answer:"نستخدم بروتوكولات تشفير متقدمة لحماية جميع البيانات. خوادمنا آمنة ونقوم بعمل نسخ احتياطية منتظمة. نحترم أيضًا المعايير الدولية لحماية البيانات."}]},contact:{title:"اتصل بنا",subtitle:"نحن هنا لمساعدتك",form:{firstName:"الاسم الأول",lastName:"اللقب",email:"البريد الإلكتروني",phone:"الهاتف",subject:"الموضوع",message:"الرسالة",send:"إرسال",success:"تم إرسال رسالتك بنجاح!"},address:"العنوان",phone:"الهاتف",email:"البريد الإلكتروني"},footer:{about:"نبذة عنا",aboutText:"بولتري دي زد هي المنصة الجزائرية الأولى المخصصة لإدارة وتسويق الدواجن.",links:"روابط سريعة",services:"الخدمات",legal:"المعلومات القانونية",terms:"شروط الاستخدام",privacy:"سياسة الخصوصية",copyright:"© 2023 بولتري دي زد. جميع الحقوق محفوظة."}}},Un=he(Gt)(({theme:t,trigger:r})=>({boxShadow:r?t.shadows[4]:"none",backgroundColor:r?t.palette.background.paper:"transparent",color:r?t.palette.text.primary:t.palette.common.white,transition:"all 0.3s ease",position:"fixed","& .MuiAppBar-root":{trigger:void 0}})),Hn=he(M)(({theme:t,trigger:r})=>({marginLeft:t.spacing(2),color:r?t.palette.primary.main:t.palette.common.white,fontWeight:500,"&:hover":{backgroundColor:r?"rgba(76, 175, 80, 0.08)":"rgba(255, 255, 255, 0.1)"}})),Gn=he(u)(({theme:t})=>({display:"flex",alignItems:"center","& img":{height:40,marginRight:t.spacing(1)}})),Zn=he(Bt)(({theme:t})=>({"& .MuiDrawer-paper":{width:250,backgroundColor:t.palette.primary.main,color:t.palette.common.white,padding:t.spacing(2)}})),Yn=he(u)(({theme:t})=>({display:"flex",alignItems:"center",justifyContent:"space-between",marginBottom:t.spacing(2)})),Qn=he(oe)(({theme:t})=>({borderRadius:t.shape.borderRadius,marginBottom:t.spacing(1),"&:hover":{backgroundColor:"rgba(255, 255, 255, 0.1)"}})),us=he(M)(({theme:t,trigger:r})=>({borderRadius:30,padding:t.spacing(1,3),backgroundColor:r?t.palette.primary.main:t.palette.secondary.main,color:t.palette.common.white,fontWeight:"bold",boxShadow:"0 4px 10px rgba(0, 0, 0, 0.15)","&:hover":{backgroundColor:r?t.palette.primary.dark:t.palette.secondary.dark,transform:"translateY(-2px)",boxShadow:"0 6px 15px rgba(0, 0, 0, 0.2)"},transition:"all 0.3s ease"}));function Jn(){const[t,r]=i.useState(!1),l=$t(p=>p.breakpoints.down("md")),{language:x}=Et(),s=dr[x],d=Nr({disableHysteresis:!0,threshold:100}),h=()=>{r(!t)},j=p=>{const a=document.getElementById(p);a&&(a.scrollIntoView({behavior:"smooth"}),r(!1))},g=[{name:s.navbar.home,path:"accueil"},{name:s.navbar.features,path:"fonctionnalites"},{name:s.navbar.pricing,path:"tarifs"},{name:s.navbar.faq,path:"faq"},{name:s.navbar.contact,path:"contact"}];return e.jsxs(e.Fragment,{children:[e.jsx(Un,{trigger:d?"true":"false",children:e.jsx(ee,{children:e.jsxs(ut,{disableGutters:!0,children:[e.jsxs(Gn,{sx:{flexGrow:1},children:[e.jsx("img",{src:qn,alt:"Poultray DZ Logo"}),e.jsx(c,{variant:"h6",component:ce,to:"/",sx:{textDecoration:"none",color:d?"primary.main":"white",fontWeight:"bold"},children:"Poultray DZ"})]}),l?e.jsx(B,{edge:"end",color:"inherit","aria-label":"menu",onClick:h,children:e.jsx(_r,{})}):e.jsx(e.Fragment,{children:e.jsxs(u,{sx:{display:"flex",alignItems:"center"},children:[g.map(p=>e.jsx(Hn,{onClick:()=>j(p.path),trigger:d?"true":"false",children:p.name},p.name)),e.jsxs(u,{sx:{display:"flex",alignItems:"center"},children:[e.jsx(ds,{color:d?"primary":"inherit"}),e.jsx(us,{variant:"contained",component:ce,to:"/login",trigger:d?"true":"false",sx:{ml:2,mr:1,backgroundColor:d?"secondary.main":"primary.main"},children:s.navbar.login||"Connexion"}),e.jsx(us,{variant:"contained",component:ce,to:"/register",trigger:d?"true":"false",children:s.navbar.createAccount})]})]})})]})})}),e.jsxs(Zn,{anchor:"right",open:t,onClose:h,children:[e.jsxs(Yn,{children:[e.jsx(c,{variant:"h6",fontWeight:"bold",children:"Menu"}),e.jsx(B,{onClick:h,sx:{color:"white"},children:e.jsx(Br,{})})]}),e.jsxs(le,{children:[g.map(p=>e.jsx(Qn,{onClick:()=>j(p.path),button:!0,children:e.jsx(ae,{primary:p.name})},p.name)),e.jsxs(u,{sx:{mt:2},children:[e.jsx(u,{sx:{display:"flex",justifyContent:"center",mb:2},children:e.jsx(ds,{variant:"text",color:"white"})}),e.jsx(M,{variant:"contained",fullWidth:!0,component:ce,to:"/login",sx:{backgroundColor:"white",color:"secondary.main","&:hover":{backgroundColor:"rgba(255, 255, 255, 0.9)"},mb:1},children:s.navbar.login||"Connexion"}),e.jsx(M,{variant:"contained",fullWidth:!0,component:ce,to:"/register",sx:{backgroundColor:"white",color:"primary.main","&:hover":{backgroundColor:"rgba(255, 255, 255, 0.9)"}},children:s.navbar.createAccount})]})]})]}),e.jsx(ut,{})]})}const Kn=he(u)(({theme:t})=>({minHeight:"80vh",display:"flex",alignItems:"center",background:`linear-gradient(135deg, ${t.palette.primary.light} 0%, ${t.palette.primary.main} 100%)`,color:t.palette.common.white,padding:t.spacing(4,0),position:"relative",overflow:"hidden"})),ft=he(M)(({theme:t})=>({borderRadius:"30px",padding:t.spacing(1,4),fontWeight:"bold",boxShadow:"0 4px 10px rgba(0, 0, 0, 0.15)",transition:"transform 0.2s","&:hover":{transform:"translateY(-3px)"}})),Xn=he(Z)(({theme:t})=>({height:"100%",display:"flex",flexDirection:"column",transition:"transform 0.3s, box-shadow 0.3s","&:hover":{transform:"translateY(-10px)",boxShadow:"0 12px 20px rgba(0, 0, 0, 0.1)"}})),Ue=he(c)(({theme:t})=>({position:"relative",marginBottom:t.spacing(6),"&:after":{content:'""',position:"absolute",bottom:"-10px",left:"50%",width:"80px",height:"4px",background:t.palette.primary.main,transform:"translateX(-50%)"}})),ea=he(_)(({theme:t,featured:r})=>({padding:t.spacing(4),height:"100%",display:"flex",flexDirection:"column",alignItems:"center",textAlign:"center",transition:"transform 0.3s",border:r?`2px solid ${t.palette.primary.main}`:"none",transform:r?"scale(1.05)":"none","&:hover":{transform:r?"scale(1.08)":"scale(1.03)"}})),ta=he(_)(({theme:t})=>({padding:t.spacing(3),marginBottom:t.spacing(2),position:"relative","&:before":{content:'"“"',position:"absolute",top:"-20px",left:"20px",fontSize:"60px",color:t.palette.primary.light,opacity:.5}})),sa=he(u)(({theme:t})=>({padding:t.spacing(4),borderRadius:t.shape.borderRadius,boxShadow:"0 5px 20px rgba(0, 0, 0, 0.1)",background:t.palette.background.paper}));function ra(){const t=$r();ke();const r=$t(t.breakpoints.down("sm"));$t(t.breakpoints.down("md"));const l=i.useRef(null),{language:x}=Et(),s=dr[x],[d,h]=i.useState({nom:"",prenom:"",email:"",telephone:"",sujet:"",message:""}),[j,g]=i.useState({}),[p,a]=i.useState(!1),[D,E]=i.useState({open:!1,message:"",severity:"success"}),v=y=>{const{name:C,value:m}=y.target;h(o=>({...o,[C]:m})),j[C]&&g(o=>({...o,[C]:""}))},f=()=>{const y={};return d.nom.trim()||(y.nom="Le nom est requis"),d.prenom.trim()||(y.prenom="Le prénom est requis"),d.email.trim()?/^[^\s@]+@[^\s@]+\.[^\s@]+$/.test(d.email)||(y.email="Format d'email invalide"):y.email="L'email est requis",d.message.trim()||(y.message="Le message est requis"),y},P=y=>{y.preventDefault();const C=f();if(Object.keys(C).length>0){g(C);return}a(!0),setTimeout(()=>{a(!1),E({open:!0,message:s.contact.form.success,severity:"success"}),h({nom:"",prenom:"",email:"",telephone:"",sujet:"",message:""})},1500)},W=()=>{E(y=>({...y,open:!1}))},b=()=>{l.current?.scrollIntoView({behavior:"smooth"})};return i.useEffect(()=>{document.querySelectorAll(".animate-fadeInUp").forEach((o,k)=>{o.style.animationDelay=`${.1*k}s`});const C=new IntersectionObserver(o=>{o.forEach(k=>{k.isIntersecting&&(k.target.classList.add("visible"),C.unobserve(k.target))})},{threshold:.1}),m=document.querySelectorAll(".scroll-anim");return m.forEach(o=>C.observe(o)),()=>{m.forEach(o=>C.unobserve(o))}},[]),e.jsxs(u,{children:[e.jsx(Jn,{}),e.jsx(Kn,{id:"accueil",children:e.jsx(ee,{children:e.jsxs(n,{container:!0,spacing:4,alignItems:"center",children:[e.jsxs(n,{item:!0,xs:12,md:6,children:[e.jsx(c,{variant:"h2",component:"h1",gutterBottom:!0,fontWeight:"bold",className:"animate-fadeInUp",children:s.hero.title}),e.jsx(c,{variant:"h5",paragraph:!0,className:"animate-fadeInUp delay-200",children:s.hero.subtitle}),e.jsxs(u,{sx:{mt:4,display:"flex",flexDirection:r?"column":"row",gap:2},className:"animate-fadeInUp delay-300",children:[e.jsx(ft,{variant:"contained",color:"secondary",size:"large",component:ce,to:"/register",className:"cta-button animate-pulse",children:s.hero.createAccount}),e.jsx(ft,{variant:"outlined",color:"inherit",size:"large",onClick:b,children:s.hero.contactUs})]})]}),e.jsx(n,{item:!0,xs:12,md:6,sx:{display:{xs:"none",md:"block"}},children:e.jsx(i.Suspense,{fallback:e.jsx(u,{sx:{display:"flex",justifyContent:"center",alignItems:"center",height:"300px"},children:e.jsx(H,{})}),children:e.jsx(u,{component:"img",src:$n,alt:"Poultray DZ - Gestion d'élevage de volailles",className:"animate-float",loading:"lazy",sx:{width:"100%",maxWidth:"500px",display:"block",margin:"0 auto",transition:"transform 0.5s ease-in-out","&:hover":{transform:"scale(1.05)"}}})})})]})})}),e.jsx(u,{id:"about",sx:{py:10,backgroundColor:t.palette.background.paper},children:e.jsxs(ee,{children:[e.jsx(Ue,{variant:"h3",component:"h2",align:"center",children:s.about.title}),e.jsx(n,{container:!0,spacing:4,justifyContent:"center",children:e.jsxs(n,{item:!0,xs:12,md:8,children:[e.jsx(c,{variant:"body1",paragraph:!0,align:"center",children:s.about.paragraph1}),e.jsx(c,{variant:"body1",paragraph:!0,align:"center",children:s.about.paragraph2})]})}),e.jsx(u,{sx:{mt:6},children:e.jsxs(n,{container:!0,spacing:4,children:[e.jsx(n,{item:!0,xs:12,sm:6,md:3,children:e.jsxs(u,{sx:{textAlign:"center"},children:[e.jsx(c,{variant:"h2",color:"primary",fontWeight:"bold",children:"500+"}),e.jsx(c,{variant:"body1",children:s.about.stats.activeBreeder})]})}),e.jsx(n,{item:!0,xs:12,sm:6,md:3,children:e.jsxs(u,{sx:{textAlign:"center"},children:[e.jsx(c,{variant:"h2",color:"primary",fontWeight:"bold",children:"1200+"}),e.jsx(c,{variant:"body1",children:s.about.stats.poultryBatches})]})}),e.jsx(n,{item:!0,xs:12,sm:6,md:3,children:e.jsxs(u,{sx:{textAlign:"center"},children:[e.jsx(c,{variant:"h2",color:"primary",fontWeight:"bold",children:"50+"}),e.jsx(c,{variant:"body1",children:s.about.stats.veterinarians})]})}),e.jsx(n,{item:!0,xs:12,sm:6,md:3,children:e.jsxs(u,{sx:{textAlign:"center"},children:[e.jsx(c,{variant:"h2",color:"primary",fontWeight:"bold",children:"15"}),e.jsx(c,{variant:"body1",children:s.about.stats.coveredRegions})]})})]})})]})}),e.jsx(u,{sx:{py:10},id:"fonctionnalites",className:"scroll-anim fade-in",children:e.jsxs(ee,{children:[e.jsx(Ue,{variant:"h3",component:"h2",align:"center",children:s.features.title}),e.jsx(n,{container:!0,spacing:4,children:[{title:s.features.breederManagement.title,description:s.features.breederManagement.description,icon:e.jsx(qr,{fontSize:"large",color:"primary"})},{title:s.features.veterinaryMonitoring.title,description:s.features.veterinaryMonitoring.description,icon:e.jsx(Fr,{fontSize:"large",color:"primary"})},{title:s.features.marketplace.title,description:s.features.marketplace.description,icon:e.jsx(Vr,{fontSize:"large",color:"primary"})},{title:s.features.smartAgriculture.title,description:s.features.smartAgriculture.description,icon:e.jsx(Or,{fontSize:"large",color:"primary"})},{title:s.features.notifications.title,description:s.features.notifications.description,icon:e.jsx(Ur,{fontSize:"large",color:"primary"})},{title:s.features.artificialIntelligence.title,description:s.features.artificialIntelligence.description,icon:e.jsx(Hr,{fontSize:"large",color:"primary"})}].map((y,C)=>e.jsx(n,{item:!0,xs:12,sm:6,md:4,className:"scroll-anim slide-in-up",sx:{animationDelay:`${.1*C}s`},children:e.jsx(Xn,{className:`feature-card animate-fadeInUp delay-${C*100}`,children:e.jsxs(Y,{sx:{flexGrow:1,textAlign:"center"},children:[e.jsx(u,{sx:{mb:2},className:"animate-float",children:y.icon}),e.jsx(c,{variant:"h5",component:"h3",gutterBottom:!0,children:y.title}),e.jsx(c,{variant:"body2",color:"text.secondary",children:y.description})]})})},C))})]})}),e.jsx(u,{sx:{py:10,backgroundColor:t.palette.grey[50]},children:e.jsxs(ee,{children:[e.jsx(Ue,{variant:"h3",component:"h2",align:"center",children:s.testimonials.title}),e.jsx(n,{container:!0,spacing:4,children:[s.testimonials.testimonial1,s.testimonials.testimonial2,s.testimonials.testimonial3].map((y,C)=>e.jsx(n,{item:!0,xs:12,md:4,children:e.jsxs(ta,{children:[e.jsx(c,{variant:"body1",paragraph:!0,children:y.content}),e.jsxs(u,{sx:{display:"flex",alignItems:"center"},children:[e.jsx(u,{sx:{width:50,height:50,borderRadius:"50%",backgroundColor:t.palette.primary.main,color:"white",display:"flex",alignItems:"center",justifyContent:"center",fontWeight:"bold",mr:2},children:y.name.charAt(0)}),e.jsxs(u,{children:[e.jsx(c,{variant:"subtitle1",fontWeight:"bold",children:y.name}),e.jsx(c,{variant:"body2",color:"text.secondary",children:y.role})]})]})]})},C))})]})}),e.jsx(u,{sx:{py:10},id:"tarifs",className:"scroll-anim fade-in",children:e.jsxs(ee,{children:[e.jsx(Ue,{variant:"h3",component:"h2",align:"center",children:s.pricing.title}),e.jsx(n,{container:!0,spacing:4,justifyContent:"center",children:[{...s.pricing.free,featured:!1},{...s.pricing.standard,featured:!0},{...s.pricing.premium,featured:!1}].map((y,C)=>e.jsx(n,{item:!0,xs:12,sm:6,md:4,className:"scroll-anim slide-in-up",sx:{animationDelay:`${.2*C}s`},children:e.jsxs(ea,{featured:y.featured?"true":"false",elevation:y.featured?8:2,children:[e.jsx(c,{variant:"h4",component:"h3",gutterBottom:!0,children:y.title}),e.jsx(c,{variant:"h3",color:"primary",gutterBottom:!0,fontWeight:"bold",children:y.price}),e.jsx(u,{sx:{my:3,width:"100%"},children:y.features.map((m,o)=>e.jsx(c,{variant:"body2",sx:{py:1,borderBottom:`1px solid ${t.palette.divider}`},children:m},o))}),e.jsx(u,{sx:{mt:"auto",pt:2,width:"100%"},children:e.jsx(ft,{variant:y.featured?"contained":"outlined",color:(y.featured,"primary"),fullWidth:!0,onClick:b,className:y.featured?"animate-pulse":"",children:y.buttonText})})]})},C))})]})}),e.jsx(u,{sx:{py:10,backgroundColor:t.palette.grey[50]},id:"faq",className:"scroll-anim fade-in",children:e.jsxs(ee,{children:[e.jsx(Ue,{variant:"h3",component:"h2",align:"center",children:s.faq.title}),e.jsx(n,{container:!0,spacing:2,justifyContent:"center",children:e.jsx(n,{item:!0,xs:12,md:8,children:s.faq.questions.map((y,C)=>e.jsxs(As,{sx:{mb:2},className:"scroll-anim slide-in-left",style:{animationDelay:`${.1*C}s`},children:[e.jsx(Ds,{expandIcon:e.jsx(ks,{}),"aria-controls":`panel${C}-content`,id:`panel${C}-header`,children:e.jsx(c,{variant:"subtitle1",fontWeight:"medium",children:y.question})}),e.jsx(Ps,{children:e.jsx(c,{variant:"body2",children:y.answer})})]},C))})})]})}),e.jsxs(u,{sx:{py:10},id:"contact",ref:l,className:"scroll-anim fade-in",children:[e.jsxs(ee,{children:[e.jsx(Ue,{variant:"h3",component:"h2",align:"center",children:s.contact.title}),e.jsxs(n,{container:!0,spacing:4,justifyContent:"center",children:[e.jsx(n,{item:!0,xs:12,md:6,children:e.jsx(sa,{component:"form",onSubmit:P,className:"animate-fadeInUp",children:e.jsxs(n,{container:!0,spacing:3,children:[e.jsx(n,{item:!0,xs:12,sm:6,children:e.jsx(A,{fullWidth:!0,label:s.contact.form.lastName,name:"nom",variant:"outlined",required:!0,value:d.nom,onChange:v,error:!!j.nom,helperText:j.nom,disabled:p})}),e.jsx(n,{item:!0,xs:12,sm:6,children:e.jsx(A,{fullWidth:!0,label:s.contact.form.firstName,name:"prenom",variant:"outlined",required:!0,value:d.prenom,onChange:v,error:!!j.prenom,helperText:j.prenom,disabled:p})}),e.jsx(n,{item:!0,xs:12,children:e.jsx(A,{fullWidth:!0,label:s.contact.form.email,name:"email",variant:"outlined",type:"email",required:!0,value:d.email,onChange:v,error:!!j.email,helperText:j.email,disabled:p})}),e.jsx(n,{item:!0,xs:12,children:e.jsx(A,{fullWidth:!0,label:s.contact.form.phone,name:"telephone",variant:"outlined",value:d.telephone,onChange:v,disabled:p})}),e.jsx(n,{item:!0,xs:12,children:e.jsxs(Q,{fullWidth:!0,variant:"outlined",children:[e.jsx(J,{id:"sujet-label",children:s.contact.form.subject}),e.jsxs(K,{labelId:"sujet-label",name:"sujet",value:d.sujet,onChange:v,label:s.contact.form.subject,disabled:p,children:[e.jsx(I,{value:"",children:e.jsx("em",{children:"Sélectionnez un sujet"})}),e.jsx(I,{value:"information",children:"Demande d'information"}),e.jsx(I,{value:"partenariat",children:"Proposition de partenariat"}),e.jsx(I,{value:"technique",children:"Support technique"}),e.jsx(I,{value:"autre",children:"Autre"})]})]})}),e.jsx(n,{item:!0,xs:12,children:e.jsx(A,{fullWidth:!0,label:s.contact.form.message,name:"message",variant:"outlined",multiline:!0,rows:4,required:!0,value:d.message,onChange:v,error:!!j.message,helperText:j.message,disabled:p})}),e.jsx(n,{item:!0,xs:12,children:e.jsx(ft,{variant:"contained",color:"primary",size:"large",fullWidth:!0,type:"submit",disabled:p,startIcon:p?e.jsx(H,{size:20,color:"inherit"}):e.jsx(Gr,{}),className:"animate-pulse",children:p?"Envoi en cours...":s.contact.form.send})})]})})}),e.jsx(n,{item:!0,xs:12,md:6,className:"scroll-anim slide-in-right",children:e.jsxs(u,{sx:{height:"100%",display:"flex",flexDirection:"column",justifyContent:"center"},children:[e.jsx(c,{variant:"h5",gutterBottom:!0,fontWeight:"bold",color:"primary",children:s.contact.title}),e.jsx(c,{variant:"body1",paragraph:!0,children:"N'hésitez pas à nous contacter directement par téléphone ou email, ou en utilisant le formulaire ci-contre."}),e.jsxs(u,{sx:{my:3},children:[e.jsxs(c,{variant:"body1",sx:{mb:2},children:[e.jsxs("strong",{children:[s.contact.address,":"]})," 123 Rue des Volailles, Alger, Algérie"]}),e.jsxs(c,{variant:"body1",sx:{mb:2},children:[e.jsxs("strong",{children:[s.contact.phone,":"]})," +213 XX XX XX XX"]}),e.jsxs(c,{variant:"body1",sx:{mb:2},children:[e.jsxs("strong",{children:[s.contact.email,":"]})," <EMAIL>"]}),e.jsxs(c,{variant:"body1",children:[e.jsx("strong",{children:"Heures d'ouverture:"})," Dim-Jeu, 9h-17h"]})]})]})})]})]}),e.jsx(kt,{open:D.open,autoHideDuration:6e3,onClose:W,anchorOrigin:{vertical:"bottom",horizontal:"center"},children:e.jsx(te,{onClose:W,severity:D.severity,sx:{width:"100%"},children:D.message})})]}),e.jsx(u,{sx:{py:5,backgroundColor:t.palette.primary.dark,color:"white"},className:"scroll-anim fade-in",children:e.jsxs(ee,{children:[e.jsxs(n,{container:!0,spacing:4,children:[e.jsxs(n,{item:!0,xs:12,sm:6,md:3,className:"scroll-anim fade-in",children:[e.jsx(c,{variant:"h6",gutterBottom:!0,fontWeight:"bold",children:"Poultray DZ"}),e.jsx(c,{variant:"body2",paragraph:!0,children:"La plateforme de gestion d'élevage de volailles en Algérie"}),e.jsx(c,{variant:"body2",children:"Connecter les éleveurs, vétérinaires et acheteurs pour une filière avicole plus efficace."})]}),e.jsxs(n,{item:!0,xs:12,sm:6,md:3,className:"scroll-anim fade-in",sx:{animationDelay:"0.2s"},children:[e.jsx(c,{variant:"h6",gutterBottom:!0,fontWeight:"bold",children:"Liens Rapides"}),e.jsx(c,{variant:"body2",component:"div",children:e.jsxs(u,{component:"ul",sx:{pl:2,m:0,listStyleType:"none"},children:[e.jsx(u,{component:"li",sx:{mb:1.5},children:e.jsx(ce,{to:"/",style:{color:"white",textDecoration:"none",display:"flex",alignItems:"center","&:hover":{color:t.palette.secondary.light}},children:"Accueil"})}),e.jsx(u,{component:"li",sx:{mb:1.5},children:e.jsx(u,{component:"a",href:"#fonctionnalites",sx:{color:"white",textDecoration:"none",display:"flex",alignItems:"center","&:hover":{color:t.palette.secondary.light}},children:"Fonctionnalités"})}),e.jsx(u,{component:"li",sx:{mb:1.5},children:e.jsx(u,{component:"a",href:"#tarifs",sx:{color:"white",textDecoration:"none",display:"flex",alignItems:"center","&:hover":{color:t.palette.secondary.light}},children:"Tarifs"})}),e.jsx(u,{component:"li",sx:{mb:1.5},children:e.jsx(u,{component:"a",onClick:b,sx:{color:"white",textDecoration:"none",display:"flex",alignItems:"center",cursor:"pointer","&:hover":{color:t.palette.secondary.light}},children:"Contact"})})]})})]}),e.jsxs(n,{item:!0,xs:12,sm:6,md:3,className:"scroll-anim fade-in",sx:{animationDelay:"0.4s"},children:[e.jsx(c,{variant:"h6",gutterBottom:!0,fontWeight:"bold",children:"Légal"}),e.jsx(c,{variant:"body2",component:"div",children:e.jsxs(u,{component:"ul",sx:{pl:2,m:0,listStyleType:"none"},children:[e.jsx(u,{component:"li",sx:{mb:1.5},children:e.jsx(ce,{to:"/terms",style:{color:"white",textDecoration:"none","&:hover":{color:t.palette.secondary.light}},children:"Conditions d'utilisation"})}),e.jsx(u,{component:"li",sx:{mb:1.5},children:e.jsx(ce,{to:"/privacy",style:{color:"white",textDecoration:"none","&:hover":{color:t.palette.secondary.light}},children:"Politique de confidentialité"})}),e.jsx(u,{component:"li",sx:{mb:1.5},children:e.jsx(ce,{to:"/faq",style:{color:"white",textDecoration:"none","&:hover":{color:t.palette.secondary.light}},children:"FAQ"})})]})})]}),e.jsxs(n,{item:!0,xs:12,sm:6,md:3,className:"scroll-anim fade-in",sx:{animationDelay:"0.6s"},children:[e.jsx(c,{variant:"h6",gutterBottom:!0,fontWeight:"bold",children:"Suivez-nous"}),e.jsxs(u,{sx:{display:"flex",gap:2},children:[e.jsx(B,{sx:{color:"white",backgroundColor:"rgba(255,255,255,0.1)","&:hover":{backgroundColor:"#1877F2",transform:"translateY(-5px)"},transition:"all 0.3s ease"},"aria-label":"Facebook",component:"a",href:"https://facebook.com",target:"_blank",className:"animate-float",children:e.jsx(Zr,{})}),e.jsx(B,{sx:{color:"white",backgroundColor:"rgba(255,255,255,0.1)","&:hover":{backgroundColor:"#0A66C2",transform:"translateY(-5px)"},transition:"all 0.3s ease"},"aria-label":"LinkedIn",component:"a",href:"https://linkedin.com",target:"_blank",className:"animate-float",children:e.jsx(Yr,{})}),e.jsx(B,{sx:{color:"white",backgroundColor:"rgba(255,255,255,0.1)","&:hover":{backgroundColor:"#1DA1F2",transform:"translateY(-5px)"},transition:"all 0.3s ease"},"aria-label":"Twitter",component:"a",href:"https://twitter.com",target:"_blank",className:"animate-float",children:e.jsx(Qr,{})}),e.jsx(B,{sx:{color:"white",backgroundColor:"rgba(255,255,255,0.1)","&:hover":{backgroundColor:"#E4405F",transform:"translateY(-5px)"},transition:"all 0.3s ease"},"aria-label":"Instagram",component:"a",href:"https://instagram.com",target:"_blank",className:"animate-float",children:e.jsx(Jr,{})})]}),e.jsxs(u,{sx:{mt:3},children:[e.jsx(c,{variant:"body2",gutterBottom:!0,children:"Inscrivez-vous à notre newsletter"}),e.jsxs(u,{sx:{display:"flex",mt:1},children:[e.jsx(A,{size:"small",placeholder:"Votre email",variant:"outlined",sx:{backgroundColor:"rgba(255,255,255,0.1)",borderRadius:1,input:{color:"white"},"& .MuiOutlinedInput-root":{"& fieldset":{borderColor:"rgba(255,255,255,0.3)"},"&:hover fieldset":{borderColor:"rgba(255,255,255,0.5)"},"&.Mui-focused fieldset":{borderColor:t.palette.secondary.main}}}}),e.jsx(M,{variant:"contained",color:"secondary",sx:{ml:1},children:"OK"})]})]})]})]}),e.jsx(u,{sx:{mt:4,pt:2,borderTop:"1px solid rgba(255,255,255,0.1)",textAlign:"center"},children:e.jsxs(c,{variant:"body2",children:["© ",new Date().getFullYear()," Poultray DZ. Tous droits réservés."]})})]})})]})}const na=Es({palette:{primary:{light:"#6abf69",main:"#4caf50",dark:"#357a38",contrastText:"#fff"},secondary:{light:"#ffb74d",main:"#ff9800",dark:"#f57c00",contrastText:"#fff"},background:{default:"#f9f9f9",paper:"#ffffff"},text:{primary:"#333333",secondary:"#666666"}},typography:{fontFamily:'"Roboto", "Helvetica", "Arial", sans-serif',h1:{fontSize:"3rem",fontWeight:700,lineHeight:1.2,"@media (max-width:600px)":{fontSize:"2.5rem"}},h2:{fontSize:"2.5rem",fontWeight:600,lineHeight:1.3,"@media (max-width:600px)":{fontSize:"2rem"}},h3:{fontSize:"2rem",fontWeight:600,lineHeight:1.3,"@media (max-width:600px)":{fontSize:"1.75rem"}},h4:{fontSize:"1.5rem",fontWeight:600,lineHeight:1.4},h5:{fontSize:"1.25rem",fontWeight:500,lineHeight:1.4},h6:{fontSize:"1.125rem",fontWeight:500,lineHeight:1.5},body1:{fontSize:"1rem",lineHeight:1.6},body2:{fontSize:"0.875rem",lineHeight:1.6},button:{textTransform:"none",fontWeight:500}},shape:{borderRadius:8},shadows:["none","0px 2px 4px rgba(0, 0, 0, 0.05)","0px 4px 8px rgba(0, 0, 0, 0.08)","0px 6px 12px rgba(0, 0, 0, 0.1)","0px 8px 16px rgba(0, 0, 0, 0.12)","0px 10px 20px rgba(0, 0, 0, 0.14)","0px 12px 24px rgba(0, 0, 0, 0.16)","0px 14px 28px rgba(0, 0, 0, 0.18)","0px 16px 32px rgba(0, 0, 0, 0.2)","0px 18px 36px rgba(0, 0, 0, 0.22)","0px 20px 40px rgba(0, 0, 0, 0.24)","0px 22px 44px rgba(0, 0, 0, 0.26)","0px 24px 48px rgba(0, 0, 0, 0.28)","0px 26px 52px rgba(0, 0, 0, 0.3)","0px 28px 56px rgba(0, 0, 0, 0.32)","0px 30px 60px rgba(0, 0, 0, 0.34)","0px 32px 64px rgba(0, 0, 0, 0.36)","0px 34px 68px rgba(0, 0, 0, 0.38)","0px 36px 72px rgba(0, 0, 0, 0.4)","0px 38px 76px rgba(0, 0, 0, 0.42)","0px 40px 80px rgba(0, 0, 0, 0.44)","0px 42px 84px rgba(0, 0, 0, 0.46)","0px 44px 88px rgba(0, 0, 0, 0.48)","0px 46px 92px rgba(0, 0, 0, 0.5)","0px 48px 96px rgba(0, 0, 0, 0.52)"],components:{MuiButton:{styleOverrides:{root:{borderRadius:30,padding:"8px 24px",fontWeight:500,boxShadow:"0 4px 10px rgba(0, 0, 0, 0.15)",transition:"transform 0.2s, box-shadow 0.2s","&:hover":{transform:"translateY(-2px)",boxShadow:"0 6px 15px rgba(0, 0, 0, 0.2)"}},containedPrimary:{background:"linear-gradient(45deg, #4caf50 30%, #6abf69 90%)"},containedSecondary:{background:"linear-gradient(45deg, #ff9800 30%, #ffb74d 90%)"}}},MuiCard:{styleOverrides:{root:{borderRadius:12,boxShadow:"0 8px 16px rgba(0, 0, 0, 0.1)",overflow:"hidden"}}},MuiPaper:{styleOverrides:{root:{borderRadius:12}}},MuiAppBar:{styleOverrides:{root:{boxShadow:"0 2px 10px rgba(0, 0, 0, 0.1)"}}},MuiTextField:{styleOverrides:{root:{"& .MuiOutlinedInput-root":{borderRadius:8,"&:hover fieldset":{borderColor:"#4caf50"},"&.Mui-focused fieldset":{borderColor:"#4caf50"}}}}},MuiAccordion:{styleOverrides:{root:{borderRadius:8,"&:before":{display:"none"},"&.Mui-expanded":{margin:0,boxShadow:"0 4px 12px rgba(0, 0, 0, 0.08)"}}}},MuiAccordionSummary:{styleOverrides:{root:{borderRadius:"8px 8px 0 0","&.Mui-expanded":{minHeight:48,backgroundColor:"rgba(76, 175, 80, 0.05)"}},content:{"&.Mui-expanded":{margin:"12px 0"}}}}}});function aa(){return e.jsx(Ts,{theme:na,children:e.jsx(ra,{})})}function ia(){const[t,r]=i.useState([]),[l,x]=i.useState(!1),[s,d]=i.useState(null),[h,j]=i.useState({nom:"",prenom:"",email:"",telephone:"",adresse:""});i.useEffect(()=>{g()},[]);const g=async()=>{try{const f=await N.get("/eleveurs"),P=Array.isArray(f.data)?f.data:f.data.eleveurs||[];r(P)}catch(f){console.error("Erreur lors de la récupération des éleveurs:",f),r([])}},p=(f=null)=>{f?(d(f),j(f)):(d(null),j({nom:"",prenom:"",email:"",telephone:"",adresse:""})),x(!0)},a=()=>{x(!1),d(null),j({nom:"",prenom:"",email:"",telephone:"",adresse:""})},D=f=>{j({...h,[f.target.name]:f.target.value})},E=async f=>{f.preventDefault();try{s?await N.put(`/eleveurs/${s.id}`,h):await N.post("/eleveurs",h),a(),g()}catch(P){console.error("Erreur lors de l'enregistrement:",P)}},v=async f=>{if(window.confirm("Êtes-vous sûr de vouloir supprimer cet éleveur ?"))try{await N.delete(`/eleveurs/${f}`),g()}catch(P){console.error("Erreur lors de la suppression:",P)}};return e.jsxs(ee,{maxWidth:"lg",sx:{mt:4,mb:4},children:[e.jsxs(u,{sx:{display:"flex",justifyContent:"space-between",alignItems:"center",mb:4},children:[e.jsx(c,{variant:"h4",component:"h1",children:"Liste des Éleveurs"}),e.jsx(M,{variant:"contained",color:"primary",startIcon:e.jsx(tt,{}),onClick:()=>p(),children:"Ajouter un Éleveur"})]}),e.jsx(ze,{component:_,children:e.jsxs(Ne,{children:[e.jsx(_e,{children:e.jsxs(ie,{children:[e.jsx(S,{children:"Nom"}),e.jsx(S,{children:"Prénom"}),e.jsx(S,{children:"Email"}),e.jsx(S,{children:"Téléphone"}),e.jsx(S,{children:"Adresse"}),e.jsx(S,{children:"Actions"})]})}),e.jsx(Be,{children:Array.isArray(t)&&t.map(f=>e.jsxs(ie,{children:[e.jsx(S,{children:f.nom}),e.jsx(S,{children:f.prenom}),e.jsx(S,{children:f.email}),e.jsx(S,{children:f.telephone}),e.jsx(S,{children:f.adresse}),e.jsxs(S,{children:[e.jsx(B,{color:"primary",onClick:()=>p(f),size:"small",children:e.jsx(Oe,{})}),e.jsx(B,{color:"error",onClick:()=>v(f.id),size:"small",children:e.jsx(st,{})})]})]},f.id))})]})}),e.jsxs(Ce,{open:l,onClose:a,maxWidth:"sm",fullWidth:!0,children:[e.jsx(Se,{children:s?"Modifier l'éleveur":"Ajouter un éleveur"}),e.jsx(we,{children:e.jsxs(u,{component:"form",sx:{mt:2},onSubmit:E,children:[e.jsx(A,{fullWidth:!0,label:"Nom",name:"nom",value:h.nom,onChange:D,margin:"normal",required:!0}),e.jsx(A,{fullWidth:!0,label:"Prénom",name:"prenom",value:h.prenom,onChange:D,margin:"normal",required:!0}),e.jsx(A,{fullWidth:!0,label:"Email",name:"email",type:"email",value:h.email,onChange:D,margin:"normal",required:!0}),e.jsx(A,{fullWidth:!0,label:"Téléphone",name:"telephone",value:h.telephone,onChange:D,margin:"normal"}),e.jsx(A,{fullWidth:!0,label:"Adresse",name:"adresse",value:h.adresse,onChange:D,margin:"normal",multiline:!0,rows:3})]})}),e.jsxs(Ie,{children:[e.jsx(M,{onClick:a,children:"Annuler"}),e.jsx(M,{onClick:E,variant:"contained",color:"primary",children:s?"Modifier":"Ajouter"})]})]})]})}function Nt(){const[t,r]=i.useState([]),[l,x]=i.useState([]),[s,d]=i.useState(!1),[h,j]=i.useState(null),[g,p]=i.useState({eleveur_id:"",espece:"",race:"",age:"",poids:"",quantite:"",prix_unitaire:"",description:""});i.useEffect(()=>{a(),D()},[]);const a=async()=>{try{const b=await N.get("/volailles"),y=Array.isArray(b.data)?b.data:b.data.volailles||[];r(y)}catch(b){console.error("Erreur lors de la récupération des volailles:",b),r([])}},D=async()=>{try{const b=await N.get("/eleveurs"),y=Array.isArray(b.data)?b.data:b.data.eleveurs||[];x(y)}catch(b){console.error("Erreur lors de la récupération des éleveurs:",b),x([])}},E=(b=null)=>{b?(j(b),p({...b,eleveur_id:b.eleveur_id?.toString()||""})):(j(null),p({eleveur_id:"",espece:"",race:"",age:"",poids:"",quantite:"",prix_unitaire:"",description:""})),d(!0)},v=()=>{d(!1),j(null),p({eleveur_id:"",espece:"",race:"",age:"",poids:"",quantite:"",prix_unitaire:"",description:""})},f=b=>{p({...g,[b.target.name]:b.target.value})},P=async b=>{b.preventDefault();try{const y={...g,age:parseInt(g.age)||null,poids:parseFloat(g.poids)||null,quantite:parseInt(g.quantite)||0,prix_unitaire:parseFloat(g.prix_unitaire)||0};h?await N.put(`/api/volailles/${h.id}`,y):await N.post("/api/volailles",y),v(),a()}catch(y){console.error("Erreur lors de l'enregistrement:",y)}},W=async b=>{if(window.confirm("Êtes-vous sûr de vouloir supprimer cette volaille ?"))try{await N.delete(`/api/volailles/${b}`),a()}catch(y){console.error("Erreur lors de la suppression:",y)}};return e.jsxs(ee,{maxWidth:"lg",sx:{mt:4,mb:4},children:[e.jsxs(u,{sx:{display:"flex",justifyContent:"space-between",alignItems:"center",mb:4},children:[e.jsx(c,{variant:"h4",component:"h1",children:"Liste des Volailles"}),e.jsx(M,{variant:"contained",color:"primary",startIcon:e.jsx(tt,{}),onClick:()=>E(),children:"Ajouter une Volaille"})]}),e.jsx(ze,{component:_,children:e.jsxs(Ne,{children:[e.jsx(_e,{children:e.jsxs(ie,{children:[e.jsx(S,{children:"Éleveur"}),e.jsx(S,{children:"Espèce"}),e.jsx(S,{children:"Race"}),e.jsx(S,{children:"Âge"}),e.jsx(S,{children:"Poids (kg)"}),e.jsx(S,{children:"Quantité"}),e.jsx(S,{children:"Prix Unitaire (DA)"}),e.jsx(S,{children:"Actions"})]})}),e.jsx(Be,{children:Array.isArray(t)&&t.map(b=>e.jsxs(ie,{children:[e.jsx(S,{children:`${b.eleveur_nom||""} ${b.eleveur_prenom||""}`}),e.jsx(S,{children:b.espece}),e.jsx(S,{children:b.race}),e.jsx(S,{children:b.age?`${b.age} jours`:"-"}),e.jsx(S,{children:b.poids||"-"}),e.jsx(S,{children:b.quantite}),e.jsx(S,{children:b.prix_unitaire}),e.jsxs(S,{children:[e.jsx(B,{color:"primary",onClick:()=>E(b),size:"small",children:e.jsx(Oe,{})}),e.jsx(B,{color:"error",onClick:()=>W(b.id),size:"small",children:e.jsx(st,{})})]})]},b.id))})]})}),e.jsxs(Ce,{open:s,onClose:v,maxWidth:"md",fullWidth:!0,children:[e.jsx(Se,{children:h?"Modifier la volaille":"Ajouter une volaille"}),e.jsx(we,{children:e.jsx(u,{component:"form",sx:{mt:2},onSubmit:P,children:e.jsxs(n,{container:!0,spacing:2,children:[e.jsx(n,{item:!0,xs:12,md:6,children:e.jsx(A,{fullWidth:!0,select:!0,label:"Éleveur",name:"eleveur_id",value:g.eleveur_id,onChange:f,margin:"normal",required:!0,children:Array.isArray(l)&&l.map(b=>e.jsx(I,{value:b.id.toString(),children:`${b.nom} ${b.prenom}`},b.id))})}),e.jsx(n,{item:!0,xs:12,md:6,children:e.jsx(A,{fullWidth:!0,label:"Espèce",name:"espece",value:g.espece,onChange:f,margin:"normal",required:!0})}),e.jsx(n,{item:!0,xs:12,md:6,children:e.jsx(A,{fullWidth:!0,label:"Race",name:"race",value:g.race,onChange:f,margin:"normal"})}),e.jsx(n,{item:!0,xs:12,md:6,children:e.jsx(A,{fullWidth:!0,label:"Âge (jours)",name:"age",type:"number",value:g.age,onChange:f,margin:"normal"})}),e.jsx(n,{item:!0,xs:12,md:6,children:e.jsx(A,{fullWidth:!0,label:"Poids (kg)",name:"poids",type:"number",value:g.poids,onChange:f,margin:"normal",inputProps:{step:"0.1"}})}),e.jsx(n,{item:!0,xs:12,md:6,children:e.jsx(A,{fullWidth:!0,label:"Quantité",name:"quantite",type:"number",value:g.quantite,onChange:f,margin:"normal",required:!0})}),e.jsx(n,{item:!0,xs:12,md:6,children:e.jsx(A,{fullWidth:!0,label:"Prix Unitaire (DA)",name:"prix_unitaire",type:"number",value:g.prix_unitaire,onChange:f,margin:"normal",required:!0,inputProps:{step:"0.01"}})}),e.jsx(n,{item:!0,xs:12,children:e.jsx(A,{fullWidth:!0,label:"Description",name:"description",value:g.description,onChange:f,margin:"normal",multiline:!0,rows:3})})]})})}),e.jsxs(Ie,{children:[e.jsx(M,{onClick:v,children:"Annuler"}),e.jsx(M,{onClick:P,variant:"contained",color:"primary",children:h?"Modifier":"Ajouter"})]})]})]})}const hs=({data:t,title:r,color:l})=>{const x=Math.max(...t.map(h=>h.value)),[s,d]=i.useState(!1);return i.useEffect(()=>{const h=setTimeout(()=>d(!0),100);return()=>clearTimeout(h)},[]),e.jsxs(Z,{sx:{height:"100%",boxShadow:3,transition:"all 0.3s ease"},children:[e.jsx(ve,{title:r,titleTypographyProps:{variant:"h6",fontWeight:"bold"},sx:{backgroundColor:"rgba(0, 0, 0, 0.03)",pb:1}}),e.jsx(ue,{}),e.jsx(Y,{children:t.map((h,j)=>e.jsxs(u,{sx:{mb:2},children:[e.jsxs(u,{sx:{display:"flex",justifyContent:"space-between",mb:.5},children:[e.jsx(c,{variant:"body2",fontWeight:"medium",children:h.label}),e.jsx(c,{variant:"body2",fontWeight:"bold",children:h.value})]}),e.jsx(u,{sx:{height:12,width:"100%",bgcolor:"grey.200",borderRadius:6,overflow:"hidden"},children:e.jsx(u,{sx:{height:"100%",width:s?`${h.value/x*100}%`:"0%",bgcolor:l,borderRadius:6,transition:"width 1s ease-out",display:"flex",alignItems:"center",justifyContent:"flex-end"}})})]},j))})]})},_t=({title:t,value:r,icon:l,color:x})=>{const[s,d]=i.useState(!1);return i.useEffect(()=>{const h=setTimeout(()=>d(!0),300);return()=>clearTimeout(h)},[]),e.jsx(Z,{sx:{height:"100%",boxShadow:3,transform:s?"translateY(0)":"translateY(20px)",opacity:s?1:0,transition:"all 0.5s ease","&:hover":{boxShadow:6,transform:"translateY(-5px)"}},children:e.jsx(Y,{sx:{p:3},children:e.jsxs(n,{container:!0,spacing:2,alignItems:"center",children:[e.jsx(n,{item:!0,children:e.jsx(re,{sx:{bgcolor:x,width:56,height:56,boxShadow:`0 4px 14px 0 ${x}40`},children:l})}),e.jsxs(n,{item:!0,xs:!0,children:[e.jsx(c,{variant:"subtitle1",component:"div",color:"text.secondary",gutterBottom:!0,children:t}),e.jsx(c,{variant:"h4",component:"div",fontWeight:"bold",children:r})]})]})})})},oa=({activities:t})=>e.jsxs(Z,{sx:{height:"100%"},children:[e.jsx(ve,{title:"Activités Récentes"}),e.jsx(ue,{}),e.jsx(Y,{sx:{p:0},children:e.jsx(le,{children:t.map((r,l)=>e.jsxs(qe.Fragment,{children:[e.jsxs(oe,{alignItems:"flex-start",children:[e.jsx(je,{children:e.jsx(re,{sx:{bgcolor:r.color},children:r.icon})}),e.jsx(ae,{primary:r.title,secondary:e.jsxs(e.Fragment,{children:[e.jsx(c,{variant:"body2",color:"text.primary",component:"span",children:r.description}),e.jsx(c,{variant:"caption",display:"block",color:"text.secondary",children:r.time})]})})]}),l<t.length-1&&e.jsx(ue,{variant:"inset",component:"li"})]},l))})})]});function la(){const[t,r]=i.useState({totalEleveurs:0,totalVolailles:0,totalValeur:0}),[l,x]=i.useState(0),[s,d]=i.useState([]),[h,j]=i.useState([]),[g,p]=i.useState(""),[a,D]=i.useState(""),[E,v]=i.useState(""),[f,P]=i.useState(""),[W,b]=i.useState(!1),y=[{label:"Poulets",value:120},{label:"Dindes",value:80},{label:"Canards",value:40},{label:"Cailles",value:25}],C=[{label:"Alger",value:45},{label:"Oran",value:35},{label:"Constantine",value:30},{label:"Annaba",value:20},{label:"Autres",value:50}],m=[{title:"Nouvel éleveur",description:"Ahmed Benali a rejoint la plateforme",time:"Il y a 2 heures",icon:e.jsx(be,{}),color:"primary.main"},{title:"Nouveau lot de volailles",description:"200 poulets ajoutés par Karim Hadj",time:"Il y a 5 heures",icon:e.jsx(ye,{}),color:"secondary.main"},{title:"Vente réalisée",description:"Lot de 50 dindes vendu",time:"Hier, 15:30",icon:e.jsx(De,{}),color:"success.main"}];i.useEffect(()=>{(async()=>{try{const[q,X]=await Promise.all([N.get("/eleveurs"),N.get("/volailles")]);j(q.data),d(X.data);const xe=X.data.reduce(($e,Pe)=>$e+Pe.prix_unitaire*Pe.quantite,0);r({totalEleveurs:q.data.length,totalVolailles:X.data.length,totalValeur:xe})}catch(q){console.error("Erreur lors de la récupération des données:",q)}})()},[]);const o=(T,q)=>{x(q)},k=T=>{p(T.target.value)},V=T=>{D(T.target.value)},U=T=>{P(T.target.value)},$=(T,q)=>{b(!0);try{let X=[],xe="";q==="volailles.csv"?(X=["ID","Espèce","Race","Éleveur","Quantité","Prix Unitaire","Valeur Totale"],xe=X.join(",")+`
`,T.forEach(w=>{const me=[w.id,w.espece,w.race,`${w.eleveur_nom||""} ${w.eleveur_prenom||""}`,w.quantite,w.prix_unitaire,w.prix_unitaire*w.quantite];xe+=me.join(",")+`
`})):q==="eleveurs.csv"&&(X=["ID","Nom","Prénom","Email","Téléphone","Nombre de Lots"],xe=X.join(",")+`
`,T.forEach(w=>{const me=s.filter(Tt=>Tt.eleveur_id===w.id).length,nt=[w.id,w.nom,w.prenom,w.email,w.telephone,me];xe+=nt.join(",")+`
`}));const $e=new Blob([xe],{type:"text/csv;charset=utf-8;"}),Pe=URL.createObjectURL($e),R=document.createElement("a");R.setAttribute("href",Pe),R.setAttribute("download",q),R.style.visibility="hidden",document.body.appendChild(R),R.click(),document.body.removeChild(R)}catch(X){console.error("Erreur lors de l'exportation:",X)}finally{b(!1)}},G=s.filter(T=>{const q=g?T.espece===g:!0,X=a?T.region===a:!0,xe=f?T.espece?.toLowerCase().includes(f.toLowerCase())||T.race?.toLowerCase().includes(f.toLowerCase())||T.eleveur_nom?.toLowerCase().includes(f.toLowerCase())||T.eleveur_prenom?.toLowerCase().includes(f.toLowerCase()):!0;return q&&X&&xe}),z=h.filter(T=>f?T.nom?.toLowerCase().includes(f.toLowerCase())||T.prenom?.toLowerCase().includes(f.toLowerCase())||T.email?.toLowerCase().includes(f.toLowerCase()):!0),O=[...new Set(s.map(T=>T.espece))],L=[...new Set(s.map(T=>T.region).filter(Boolean))];return e.jsxs(ee,{maxWidth:"lg",sx:{mt:4,mb:4},children:[e.jsxs(u,{sx:{mb:4},children:[e.jsx(c,{variant:"h4",component:"h1",gutterBottom:!0,children:"Tableau de Bord Administratif"}),e.jsx(c,{variant:"subtitle1",color:"textSecondary",children:"Aperçu des statistiques et des activités de la plateforme"})]}),e.jsxs(n,{container:!0,spacing:3,sx:{mb:4},children:[e.jsx(n,{item:!0,xs:12,sm:6,md:4,children:e.jsx(_t,{title:"Éleveurs",value:t.totalEleveurs,icon:e.jsx(be,{}),color:"primary.main"})}),e.jsx(n,{item:!0,xs:12,sm:6,md:4,children:e.jsx(_t,{title:"Lots de Volailles",value:t.totalVolailles,icon:e.jsx(ye,{}),color:"secondary.main"})}),e.jsx(n,{item:!0,xs:12,sm:6,md:4,children:e.jsx(_t,{title:"Valeur Totale",value:`${t.totalValeur.toLocaleString()} DA`,icon:e.jsx(De,{}),color:"success.main"})})]}),e.jsxs(n,{container:!0,spacing:3,sx:{mb:4},children:[e.jsx(n,{item:!0,xs:12,md:8,children:e.jsxs(n,{container:!0,spacing:3,children:[e.jsx(n,{item:!0,xs:12,sm:6,children:e.jsx(hs,{data:y,title:"Répartition par Espèce",color:"primary.main"})}),e.jsx(n,{item:!0,xs:12,sm:6,children:e.jsx(hs,{data:C,title:"Répartition par Région",color:"secondary.main"})})]})}),e.jsx(n,{item:!0,xs:12,md:4,children:e.jsx(oa,{activities:m})})]}),e.jsxs(_,{sx:{mb:4},children:[e.jsxs(xt,{value:l,onChange:o,indicatorColor:"primary",textColor:"primary",variant:"fullWidth",children:[e.jsx(ge,{label:"Volailles",icon:e.jsx(ye,{})}),e.jsx(ge,{label:"Éleveurs",icon:e.jsx(be,{})})]}),l===0&&e.jsxs(u,{sx:{p:3},children:[e.jsxs(u,{sx:{mb:3},children:[e.jsxs(u,{sx:{display:"flex",justifyContent:"space-between",alignItems:"center",mb:2},children:[e.jsx(c,{variant:"h6",children:"Liste des Volailles"}),e.jsx(M,{variant:"outlined",color:"primary",onClick:()=>$(G,"volailles.csv"),disabled:W,startIcon:e.jsx(Fe,{}),children:"Exporter en CSV"})]}),e.jsxs(n,{container:!0,spacing:2,alignItems:"center",children:[e.jsx(n,{item:!0,xs:12,sm:6,md:3,children:e.jsxs(Q,{fullWidth:!0,size:"small",children:[e.jsx(J,{id:"filtre-espece-label",children:"Filtrer par espèce"}),e.jsxs(K,{labelId:"filtre-espece-label",value:g,label:"Filtrer par espèce",onChange:k,children:[e.jsx(I,{value:"",children:"Toutes les espèces"}),O.map((T,q)=>e.jsx(I,{value:T,children:T},q))]})]})}),e.jsx(n,{item:!0,xs:12,sm:6,md:3,children:e.jsxs(Q,{fullWidth:!0,size:"small",children:[e.jsx(J,{id:"filtre-region-label",children:"Filtrer par région"}),e.jsxs(K,{labelId:"filtre-region-label",value:a,label:"Filtrer par région",onChange:V,children:[e.jsx(I,{value:"",children:"Toutes les régions"}),L.map((T,q)=>e.jsx(I,{value:T,children:T},q))]})]})}),e.jsx(n,{item:!0,xs:12,sm:6,md:6,children:e.jsx(A,{fullWidth:!0,size:"small",label:"Rechercher",variant:"outlined",value:f,onChange:U,placeholder:"Rechercher par espèce, race ou éleveur...",InputProps:{startAdornment:e.jsx(u,{sx:{color:"action.active",mr:1,my:.5},children:e.jsx(Le,{})})}})})]})]}),e.jsx(ze,{component:_,sx:{boxShadow:3,borderRadius:2,overflow:"hidden"},children:e.jsxs(Ne,{sx:{minWidth:650},children:[e.jsx(_e,{sx:{bgcolor:"primary.main"},children:e.jsxs(ie,{children:[e.jsx(S,{sx:{color:"white",fontWeight:"bold"},children:"Espèce"}),e.jsx(S,{sx:{color:"white",fontWeight:"bold"},children:"Race"}),e.jsx(S,{sx:{color:"white",fontWeight:"bold"},children:"Éleveur"}),e.jsx(S,{align:"right",sx:{color:"white",fontWeight:"bold"},children:"Quantité"}),e.jsx(S,{align:"right",sx:{color:"white",fontWeight:"bold"},children:"Prix Unitaire"}),e.jsx(S,{align:"right",sx:{color:"white",fontWeight:"bold"},children:"Valeur Totale"})]})}),e.jsx(Be,{children:G.slice(0,5).map((T,q)=>e.jsxs(ie,{sx:{"&:nth-of-type(odd)":{bgcolor:"rgba(0, 0, 0, 0.03)"},"&:hover":{bgcolor:"rgba(0, 0, 0, 0.07)"},animation:"fadeIn 0.5s ease-in-out",animationDelay:`${q*.1}s`,"@keyframes fadeIn":{"0%":{opacity:0,transform:"translateY(10px)"},"100%":{opacity:1,transform:"translateY(0)"}}},children:[e.jsx(S,{children:T.espece}),e.jsx(S,{children:T.race}),e.jsx(S,{children:`${T.eleveur_nom||""} ${T.eleveur_prenom||""}`}),e.jsx(S,{align:"right",children:T.quantite}),e.jsx(S,{align:"right",children:`${T.prix_unitaire} DA`}),e.jsx(S,{align:"right",children:`${(T.prix_unitaire*T.quantite).toLocaleString()} DA`})]},T.id))})]})}),G.length>5&&e.jsx(u,{sx:{mt:2,display:"flex",justifyContent:"center"},children:e.jsx(M,{variant:"outlined",children:"Voir plus"})})]}),l===1&&e.jsxs(u,{sx:{p:3},children:[e.jsxs(u,{sx:{mb:3},children:[e.jsxs(u,{sx:{display:"flex",justifyContent:"space-between",alignItems:"center",mb:2},children:[e.jsx(c,{variant:"h6",children:"Liste des Éleveurs"}),e.jsx(M,{variant:"outlined",color:"primary",onClick:()=>$(z,"eleveurs.csv"),disabled:W,startIcon:e.jsx(Fe,{}),children:"Exporter en CSV"})]}),e.jsx(n,{container:!0,spacing:2,alignItems:"center",children:e.jsx(n,{item:!0,xs:12,sm:6,children:e.jsx(A,{fullWidth:!0,size:"small",label:"Rechercher",variant:"outlined",value:f,onChange:U,placeholder:"Rechercher par nom, prénom ou email...",InputProps:{startAdornment:e.jsx(u,{sx:{color:"action.active",mr:1,my:.5},children:e.jsx(Le,{})})}})})})]}),e.jsx(ze,{children:e.jsxs(Ne,{children:[e.jsx(_e,{children:e.jsxs(ie,{children:[e.jsx(S,{children:"Nom"}),e.jsx(S,{children:"Prénom"}),e.jsx(S,{children:"Email"}),e.jsx(S,{children:"Téléphone"}),e.jsx(S,{align:"right",children:"Nombre de Lots"})]})}),e.jsx(Be,{children:z.slice(0,5).map(T=>{const q=s.filter(X=>X.eleveur_id===T.id).length;return e.jsxs(ie,{children:[e.jsx(S,{children:T.nom}),e.jsx(S,{children:T.prenom}),e.jsx(S,{children:T.email}),e.jsx(S,{children:T.telephone}),e.jsx(S,{align:"right",children:q})]},T.id)})})]})}),z.length>5&&e.jsx(u,{sx:{mt:2,display:"flex",justifyContent:"center"},children:e.jsx(M,{variant:"outlined",children:"Voir plus"})})]})]})]})}const ca=()=>{const[t,r]=i.useState({email:"",password:""}),[l,x]=i.useState(!1),[s,d]=i.useState(""),[h,j]=i.useState(!1),{login:g,error:p,setError:a}=fe(),D=ke(),E=W=>({admin:"/admin/dashboard",eleveur:"/eleveur/dashboard",veterinaire:"/veterinaire/dashboard",marchand:"/marchand/dashboard"})[W]||"/dashboard",v=W=>{const{name:b,value:y}=W.target;r(C=>({...C,[b]:y}))},f=async W=>{W.preventDefault(),d(""),j(!0);try{console.log("Début de la soumission du formulaire de connexion");const{email:b,password:y}=t;if(console.log("Données du formulaire:",{email:b,password:y}),!b||!y){console.log("Champs manquants dans le formulaire"),d("Veuillez remplir tous les champs"),j(!1);return}console.log("Appel de la fonction login avec:",{email:b,password:y});const C=await g(b,y);if(console.log("Réponse de login reçue:",C),!C||!C.role){console.error("❌ Données utilisateur invalides après connexion:",C),d("Erreur: données utilisateur incomplètes");return}const m=typeof C.role=="object"&&C.role!==null?C.role.name:C.role;console.log("🎯 Attente de mise à jour du state..."),await new Promise(k=>setTimeout(k,100)),console.log("🚀 Redirection basée sur le rôle:",C.role,"-> rôle extrait:",m);const o=E(m);console.log("📍 Navigation vers:",o),D(o,{replace:!0})}catch(b){console.error("Erreur de connexion:",b),console.error("Détails de l'erreur:",{status:b.response?.status,statusText:b.response?.statusText,data:b.response?.data,message:b.message}),d(b.response?.data?.message||"Erreur de connexion")}finally{j(!1)}},P=()=>{x(!l)};return e.jsx(ee,{maxWidth:"sm",children:e.jsx(_,{elevation:3,sx:{p:4,mt:8},children:e.jsxs(u,{sx:{display:"flex",flexDirection:"column",alignItems:"center"},children:[e.jsx(c,{component:"h1",variant:"h5",sx:{mb:3},children:"Connexion"}),(p||s)&&e.jsx(te,{severity:"error",sx:{width:"100%",mb:2},children:s||p}),e.jsxs(u,{component:"form",onSubmit:f,sx:{width:"100%"},children:[e.jsx(A,{margin:"normal",required:!0,fullWidth:!0,id:"email",label:"Adresse email",name:"email",autoComplete:"email",autoFocus:!0,value:t.email,onChange:v,error:!!s}),e.jsx(A,{margin:"normal",required:!0,fullWidth:!0,name:"password",label:"Mot de passe",type:l?"text":"password",id:"password",autoComplete:"current-password",value:t.password,onChange:v,error:!!s,InputProps:{endAdornment:e.jsx(Yt,{position:"end",children:e.jsx(B,{"aria-label":"toggle password visibility",onClick:P,edge:"end",children:l?e.jsx(Qt,{}):e.jsx(rt,{})})})}}),e.jsx(M,{type:"submit",fullWidth:!0,variant:"contained",color:"primary",sx:{mt:3,mb:2},disabled:h,startIcon:e.jsx(Is,{}),children:h?"Connexion en cours...":"Se connecter"}),e.jsxs(u,{sx:{mt:2,textAlign:"center"},children:[e.jsxs(c,{variant:"body2",children:["Vous n'avez pas de compte ?"," ",e.jsx(ce,{to:"/register",style:{color:"inherit"},children:"Inscrivez-vous"})]}),!1]})]})]})})})},da=()=>{const[t,r]=i.useState({username:"",email:"",password:"",confirmPassword:"",first_name:"",last_name:"",role:"eleveur"}),[l,x]=i.useState(!1),[s,d]=i.useState(""),[h,j]=i.useState(!1),[g,p]=i.useState(""),{register:a,login:D}=fe(),E=ke(),v=b=>{const{name:y,value:C}=b.target;r(m=>({...m,[y]:C}))},f=()=>!t.username||!t.email||!t.password||!t.confirmPassword||!t.first_name||!t.last_name?(d("Veuillez remplir tous les champs obligatoires"),!1):t.password!==t.confirmPassword?(d("Les mots de passe ne correspondent pas"),!1):t.password.length<6?(d("Le mot de passe doit contenir au moins 6 caractères"),!1):/^[^\s@]+@[^\s@]+\.[^\s@]+$/.test(t.email)?!0:(d("Veuillez entrer une adresse email valide"),!1),P=async b=>{if(b.preventDefault(),d(""),p(""),!!f()){j(!0);try{const{confirmPassword:y,...C}=t;await a(C),p("Inscription réussie! Vous pouvez maintenant vous connecter."),setTimeout(async()=>{try{const m=await D(t.email,t.password),o=typeof m.role=="object"&&m.role!==null?m.role.name:m.role;E(o==="admin"?"/admin/dashboard":o==="eleveur"?"/eleveur/dashboard":o==="veterinaire"?"/veterinaire/dashboard":o==="marchand"?"/marchand/dashboard":"/dashboard")}catch(m){console.error("Auto login error:",m),E("/login")}},1500)}catch(y){console.error("Registration error:",y),d(y.response?.data?.message||"Erreur lors de l'inscription")}finally{j(!1)}}},W=()=>{x(!l)};return e.jsx(ee,{maxWidth:"md",children:e.jsx(_,{elevation:3,sx:{p:4,mt:8},children:e.jsxs(u,{sx:{display:"flex",flexDirection:"column",alignItems:"center"},children:[e.jsx(c,{component:"h1",variant:"h5",sx:{mb:3},children:"Créer un compte"}),s&&e.jsx(te,{severity:"error",sx:{width:"100%",mb:2},children:s}),g&&e.jsx(te,{severity:"success",sx:{width:"100%",mb:2},children:g}),e.jsxs(u,{component:"form",onSubmit:P,sx:{width:"100%"},children:[e.jsxs(n,{container:!0,spacing:2,children:[e.jsx(n,{item:!0,xs:12,sm:6,children:e.jsx(A,{required:!0,fullWidth:!0,id:"first_name",label:"Prénom",name:"first_name",autoComplete:"given-name",value:t.first_name,onChange:v,error:!!s})}),e.jsx(n,{item:!0,xs:12,sm:6,children:e.jsx(A,{required:!0,fullWidth:!0,id:"last_name",label:"Nom",name:"last_name",autoComplete:"family-name",value:t.last_name,onChange:v,error:!!s})}),e.jsx(n,{item:!0,xs:12,children:e.jsx(A,{required:!0,fullWidth:!0,id:"username",label:"Nom d'utilisateur",name:"username",autoComplete:"username",value:t.username,onChange:v,error:!!s})}),e.jsx(n,{item:!0,xs:12,children:e.jsx(A,{required:!0,fullWidth:!0,id:"email",label:"Adresse email",name:"email",autoComplete:"email",value:t.email,onChange:v,error:!!s})}),e.jsx(n,{item:!0,xs:12,sm:6,children:e.jsx(A,{required:!0,fullWidth:!0,name:"password",label:"Mot de passe",type:l?"text":"password",id:"password",autoComplete:"new-password",value:t.password,onChange:v,error:!!s,InputProps:{endAdornment:e.jsx(Yt,{position:"end",children:e.jsx(B,{"aria-label":"toggle password visibility",onClick:W,edge:"end",children:l?e.jsx(Qt,{}):e.jsx(rt,{})})})}})}),e.jsx(n,{item:!0,xs:12,sm:6,children:e.jsx(A,{required:!0,fullWidth:!0,name:"confirmPassword",label:"Confirmer le mot de passe",type:l?"text":"password",id:"confirmPassword",value:t.confirmPassword,onChange:v,error:!!s})}),e.jsx(n,{item:!0,xs:12,children:e.jsxs(Q,{fullWidth:!0,children:[e.jsx(J,{id:"role-label",children:"Rôle"}),e.jsxs(K,{labelId:"role-label",id:"role",name:"role",value:t.role,label:"Rôle",onChange:v,children:[e.jsx(I,{value:"eleveur",children:"Éleveur"}),e.jsx(I,{value:"veterinaire",children:"Vétérinaire"}),e.jsx(I,{value:"marchand",children:"Marchand"})]})]})})]}),e.jsx(M,{type:"submit",fullWidth:!0,variant:"contained",color:"primary",sx:{mt:3,mb:2},disabled:h,startIcon:e.jsx(Ms,{}),children:h?"Inscription en cours...":"S'inscrire"}),e.jsx(u,{sx:{mt:2,textAlign:"center"},children:e.jsxs(c,{variant:"body2",children:["Vous avez déjà un compte ?"," ",e.jsx(ce,{to:"/login",style:{color:"inherit"},children:"Connectez-vous"})]})})]})]})})})},Ae={menuItem:{"&:hover":{backgroundColor:"rgba(0, 0, 0, 0.04)",borderRadius:"8px",margin:"0 8px"},"&.Mui-selected":{backgroundColor:"primary.light",borderRadius:"8px",margin:"0 8px","&:hover":{backgroundColor:"primary.light"}}},iconContainer:{borderRadius:"8px",padding:"8px",display:"flex",justifyContent:"center",alignItems:"center",transition:"all 0.2s ease-in-out","&:hover":{transform:"scale(1.1)"}},listItemButton:{borderRadius:"8px",margin:"4px 8px",transition:"all 0.2s ease","&.Mui-selected":{backgroundColor:"rgba(0, 0, 0, 0.08)","&:before":{content:'""',position:"absolute",left:0,top:"20%",height:"60%",width:"4px",backgroundColor:"primary.main",borderRadius:"0 4px 4px 0"}}}},ne={dashboard:"#4caf50",profile:"#2196f3",homepage:"#ff9800",users:"#9c27b0",volailles:"#e91e63",statistics:"#00bcd4",blog:"#607d8b",ai:"#673ab7",translations:"#3f51b5",notifications:"#f44336",prescriptions:"#8bc34a",consultations:"#cddc39",historique:"#795548",produits:"#ff5722",commandes:"#ffc107",ventes:"#009688",settings:"#757575"},ua=260,ha=(t,r)=>{const l=[{title:"Tableau de bord",icon:e.jsx(Zt,{}),path:`/${t}/dashboard`,color:ne.dashboard},{title:"Profil",icon:e.jsx(be,{}),path:`/${t}/profile`,color:ne.profile}],x={admin:[{title:"Page d'accueil",icon:e.jsx(ct,{}),path:"/admin/homepage",color:ne.homepage,sx:Ae.menuItem},{title:"Gestion des utilisateurs",icon:e.jsx(be,{}),color:ne.users,items:[{title:"Tous les utilisateurs",path:"/admin/users"},{title:"Éleveurs",path:"/admin/users/eleveurs"},{title:"Vétérinaires",path:"/admin/users/veterinaires"},{title:"Marchands",path:"/admin/users/marchands"},{title:"Connexion en tant que...",path:"/admin/users/login-as"}],sx:Ae.menuItem},{title:"Volailles",icon:e.jsx(ye,{}),path:"/admin/volailles",color:ne.volailles},{title:"Statistiques",icon:e.jsx(lt,{}),path:"/admin/statistics",color:ne.statistics},{title:"Blog",icon:e.jsx(ct,{}),path:"/admin/blog",color:ne.blog},{title:"Intelligence Artificielle",icon:e.jsx(Jt,{}),color:ne.ai,items:[{title:"Générateur de blog",path:"/admin/ai/blog-generator"},{title:"Analyse de données",path:"/admin/ai/data-analysis"},{title:"Contenu de page",path:"/admin/ai/page-content"},{title:"Configuration API",path:"/admin/ai/api-config"}]},{title:"Traductions",icon:e.jsx(Ke,{}),path:"/admin/translations",color:ne.translations},{title:"Rôles et Plans",icon:e.jsx(qt,{}),path:"/admin/roles-plans",color:"#ff5722"},{title:"Notifications",icon:e.jsx(Le,{}),path:"/admin/notifications",color:ne.notifications}],eleveur:[{title:"Mes volailles",icon:e.jsx(ye,{}),path:"/eleveur/volailles",color:ne.volailles},{title:"Mes ventes",icon:e.jsx(ot,{}),path:"/eleveur/ventes",color:ne.ventes},{title:"Statistiques",icon:e.jsx(lt,{}),path:"/eleveur/statistics",color:ne.statistics}],veterinaire:[{title:"Prescriptions",icon:e.jsx(Ze,{}),path:"/veterinaire/prescriptions",color:ne.prescriptions},{title:"Consultations",icon:e.jsx(Ze,{}),path:"/veterinaire/consultations",color:ne.consultations},{title:"Historique",icon:e.jsx(ct,{}),path:"/veterinaire/historique",color:ne.historique}],marchand:[{title:"Produits",icon:e.jsx(ot,{}),path:"/marchand/produits",color:ne.produits},{title:"Commandes",icon:e.jsx(ot,{}),path:"/marchand/commandes",color:ne.commandes},{title:"Ventes",icon:e.jsx(lt,{}),path:"/marchand/ventes",color:ne.ventes}]},s={title:"Paramètres",icon:e.jsx(qt,{}),color:ne.settings,items:[{title:"Général",path:"/admin/settings/general"},{title:"Configuration SMTP",path:"/admin/settings/smtp"},{title:"Sécurité",path:"/admin/settings/security"}]};return[...l,...x[t],s]},xa=({open:t,toggleDrawer:r,handleLoginAsUser:l})=>{const{user:x,logout:s}=fe(),d=ke(),h=Ot(),[j,g]=i.useState({}),a=x?.role?typeof x.role=="object"&&x.role!==null?x.role.name:x.role:"guest",D=ha(a),E=()=>{s(),d("/login")},v=y=>{d(y),window.innerWidth<600&&r(!1)},f=y=>{g(C=>({...C,[y]:!C[y]}))},P=y=>h.pathname===y,W=y=>y.map(C=>C.items?e.jsxs(qe.Fragment,{children:[e.jsx(oe,{disablePadding:!0,children:e.jsxs(mt,{onClick:()=>f(C.title),sx:Ae.listItemButton,children:[e.jsx(Mt,{children:e.jsx(u,{sx:{...Ae.iconContainer,bgcolor:C.color?`${C.color}15`:"transparent"},children:qe.cloneElement(C.icon,{sx:{color:C.color}})})}),e.jsx(ae,{primary:C.title}),j[C.title]?e.jsx(en,{}):e.jsx(tn,{})]})}),e.jsx(sn,{in:j[C.title],timeout:"auto",unmountOnExit:!0,children:e.jsx(le,{component:"div",disablePadding:!0,children:C.items.map(m=>e.jsx(mt,{sx:{pl:4,...Ae.listItemButton},onClick:()=>m.action?m.action():v(m.path),selected:m.path?P(m.path):!1,children:e.jsx(ae,{primary:m.title})},m.title))})})]},C.title):e.jsx(oe,{disablePadding:!0,children:e.jsxs(mt,{onClick:()=>C.action?C.action():v(C.path),selected:C.path?P(C.path):!1,sx:Ae.listItemButton,children:[e.jsx(Mt,{children:e.jsx(u,{sx:{...Ae.iconContainer,bgcolor:C.color?`${C.color}15`:"transparent"},children:qe.cloneElement(C.icon,{sx:{color:C.color}})})}),e.jsx(ae,{primary:C.title})]})},C.title)),b=e.jsxs(e.Fragment,{children:[e.jsxs(u,{sx:{display:"flex",alignItems:"center",justifyContent:"space-between",p:2},children:[e.jsx(c,{variant:"h6",component:ce,to:"/",sx:{textDecoration:"none",color:"inherit"},children:"Poultray DZ"}),e.jsx(B,{onClick:()=>r(!1),children:e.jsx(Kr,{})})]}),e.jsx(ue,{}),x&&e.jsxs(u,{sx:{p:2,display:"flex",alignItems:"center",gap:2},children:[e.jsx(re,{sx:{bgcolor:"primary.main"},children:x.first_name?x.first_name[0]:x.username?x.username[0]:"U"}),e.jsxs(u,{children:[e.jsx(c,{variant:"subtitle1",noWrap:!0,children:x.first_name&&x.last_name?`${x.first_name} ${x.last_name}`:x.username}),e.jsx(c,{variant:"body2",color:"text.secondary",noWrap:!0,children:a.charAt(0).toUpperCase()+a.slice(1)})]})]}),e.jsx(ue,{}),e.jsx(le,{sx:{flexGrow:1},children:W(D)}),e.jsx(ue,{}),e.jsx(le,{children:e.jsx(oe,{disablePadding:!0,children:e.jsxs(mt,{onClick:E,sx:Ae.listItemButton,children:[e.jsx(Mt,{children:e.jsx(u,{sx:{...Ae.iconContainer,bgcolor:"#f4433615"},children:e.jsx(ws,{sx:{color:"#f44336"}})})}),e.jsx(ae,{primary:"Déconnexion"})]})})})]});return e.jsxs(u,{sx:{display:"flex"},children:[e.jsx(B,{color:"inherit","aria-label":"open drawer",edge:"start",onClick:()=>r(!0),sx:{position:"fixed",top:12,left:10,zIndex:1199,display:{xs:"flex",md:"none"}},children:e.jsx(Xr,{})}),e.jsx(Bt,{variant:"temporary",open:t,onClose:()=>r(!1),ModalProps:{keepMounted:!0},sx:Ae.drawer,children:b}),e.jsx(Bt,{variant:"permanent",sx:{display:{xs:"none",md:"block"},"& .MuiDrawer-paper":{boxSizing:"border-box",width:ua,borderRight:"1px solid rgba(0, 0, 0, 0.12)"}},open:!0,children:b})]})},vt=260,bt=({requiredRole:t})=>{const{user:r,isAuthenticated:l,logout:x,loading:s}=fe(),[d,h]=i.useState(!1),[j,g]=i.useState(null),[p,a]=i.useState(null),[D,E]=i.useState(null),v=ke();if(Ot(),s)return e.jsxs(u,{sx:{display:"flex",justifyContent:"center",alignItems:"center",height:"100vh",flexDirection:"column",gap:2},children:[e.jsx(H,{size:60}),e.jsx(c,{variant:"h6",color:"text.secondary",children:"Chargement..."})]});if(!l())return console.log("🔒 DashboardLayout: Utilisateur non authentifié, redirection vers /login"),e.jsx(Me,{to:"/login"});console.log("👤 DashboardLayout: Utilisateur authentifié:",{id:r?.id,email:r?.email,role:r?.role,requiredRole:t});const f=typeof r?.role=="object"&&r?.role!==null?r.role.name:r?.role;if(t&&f!==t){console.log(`🚫 DashboardLayout: Rôle incorrect. Requis: ${t}, Actuel: ${f}`);const o=`/${f}/dashboard`;return console.log(`📍 DashboardLayout: Redirection vers ${o}`),e.jsx(Me,{to:o,replace:!0})}console.log("✅ DashboardLayout: Accès autorisé pour le rôle:",f);const P=o=>{h(o)},W=o=>{g(o.currentTarget)},b=o=>{a(o.currentTarget)},y=o=>{E(o.currentTarget)},C=()=>{g(null),a(null),E(null)},m=()=>{C(),x()};return e.jsxs(u,{sx:{display:"flex"},children:[e.jsx(xa,{open:d,toggleDrawer:P}),e.jsxs(u,{component:"main",sx:{flexGrow:1,p:3,width:{md:`calc(100% - ${vt}px)`},ml:{md:`${vt}px`}},children:[e.jsx(Gt,{position:"fixed",sx:{width:{md:`calc(100% - ${vt}px)`},ml:{md:`${vt}px`},bgcolor:"white",color:"text.primary",boxShadow:"0 1px 3px rgba(0,0,0,0.12)"},children:e.jsxs(ut,{children:[e.jsx(de,{title:"Retour",children:e.jsx(B,{edge:"start",color:"inherit","aria-label":"retour",onClick:()=>v(-1),sx:{mr:2},children:e.jsx(rn,{})})}),e.jsxs(c,{variant:"h6",noWrap:!0,component:"div",sx:{flexGrow:1,display:{xs:"none",sm:"block"}},children:[r?.role==="admin"&&"Administration",r?.role==="eleveur"&&"Espace Éleveur",r?.role==="veterinaire"&&"Espace Vétérinaire",r?.role==="marchand"&&"Espace Marchand"]}),e.jsx(de,{title:"Changer de langue",children:e.jsx(B,{size:"large",color:"inherit",onClick:y,children:e.jsx(Ke,{})})}),e.jsxs(it,{anchorEl:D,open:!!D,onClose:C,children:[e.jsx(I,{onClick:C,children:"Français"}),e.jsx(I,{onClick:C,children:"العربية"})]}),e.jsx(de,{title:"Notifications",children:e.jsx(B,{size:"large",color:"inherit",onClick:b,children:e.jsx(Pt,{badgeContent:3,color:"error",children:e.jsx(Le,{})})})}),e.jsxs(it,{anchorEl:p,open:!!p,onClose:C,children:[e.jsx(I,{onClick:C,children:"Notification 1"}),e.jsx(I,{onClick:C,children:"Notification 2"}),e.jsx(I,{onClick:C,children:"Notification 3"})]}),e.jsx(de,{title:"Profil",children:e.jsx(B,{size:"large",edge:"end",onClick:W,color:"inherit",children:e.jsx(nn,{})})}),e.jsxs(it,{anchorEl:j,open:!!j,onClose:C,children:[e.jsx(I,{onClick:C,children:"Profil"}),e.jsx(I,{onClick:C,children:"Paramètres"}),e.jsx(I,{onClick:m,children:"Déconnexion"})]})]})}),e.jsx(ut,{}),e.jsx(u,{sx:{mt:2},children:e.jsx(ur,{})})]})]})},ma=({id:t,children:r,isActive:l})=>{const{attributes:x,listeners:s,setNodeRef:d,transform:h,transition:j,isDragging:g}=Sr({id:t}),p={transform:wr.Transform.toString(h),transition:j,opacity:g?.5:1};return e.jsx("div",{ref:d,style:p,children:e.jsx(Z,{sx:{mb:2,border:g?"2px dashed #1976d2":"1px solid #e0e0e0",backgroundColor:l?"#f3f4f6":"white","&:hover":{backgroundColor:"#f9f9f9",boxShadow:2}},children:e.jsxs(Y,{sx:{display:"flex",alignItems:"center",py:2},children:[e.jsx(B,{...x,...s,sx:{cursor:"grab",mr:2,"&:active":{cursor:"grabbing"}},children:e.jsx(Ls,{})}),r]})})})},pa=()=>{const[t,r]=i.useState(null),[l,x]=i.useState([{id:"hero",name:"Section Hero",description:"Bannière principale avec titre et call-to-action",visible:!0,order:1},{id:"features",name:"Section Fonctionnalités",description:"Présentation des principales fonctionnalités",visible:!0,order:2},{id:"about",name:"Section À Propos",description:"Informations sur l'entreprise",visible:!0,order:3},{id:"services",name:"Section Services",description:"Liste des services proposés",visible:!0,order:4},{id:"testimonials",name:"Section Témoignages",description:"Avis et retours clients",visible:!1,order:5},{id:"contact",name:"Section Contact",description:"Formulaire de contact et informations",visible:!0,order:6}]),s=hr(es(Cr,{activationConstraint:{distance:8}}),es(yr,{coordinateGetter:br})),d=a=>{r(a.active.id)},h=a=>{const{active:D,over:E}=a;D.id!==E?.id&&x(v=>{const f=v.findIndex(b=>b.id===D.id),P=v.findIndex(b=>b.id===E.id);return vr(v,f,P).map((b,y)=>({...b,order:y+1}))}),r(null)},j=()=>{r(null)},g=a=>{x(l.map(D=>D.id===a?{...D,visible:!D.visible}:D))},p=l.find(a=>a.id===t);return e.jsxs(u,{sx:{p:3,maxWidth:800,mx:"auto"},children:[e.jsx(c,{variant:"h4",gutterBottom:!0,sx:{mb:3},children:"🏠 Gestionnaire de Page d'Accueil"}),e.jsxs(_,{sx:{p:3},children:[e.jsx(c,{variant:"h6",gutterBottom:!0,sx:{mb:3},children:"Réorganisez les sections de votre page d'accueil"}),e.jsx(c,{variant:"body2",color:"text.secondary",sx:{mb:3},children:"Glissez-déposez les sections pour changer leur ordre d'affichage"}),e.jsxs(xr,{sensors:s,collisionDetection:pr,onDragStart:d,onDragEnd:h,onDragCancel:j,modifiers:[mr],children:[e.jsx(gr,{items:l.map(a=>a.id),strategy:jr,children:l.map((a,D)=>e.jsxs(ma,{id:a.id,isActive:a.id===t,children:[e.jsxs(u,{sx:{flexGrow:1},children:[e.jsxs(u,{sx:{display:"flex",alignItems:"center",mb:1},children:[e.jsx(c,{variant:"h6",sx:{mr:2},children:a.name}),e.jsx(se,{label:`Position ${D+1}`,size:"small",color:"primary",variant:"outlined",sx:{mr:1}}),e.jsx(se,{label:a.visible?"Visible":"Masquée",size:"small",color:a.visible?"success":"default",variant:a.visible?"filled":"outlined"})]}),e.jsx(c,{variant:"body2",color:"text.secondary",children:a.description})]}),e.jsx(B,{onClick:()=>g(a.id),color:a.visible?"primary":"default",sx:{ml:1},children:a.visible?e.jsx(rt,{}):e.jsx(Qt,{})})]},a.id))}),e.jsx(fr,{children:p?e.jsx(Z,{sx:{opacity:.9,boxShadow:4,transform:"rotate(5deg)"},children:e.jsxs(Y,{sx:{display:"flex",alignItems:"center",py:2},children:[e.jsx(Ls,{sx:{mr:2,color:"primary.main"}}),e.jsxs(u,{children:[e.jsx(c,{variant:"h6",children:p.name}),e.jsx(c,{variant:"body2",color:"text.secondary",children:p.description})]})]})}):null})]}),e.jsx(u,{sx:{mt:3,p:2,backgroundColor:"#f5f5f5",borderRadius:1},children:e.jsx(c,{variant:"caption",color:"text.secondary",children:"💡 Conseils : Utilisez les icônes d'œil pour masquer/afficher les sections. L'ordre d'affichage correspond à l'ordre dans cette liste."})})]})]})};var ga=Object.defineProperty,ja=(t,r,l)=>r in t?ga(t,r,{enumerable:!0,configurable:!0,writable:!0,value:l}):t[r]=l,xs=(t,r,l)=>ja(t,typeof r!="symbol"?r+"":r,l);class fa extends qe.Component{constructor(r){super(r),xs(this,"handleReload",()=>{window.location.reload()}),xs(this,"handleGoBack",()=>{window.history.back()}),this.state={hasError:!1,error:null,errorInfo:null}}static getDerivedStateFromError(r){return{hasError:!0}}componentDidCatch(r,l){console.error("ErrorBoundary a capturé une erreur:",r,l),this.setState({error:r,errorInfo:l})}render(){return this.state.hasError?e.jsx(u,{display:"flex",flexDirection:"column",alignItems:"center",justifyContent:"center",minHeight:"50vh",p:3,children:e.jsxs(_,{elevation:3,sx:{p:4,textAlign:"center",maxWidth:600,width:"100%"},children:[e.jsx(an,{color:"error",sx:{fontSize:64,mb:2}}),e.jsx(c,{variant:"h4",gutterBottom:!0,color:"error",children:"Oups ! Une erreur s'est produite"}),e.jsx(c,{variant:"body1",color:"text.secondary",paragraph:!0,children:"Nous nous excusons pour ce désagrément. Une erreur inattendue s'est produite."}),!1,e.jsxs(u,{sx:{mt:3,display:"flex",gap:2,justifyContent:"center"},children:[e.jsx(M,{variant:"contained",color:"primary",onClick:this.handleReload,children:"Recharger la page"}),e.jsx(M,{variant:"outlined",onClick:this.handleGoBack,children:"Retour"})]})]})}):this.props.children}}const va=()=>{const{user:t}=fe(),[r,l]=i.useState([]),[x,s]=i.useState([]),[d,h]=i.useState(!0),[j,g]=i.useState(""),[p,a]=i.useState(""),[D,E]=i.useState(!1),[v,f]=i.useState(0),[P,W]=i.useState(1),[b,y]=i.useState(1),[C,m]=i.useState(null),[o,k]=i.useState({title:"",message:"",type:"info",recipients:"all",roleId:"",userId:"",notificationType:"both"});i.useEffect(()=>{V(),U()},[P]);const V=async()=>{try{h(!0);const w=await N.get(`/admin/notifications?page=${P}&limit=10`);l(w.data.notifications||[]),y(w.data.pagination?.totalPages||1),g("")}catch(w){console.error("Error fetching notifications:",w),g("Erreur lors de la récupération des notifications")}finally{h(!1)}},U=async()=>{try{const w=await N.get("/admin/users");s(w.data.users)}catch(w){console.error("Error fetching users:",w)}},$=w=>{const{name:me,value:nt}=w.target;k(Tt=>({...Tt,[me]:nt}))},G=(w,me)=>{f(me)},z=()=>{E(!0)},O=()=>{E(!1)},L=async()=>{try{h(!0);const w={...o};o.recipients==="all"?(delete w.roleId,delete w.userId):o.recipients==="role"?delete w.userId:o.recipients==="specific"&&delete w.roleId;const me=await N.post("/admin/notifications/send",w);a("Notification envoyée avec succès"),V(),O()}catch(w){console.error("Error sending notification:",w),g(w.response?.data?.message||"Erreur lors de l'envoi de la notification")}finally{h(!1)}},T=w=>{m(w)},q=async()=>{try{h(!0),await N.delete(`/admin/notifications/${C}`),a("Notification supprimée avec succès"),V(),m(null)}catch(w){console.error("Error deleting notification:",w),g(w.response?.data?.message||"Erreur lors de la suppression de la notification")}finally{h(!1)}},X=(w,me)=>{W(me)},xe=async()=>{try{h(!0),await N.post("/admin/notifications/test"),a("Notification de test envoyée avec succès")}catch(w){console.error("Error sending test notification:",w),g(w.response?.data?.message||"Erreur lors de l'envoi de la notification de test")}finally{h(!1)}},$e=w=>{switch(w){case"email":return e.jsx(It,{fontSize:"small"});case"push":return e.jsx(Lt,{fontSize:"small"});case"both":return e.jsxs(u,{sx:{display:"flex"},children:[e.jsx(It,{fontSize:"small"}),e.jsx(Lt,{fontSize:"small"})]});default:return e.jsx(Le,{fontSize:"small"})}},Pe=w=>{switch(w){case"email":return"Email";case"push":return"Push";case"both":return"Email & Push";default:return"Inconnu"}},R=w=>{switch(w){case"sent":return e.jsx(se,{label:"Envoyée",color:"success",size:"small"});case"failed":return e.jsx(se,{label:"Échec",color:"error",size:"small"});case"pending":return e.jsx(se,{label:"En attente",color:"warning",size:"small"});default:return e.jsx(se,{label:w,size:"small"})}};return e.jsxs(u,{children:[e.jsxs(_,{elevation:3,sx:{p:3,mb:3},children:[e.jsxs(u,{display:"flex",justifyContent:"space-between",alignItems:"center",mb:2,children:[e.jsxs(c,{variant:"h5",component:"h1",gutterBottom:!0,sx:{display:"flex",alignItems:"center",gap:1},children:[e.jsx(Le,{color:"primary"})," Gestion des Notifications"]}),e.jsxs(u,{children:[e.jsx(M,{variant:"outlined",color:"primary",startIcon:e.jsx(on,{}),onClick:xe,sx:{mr:1},children:"Notification de Test"}),e.jsx(M,{variant:"contained",color:"primary",startIcon:e.jsx(Le,{}),onClick:z,children:"Nouvelle Notification"})]})]}),e.jsxs(xt,{value:v,onChange:G,sx:{mb:2},children:[e.jsx(ge,{label:"Historique des Notifications"}),e.jsx(ge,{label:"Paramètres"})]}),j&&e.jsx(te,{severity:"error",sx:{mb:2},children:j}),p&&e.jsx(te,{severity:"success",sx:{mb:2},children:p}),v===0&&e.jsx(e.Fragment,{children:d&&!r.length?e.jsx(u,{display:"flex",justifyContent:"center",my:4,children:e.jsx(H,{})}):e.jsxs(e.Fragment,{children:[e.jsx(ze,{component:_,sx:{mt:2},children:e.jsxs(Ne,{children:[e.jsx(_e,{children:e.jsxs(ie,{children:[e.jsx(S,{children:"Titre"}),e.jsx(S,{children:"Message"}),e.jsx(S,{children:"Type"}),e.jsx(S,{children:"Méthode"}),e.jsx(S,{children:"Destinataires"}),e.jsx(S,{children:"Date"}),e.jsx(S,{children:"Statut"}),e.jsx(S,{align:"right",children:"Actions"})]})}),e.jsx(Be,{children:r.length>0?r.map(w=>e.jsxs(ie,{children:[e.jsx(S,{children:w.title}),e.jsxs(S,{children:[w.message.substring(0,50),"..."]}),e.jsx(S,{children:e.jsx(se,{label:w.type,color:w.type==="info"?"info":w.type==="warning"?"warning":"error",size:"small"})}),e.jsxs(S,{children:[$e(w.notificationType),Pe(w.notificationType)]}),e.jsx(S,{children:w.recipients==="all"?"Tous les utilisateurs":w.recipients==="role"?`Rôle: ${w.roleName}`:`Utilisateur: ${w.userName}`}),e.jsx(S,{children:new Date(w.created_at).toLocaleDateString()}),e.jsx(S,{children:R(w.status)}),e.jsx(S,{align:"right",children:e.jsx(B,{color:"error",onClick:()=>T(w.id),size:"small",children:e.jsx(st,{fontSize:"small"})})})]},w.id)):e.jsx(ie,{children:e.jsx(S,{colSpan:8,align:"center",children:"Aucune notification trouvée"})})})]})}),b>1&&e.jsx(u,{display:"flex",justifyContent:"center",mt:3,children:e.jsx(Rs,{count:b,page:P,onChange:X,color:"primary"})})]})}),v===1&&e.jsxs(_,{elevation:1,sx:{p:3},children:[e.jsxs(c,{variant:"h6",gutterBottom:!0,sx:{display:"flex",alignItems:"center",gap:1},children:[e.jsx(qt,{})," Paramètres de Notification"]}),e.jsx(ue,{sx:{my:2}}),e.jsxs(n,{container:!0,spacing:3,children:[e.jsx(n,{item:!0,xs:12,md:6,children:e.jsxs(Z,{children:[e.jsxs(Y,{children:[e.jsxs(c,{variant:"h6",gutterBottom:!0,children:[e.jsx(It,{sx:{mr:1,verticalAlign:"middle"}}),"Configuration Email"]}),e.jsx(c,{variant:"body2",color:"text.secondary",paragraph:!0,children:"Configurez les paramètres de notification par email pour la plateforme."}),e.jsx(A,{label:"Email expéditeur",fullWidth:!0,margin:"normal",defaultValue:"<EMAIL>"}),e.jsx(A,{label:"Nom expéditeur",fullWidth:!0,margin:"normal",defaultValue:"Poultray DZ"})]}),e.jsxs(rs,{children:[e.jsx(M,{size:"small",color:"primary",children:"Enregistrer"}),e.jsx(M,{size:"small",onClick:xe,children:"Tester"})]})]})}),e.jsx(n,{item:!0,xs:12,md:6,children:e.jsxs(Z,{children:[e.jsxs(Y,{children:[e.jsxs(c,{variant:"h6",gutterBottom:!0,children:[e.jsx(Lt,{sx:{mr:1,verticalAlign:"middle"}}),"Configuration Push"]}),e.jsx(c,{variant:"body2",color:"text.secondary",paragraph:!0,children:"Configurez les paramètres de notification push pour la plateforme."}),e.jsxs(Q,{fullWidth:!0,margin:"normal",children:[e.jsx(J,{children:"Activer les notifications push"}),e.jsxs(K,{value:"enabled",label:"Activer les notifications push",children:[e.jsx(I,{value:"enabled",children:"Activé"}),e.jsx(I,{value:"disabled",children:"Désactivé"})]})]}),e.jsx(A,{label:"Firebase Server Key",fullWidth:!0,margin:"normal",type:"password",defaultValue:"••••••••••••••••••••••••••••••••"})]}),e.jsxs(rs,{children:[e.jsx(M,{size:"small",color:"primary",children:"Enregistrer"}),e.jsx(M,{size:"small",onClick:xe,children:"Tester"})]})]})})]})]})]}),e.jsxs(Ce,{open:D,onClose:O,maxWidth:"md",fullWidth:!0,children:[e.jsx(Se,{children:"Nouvelle Notification"}),e.jsxs(we,{children:[e.jsx(A,{autoFocus:!0,margin:"dense",name:"title",label:"Titre",type:"text",fullWidth:!0,value:o.title,onChange:$,required:!0,sx:{mb:2}}),e.jsx(A,{margin:"dense",name:"message",label:"Message",multiline:!0,rows:4,fullWidth:!0,value:o.message,onChange:$,required:!0,sx:{mb:2}}),e.jsxs(n,{container:!0,spacing:2,children:[e.jsx(n,{item:!0,xs:12,sm:6,children:e.jsxs(Q,{fullWidth:!0,margin:"dense",children:[e.jsx(J,{children:"Type de notification"}),e.jsxs(K,{name:"type",value:o.type,label:"Type de notification",onChange:$,children:[e.jsx(I,{value:"info",children:"Information"}),e.jsx(I,{value:"warning",children:"Avertissement"}),e.jsx(I,{value:"error",children:"Erreur"}),e.jsx(I,{value:"success",children:"Succès"})]})]})}),e.jsx(n,{item:!0,xs:12,sm:6,children:e.jsxs(Q,{fullWidth:!0,margin:"dense",children:[e.jsx(J,{children:"Méthode d'envoi"}),e.jsxs(K,{name:"notificationType",value:o.notificationType,label:"Méthode d'envoi",onChange:$,children:[e.jsx(I,{value:"email",children:"Email uniquement"}),e.jsx(I,{value:"push",children:"Push uniquement"}),e.jsx(I,{value:"both",children:"Email et Push"})]})]})})]}),e.jsxs(Q,{fullWidth:!0,margin:"dense",sx:{mt:2},children:[e.jsx(J,{children:"Destinataires"}),e.jsxs(K,{name:"recipients",value:o.recipients,label:"Destinataires",onChange:$,children:[e.jsx(I,{value:"all",children:"Tous les utilisateurs"}),e.jsx(I,{value:"role",children:"Par rôle"}),e.jsx(I,{value:"specific",children:"Utilisateur spécifique"})]})]}),o.recipients==="role"&&e.jsxs(Q,{fullWidth:!0,margin:"dense",sx:{mt:2},children:[e.jsx(J,{children:"Rôle"}),e.jsxs(K,{name:"roleId",value:o.roleId,label:"Rôle",onChange:$,children:[e.jsx(I,{value:"1",children:"Administrateur"}),e.jsx(I,{value:"2",children:"Éleveur"}),e.jsx(I,{value:"3",children:"Marchand"}),e.jsx(I,{value:"4",children:"Vétérinaire"})]})]}),o.recipients==="specific"&&e.jsxs(Q,{fullWidth:!0,margin:"dense",sx:{mt:2},children:[e.jsx(J,{children:"Utilisateur"}),e.jsx(K,{name:"userId",value:o.userId,label:"Utilisateur",onChange:$,children:x.map(w=>e.jsxs(I,{value:w.id,children:[w.username," (",w.email,")"]},w.id))})]})]}),e.jsxs(Ie,{children:[e.jsx(M,{onClick:O,children:"Annuler"}),e.jsx(M,{onClick:L,variant:"contained",color:"primary",disabled:!o.title||!o.message,children:d?e.jsx(H,{size:24}):"Envoyer"})]})]}),e.jsxs(Ce,{open:!!C,onClose:()=>m(null),children:[e.jsx(Se,{children:"Confirmer la suppression"}),e.jsx(we,{children:e.jsx(c,{children:"Êtes-vous sûr de vouloir supprimer cette notification ? Cette action est irréversible."})}),e.jsxs(Ie,{children:[e.jsx(M,{onClick:()=>m(null),children:"Annuler"}),e.jsx(M,{onClick:q,color:"error",variant:"contained",children:d?e.jsx(H,{size:24}):"Supprimer"})]})]}),e.jsx(kt,{open:!!j||!!p,autoHideDuration:6e3,onClose:()=>{g(""),a("")},children:e.jsx(te,{onClose:()=>{g(""),a("")},severity:j?"error":"success",sx:{width:"100%"},children:j||p})})]})},yt=()=>{const{user:t,updateProfile:r}=fe(),[l,x]=i.useState(!1),[s,d]=i.useState(!1),[h,j]=i.useState(null),[g,p]=i.useState(null),[a,D]=i.useState(!1),[E,v]=i.useState({username:"",email:"",first_name:"",last_name:"",phone:"",address:""}),[f,P]=i.useState({currentPassword:"",newPassword:"",confirmPassword:""});i.useEffect(()=>{t&&v({username:t.username||"",email:t.email||"",first_name:t.first_name||"",last_name:t.last_name||"",phone:t.phone||"",address:t.address||""})},[t]);const W=U=>{const{name:$,value:G}=U.target;v(z=>({...z,[$]:G}))},b=U=>{const{name:$,value:G}=U.target;P(z=>({...z,[$]:G}))},y=async()=>{try{d(!0),j(null);const U=await N.put("/auth/profile",E);await r(E),p("Profil mis à jour avec succès"),x(!1)}catch(U){console.error("Erreur lors de la mise à jour du profil:",U),j("Erreur lors de la mise à jour du profil")}finally{d(!1)}},C=()=>{t&&v({username:t.username||"",email:t.email||"",first_name:t.first_name||"",last_name:t.last_name||"",phone:t.phone||"",address:t.address||""}),x(!1),j(null)},m=async()=>{try{if(f.newPassword!==f.confirmPassword){j("Les mots de passe ne correspondent pas");return}if(f.newPassword.length<6){j("Le mot de passe doit contenir au moins 6 caractères");return}d(!0),j(null),await N.put("/auth/change-password",{oldPassword:f.currentPassword,newPassword:f.newPassword}),p("Mot de passe modifié avec succès"),D(!1),P({currentPassword:"",newPassword:"",confirmPassword:""})}catch(U){console.error("Erreur lors du changement de mot de passe:",U),j("Erreur lors du changement de mot de passe")}finally{d(!1)}},o=()=>t?.first_name&&t?.last_name?`${t.first_name[0]}${t.last_name[0]}`:t?.username?t.username.substring(0,2).toUpperCase():"U",k=()=>t?.first_name&&t?.last_name?`${t.first_name} ${t.last_name}`:t?.username||"Utilisateur",V=U=>{const $=typeof U=="object"&&U!==null?U.name:U;return{admin:"Administrateur",eleveur:"Éleveur",veterinaire:"Vétérinaire",marchand:"Marchand"}[$]||$};return t?e.jsxs(u,{sx:{p:3},children:[e.jsx(c,{variant:"h4",gutterBottom:!0,children:"Mon Profil"}),h&&e.jsx(te,{severity:"error",sx:{mb:2},onClose:()=>j(null),children:h}),g&&e.jsx(te,{severity:"success",sx:{mb:2},onClose:()=>p(null),children:g}),e.jsxs(n,{container:!0,spacing:3,children:[e.jsx(n,{item:!0,xs:12,md:4,children:e.jsx(Z,{children:e.jsxs(Y,{sx:{textAlign:"center",p:3},children:[e.jsxs(u,{sx:{position:"relative",display:"inline-block",mb:2},children:[e.jsx(re,{sx:{width:120,height:120,fontSize:"2rem",bgcolor:"primary.main",mx:"auto"},children:o()}),e.jsx(B,{sx:{position:"absolute",bottom:0,right:0,bgcolor:"background.paper",border:"2px solid",borderColor:"divider","&:hover":{bgcolor:"action.hover"}},size:"small",children:e.jsx(ln,{fontSize:"small"})})]}),e.jsx(c,{variant:"h5",gutterBottom:!0,children:k()}),e.jsx(c,{variant:"body1",color:"text.secondary",gutterBottom:!0,children:V(t.role)}),e.jsx(c,{variant:"body2",color:"text.secondary",children:t.email}),e.jsx(ue,{sx:{my:2}}),e.jsx(M,{variant:"outlined",startIcon:e.jsx(cn,{}),onClick:()=>D(!0),fullWidth:!0,children:"Changer le mot de passe"})]})})}),e.jsx(n,{item:!0,xs:12,md:8,children:e.jsx(Z,{children:e.jsxs(Y,{children:[e.jsxs(u,{sx:{display:"flex",justifyContent:"space-between",alignItems:"center",mb:3},children:[e.jsx(c,{variant:"h6",children:"Informations personnelles"}),l?e.jsxs(u,{sx:{display:"flex",gap:1},children:[e.jsx(M,{variant:"outlined",startIcon:e.jsx(dn,{}),onClick:C,disabled:s,children:"Annuler"}),e.jsx(M,{variant:"contained",startIcon:e.jsx(Xe,{}),onClick:y,disabled:s,children:"Sauvegarder"})]}):e.jsx(M,{variant:"outlined",startIcon:e.jsx(Oe,{}),onClick:()=>x(!0),children:"Modifier"})]}),e.jsxs(n,{container:!0,spacing:3,children:[e.jsx(n,{item:!0,xs:12,sm:6,children:e.jsx(A,{name:"username",label:"Nom d'utilisateur",value:E.username,onChange:W,fullWidth:!0,disabled:!l,InputProps:{startAdornment:e.jsx(be,{sx:{mr:1,color:"action.active"}})}})}),e.jsx(n,{item:!0,xs:12,sm:6,children:e.jsx(A,{name:"email",label:"Email",type:"email",value:E.email,onChange:W,fullWidth:!0,disabled:!l,InputProps:{startAdornment:e.jsx(It,{sx:{mr:1,color:"action.active"}})}})}),e.jsx(n,{item:!0,xs:12,sm:6,children:e.jsx(A,{name:"first_name",label:"Prénom",value:E.first_name,onChange:W,fullWidth:!0,disabled:!l})}),e.jsx(n,{item:!0,xs:12,sm:6,children:e.jsx(A,{name:"last_name",label:"Nom",value:E.last_name,onChange:W,fullWidth:!0,disabled:!l})}),e.jsx(n,{item:!0,xs:12,sm:6,children:e.jsx(A,{name:"phone",label:"Téléphone",value:E.phone,onChange:W,fullWidth:!0,disabled:!l,InputProps:{startAdornment:e.jsx(un,{sx:{mr:1,color:"action.active"}})}})}),e.jsx(n,{item:!0,xs:12,sm:6,children:e.jsx(A,{label:"Rôle",value:V(t.role),fullWidth:!0,disabled:!0})}),e.jsx(n,{item:!0,xs:12,children:e.jsx(A,{name:"address",label:"Adresse",value:E.address,onChange:W,fullWidth:!0,disabled:!l,multiline:!0,rows:3,InputProps:{startAdornment:e.jsx(hn,{sx:{mr:1,color:"action.active",alignSelf:"flex-start",mt:1}})}})})]})]})})})]}),e.jsxs(Ce,{open:a,onClose:()=>D(!1),maxWidth:"sm",fullWidth:!0,children:[e.jsx(Se,{children:"Changer le mot de passe"}),e.jsx(we,{children:e.jsxs(n,{container:!0,spacing:2,sx:{mt:1},children:[e.jsx(n,{item:!0,xs:12,children:e.jsx(A,{name:"currentPassword",label:"Mot de passe actuel",type:"password",value:f.currentPassword,onChange:b,fullWidth:!0,required:!0})}),e.jsx(n,{item:!0,xs:12,children:e.jsx(A,{name:"newPassword",label:"Nouveau mot de passe",type:"password",value:f.newPassword,onChange:b,fullWidth:!0,required:!0,helperText:"Au moins 6 caractères"})}),e.jsx(n,{item:!0,xs:12,children:e.jsx(A,{name:"confirmPassword",label:"Confirmer le nouveau mot de passe",type:"password",value:f.confirmPassword,onChange:b,fullWidth:!0,required:!0})})]})}),e.jsxs(Ie,{children:[e.jsx(M,{onClick:()=>D(!1),children:"Annuler"}),e.jsx(M,{onClick:m,variant:"contained",disabled:s,children:"Changer le mot de passe"})]})]})]}):e.jsx(u,{sx:{display:"flex",justifyContent:"center",alignItems:"center",height:400},children:e.jsx(c,{children:"Chargement du profil..."})})},ms={getStats:async()=>{try{return(await N.get("/admin/stats")).data}catch(t){throw console.error("Erreur lors de la récupération des statistiques:",t),t}},getUsers:async(t,r=1,l=10)=>{try{return(await N.get(`/admin/users?role=${t}&page=${r}&limit=${l}`)).data}catch(x){throw console.error(`Erreur lors de la récupération des utilisateurs ${t}:`,x),x}},getBlogPosts:async(t=1,r=10)=>{try{return(await N.get(`/admin/blog?page=${t}&limit=${r}`)).data}catch(l){throw console.error("Erreur lors de la récupération des articles de blog:",l),l}},getTranslations:async t=>{try{return(await N.get(`/admin/translations/${t}`)).data}catch(r){throw console.error(`Erreur lors de la récupération des traductions ${t}:`,r),r}}},at=({title:t,value:r,icon:l,color:x})=>{const s=r!=null?typeof r=="number"?r.toLocaleString():r:"0";return e.jsxs(_,{elevation:2,sx:{p:3,display:"flex",flexDirection:"column",height:"100%",borderTop:`4px solid ${x}`},children:[e.jsxs(u,{sx:{display:"flex",justifyContent:"space-between",mb:2},children:[e.jsx(c,{variant:"h6",component:"h2",color:"text.secondary",children:t}),e.jsx(re,{sx:{bgcolor:x,width:40,height:40},children:l})]}),e.jsx(c,{variant:"h4",component:"div",sx:{fontWeight:"bold"},children:s})]})},ps=({data:t,title:r,color:l})=>e.jsxs(Z,{sx:{height:"100%"},children:[e.jsx(ve,{title:r}),e.jsx(ue,{}),e.jsx(Y,{sx:{height:250},children:e.jsx(Re,{width:"100%",height:"100%",children:e.jsxs(bs,{data:t,margin:{top:5,right:30,left:20,bottom:5},children:[e.jsx(Ye,{strokeDasharray:"3 3"}),e.jsx(Qe,{dataKey:"label"}),e.jsx(Je,{}),e.jsx(We,{}),e.jsx(Ut,{dataKey:"value",fill:l})]})})})]}),ba=({activities:t})=>e.jsxs(Z,{sx:{height:"100%"},children:[e.jsx(ve,{title:"Activités Récentes"}),e.jsx(ue,{}),e.jsx(Y,{sx:{p:0},children:e.jsx(le,{children:t.map((r,l)=>e.jsxs(qe.Fragment,{children:[e.jsxs(oe,{alignItems:"flex-start",children:[e.jsx(je,{children:e.jsx(re,{sx:{bgcolor:r.color},children:r.icon})}),e.jsx(ae,{primary:r.title,secondary:e.jsxs(e.Fragment,{children:[e.jsx(c,{variant:"body2",color:"text.primary",component:"span",children:r.description}),e.jsx(c,{variant:"caption",display:"block",color:"text.secondary",children:r.time})]})})]}),l<t.length-1&&e.jsx(ue,{variant:"inset",component:"li"})]},l))})})]}),ya=()=>{const t=["#0088FE","#00C49F","#FFBB28","#FF8042"],r=[{name:"Articles générés",value:12},{name:"Analyses de données",value:8},{name:"Pages créées",value:5},{name:"Traductions",value:15}];return e.jsxs(Z,{sx:{height:"100%"},children:[e.jsx(ve,{title:"Utilisation de l'IA"}),e.jsx(ue,{}),e.jsx(Y,{sx:{height:250},children:e.jsx(Re,{width:"100%",height:"100%",children:e.jsxs(ys,{children:[e.jsx(Cs,{data:r,cx:"50%",cy:"50%",labelLine:!1,label:({name:l,percent:x})=>`${l}: ${(x*100).toFixed(0)}%`,outerRadius:80,fill:"#8884d8",dataKey:"value",children:r.map((l,x)=>e.jsx(Ss,{fill:t[x%t.length]},`cell-${x}`))}),e.jsx(We,{}),e.jsx(dt,{})]})})})]})},Ca=()=>{const t=[{label:"Jan",value:4},{label:"Fév",value:3},{label:"Mar",value:5},{label:"Avr",value:2},{label:"Mai",value:6},{label:"Juin",value:8}];return e.jsxs(Z,{sx:{height:"100%"},children:[e.jsx(ve,{title:"Activité du Blog"}),e.jsx(ue,{}),e.jsx(Y,{sx:{height:250},children:e.jsx(Re,{width:"100%",height:"100%",children:e.jsxs(bs,{data:t,margin:{top:5,right:30,left:20,bottom:5},children:[e.jsx(Ye,{strokeDasharray:"3 3"}),e.jsx(Qe,{dataKey:"label"}),e.jsx(Je,{}),e.jsx(We,{}),e.jsx(Ut,{dataKey:"value",fill:"#8884d8"})]})})})]})};function Sa(){const{user:t}=fe();ke();const[r,l]=i.useState({totalEleveurs:0,totalVolailles:0,totalValeur:0,totalVeterinaires:0,totalMarchands:0}),[x,s]=i.useState(0),[d,h]=i.useState([]),[j,g]=i.useState([]),[p,a]=i.useState(!0),[D,E]=i.useState(null),[v,f]=i.useState([]),P=[{label:"Poulets",value:120},{label:"Dindes",value:80},{label:"Canards",value:40},{label:"Cailles",value:25}],W=[{label:"Alger",value:85},{label:"Oran",value:65},{label:"Constantine",value:45},{label:"Annaba",value:30},{label:"Blida",value:25}],b=[{title:"Nouvel éleveur inscrit",description:"Ahmed Benali a rejoint la plateforme",time:"Il y a 2 heures",icon:e.jsx(be,{}),color:"primary.main"},{title:"Nouveau lot ajouté",description:"500 poulets ajoutés par Ferme Avicole du Sud",time:"Il y a 5 heures",icon:e.jsx(ye,{}),color:"secondary.main"},{title:"Vente effectuée",description:"Vente de 200 dindes pour 150,000 DA",time:"Hier à 14:30",icon:e.jsx(De,{}),color:"success.main"},{title:"Nouvel article de blog",description:"Article sur les techniques d'élevage moderne publié",time:"Hier à 10:15",icon:e.jsx(ct,{}),color:"info.main"},{title:"Analyse IA générée",description:"Rapport sur les tendances du marché avicole",time:"Il y a 2 jours",icon:e.jsx(Jt,{}),color:"warning.main"}];return i.useEffect(()=>{const y=async()=>{try{a(!0);const m=await ms.getStats();l({totalEleveurs:m.totalEleveurs||0,totalVolailles:m.volailles?.total_volailles||0,totalValeur:m.volailles?.prix_moyen||0,totalVeterinaires:m.totalVeterinaires||0,totalMarchands:m.totalMarchands||0}),E(null)}catch(m){console.error("Erreur lors de la récupération des statistiques:",m),E("Erreur lors de la récupération des données")}finally{a(!1)}},C=async()=>{try{const m=await ms.getUsers("eleveur",1,100);g(m.users||m)}catch(m){console.error("Erreur lors de la récupération des utilisateurs:",m)}};y(),C()},[]),p?e.jsx(u,{sx:{display:"flex",justifyContent:"center",alignItems:"center",height:"80vh"},children:e.jsx(H,{})}):D?e.jsx(u,{sx:{display:"flex",justifyContent:"center",alignItems:"center",height:"80vh"},children:e.jsx(c,{color:"error",children:D})}):e.jsx(ee,{maxWidth:"xl",sx:{mt:4,mb:4},children:e.jsxs(n,{container:!0,spacing:3,children:[e.jsx(n,{size:{xs:12,sm:6,md:4,lg:2.4},children:e.jsx(at,{title:"Total Éleveurs",value:r.totalEleveurs,icon:e.jsx(be,{}),color:"#2196f3"})}),e.jsx(n,{size:{xs:12,sm:6,md:4,lg:2.4},children:e.jsx(at,{title:"Total Volailles",value:r.totalVolailles,icon:e.jsx(ye,{}),color:"#4caf50"})}),e.jsx(n,{size:{xs:12,sm:6,md:4,lg:2.4},children:e.jsx(at,{title:"Valeur Totale",value:`${r.totalValeur.toLocaleString()} DA`,icon:e.jsx(De,{}),color:"#ff9800"})}),e.jsx(n,{size:{xs:12,sm:6,md:4,lg:2.4},children:e.jsx(at,{title:"Total Vétérinaires",value:r.totalVeterinaires,icon:e.jsx(be,{}),color:"#f44336"})}),e.jsx(n,{size:{xs:12,sm:6,md:4,lg:2.4},children:e.jsx(at,{title:"Total Marchands",value:r.totalMarchands,icon:e.jsx(be,{}),color:"#9c27b0"})}),e.jsx(n,{size:{xs:12,md:6},children:e.jsx(ps,{data:P,title:"Répartition par Espèces",color:"#2196f3"})}),e.jsx(n,{size:{xs:12,md:6},children:e.jsx(ps,{data:W,title:"Répartition par Région",color:"#4caf50"})}),e.jsx(n,{size:{xs:12,md:4},children:e.jsx(ba,{activities:b})}),e.jsx(n,{size:{xs:12,md:4},children:e.jsx(ya,{})}),e.jsx(n,{size:{xs:12,md:4},children:e.jsx(Ca,{})})]})})}const gs=["#0088FE","#00C49F","#FFBB28","#FF8042","#8884D8","#82ca9d"],He=({title:t,value:r,icon:l,color:x,subtitle:s,trend:d})=>e.jsx(_,{elevation:2,sx:{p:3,height:"100%",display:"flex",flexDirection:"column",background:`linear-gradient(135deg, ${x}15 0%, ${x}05 100%)`,border:`1px solid ${x}30`},children:e.jsxs(u,{sx:{display:"flex",alignItems:"center",mb:2},children:[e.jsx(re,{sx:{bgcolor:x,mr:2},children:l}),e.jsxs(u,{sx:{flex:1},children:[e.jsx(c,{variant:"h4",component:"div",fontWeight:"bold",color:x,children:typeof r=="number"&&r>1e3?`${(r/1e3).toFixed(1)}k`:r}),e.jsx(c,{variant:"body2",color:"text.secondary",children:t}),s&&e.jsx(c,{variant:"caption",color:"text.secondary",children:s})]}),d&&e.jsx(u,{sx:{textAlign:"right"},children:e.jsxs(c,{variant:"caption",color:d>0?"success.main":"error.main",sx:{display:"flex",alignItems:"center"},children:[e.jsx(Fe,{sx:{fontSize:16,mr:.5}}),d>0?"+":"",d,"%"]})})]})}),wa=({eleveurId:t,ouvriers:r,onRefresh:l})=>{const[x,s]=i.useState(!1),[d,h]=i.useState({}),[j,g]=i.useState(!1),p=async()=>{if(!t){console.error("ID éleveur manquant");return}g(!0);try{const a=localStorage.getItem("token");if(!a){console.error("Token manquant");return}await pe.post(`/api/eleveurs/${t}/ouvriers`,d,{headers:{Authorization:`Bearer ${a}`}}),h({}),s(!1),l()}catch(a){console.error("Erreur lors de la création de l'ouvrier:",a)}finally{g(!1)}};return e.jsxs(_,{elevation:2,sx:{p:3,height:"400px"},children:[e.jsxs(u,{sx:{display:"flex",justifyContent:"space-between",alignItems:"center",mb:2},children:[e.jsxs(c,{variant:"h6",sx:{display:"flex",alignItems:"center"},children:[e.jsx(zs,{sx:{mr:1}}),"Gestion des Ouvriers (",r.length,")"]}),e.jsx(M,{variant:"contained",startIcon:e.jsx(Ms,{}),onClick:()=>s(!0),size:"small",children:"Ajouter"})]}),e.jsx(u,{sx:{height:"320px",overflow:"auto"},children:e.jsx(le,{children:r.map(a=>e.jsxs(oe,{divider:!0,children:[e.jsx(je,{children:e.jsx(re,{sx:{bgcolor:"primary.main"},children:a.first_name?.[0]||a.username[0]})}),e.jsx(ae,{primary:`${a.first_name||""} ${a.last_name||""} (${a.username})`,secondary:e.jsxs(u,{children:[e.jsxs(c,{variant:"body2",color:"text.secondary",children:["Email: ",a.email]}),e.jsxs(c,{variant:"caption",color:"text.secondary",children:["Saisies: ",a.nombre_saisies," | Dernière: ",a.derniere_saisie?new Date(a.derniere_saisie).toLocaleDateString():"Aucune"]})]})}),e.jsx(se,{label:a.status,color:a.status==="active"?"success":"default",size:"small"})]},a.id))})}),e.jsxs(Ce,{open:x,onClose:()=>s(!1),maxWidth:"sm",fullWidth:!0,children:[e.jsx(Se,{children:"Ajouter un Ouvrier"}),e.jsxs(we,{children:[e.jsx(A,{fullWidth:!0,label:"Nom d'utilisateur",value:d.username||"",onChange:a=>h({...d,username:a.target.value}),margin:"normal",required:!0}),e.jsx(A,{fullWidth:!0,label:"Email",type:"email",value:d.email||"",onChange:a=>h({...d,email:a.target.value}),margin:"normal",required:!0}),e.jsx(A,{fullWidth:!0,label:"Mot de passe",type:"password",value:d.password||"",onChange:a=>h({...d,password:a.target.value}),margin:"normal",required:!0}),e.jsx(A,{fullWidth:!0,label:"Prénom",value:d.first_name||"",onChange:a=>h({...d,first_name:a.target.value}),margin:"normal"}),e.jsx(A,{fullWidth:!0,label:"Nom",value:d.last_name||"",onChange:a=>h({...d,last_name:a.target.value}),margin:"normal"})]}),e.jsxs(Ie,{children:[e.jsx(M,{onClick:()=>s(!1),children:"Annuler"}),e.jsx(M,{onClick:p,variant:"contained",disabled:j,children:j?e.jsx(H,{size:20}):"Créer"})]})]})]})},Ia=({saisies:t,eleveurId:r,onRefresh:l})=>{const[x,s]=i.useState(!1),[d,h]=i.useState({eleveur_id:r,date_saisie:new Date().toISOString().split("T")[0]}),[j,g]=i.useState(!1),p=async()=>{g(!0);try{const a=localStorage.getItem("token");await pe.post("/api/eleveurs/saisies-quotidiennes",d,{headers:{Authorization:`Bearer ${a}`}}),h({eleveur_id:r,date_saisie:new Date().toISOString().split("T")[0]}),s(!1),l()}catch(a){console.error("Erreur lors de la saisie:",a)}finally{g(!1)}};return e.jsxs(_,{elevation:2,sx:{p:3,height:"400px"},children:[e.jsxs(u,{sx:{display:"flex",justifyContent:"space-between",alignItems:"center",mb:2},children:[e.jsxs(c,{variant:"h6",sx:{display:"flex",alignItems:"center"},children:[e.jsx(ns,{sx:{mr:1}}),"Saisies Quotidiennes"]}),e.jsx(M,{variant:"contained",startIcon:e.jsx(tt,{}),onClick:()=>s(!0),size:"small",children:"Nouvelle saisie"})]}),e.jsx(u,{sx:{height:"320px",overflow:"auto"},children:e.jsx(le,{children:t.map(a=>e.jsxs(oe,{divider:!0,children:[e.jsx(je,{children:e.jsx(re,{sx:{bgcolor:"success.main"},children:e.jsx(ns,{})})}),e.jsx(ae,{primary:`${a.ouvrier_nom} - ${a.ferme_nom||"Ferme principale"}`,secondary:e.jsxs(u,{children:[e.jsxs(c,{variant:"body2",color:"text.secondary",children:["Morts: ",a.nombre_morts," | Malades: ",a.nombre_malades]}),e.jsx(c,{variant:"caption",color:"text.secondary",children:new Date(a.date_saisie).toLocaleDateString()}),a.incidents&&e.jsxs(c,{variant:"caption",color:"error.main",display:"block",children:["Incident: ",a.incidents]})]})})]},a.id))})}),e.jsxs(Ce,{open:x,onClose:()=>s(!1),maxWidth:"md",fullWidth:!0,children:[e.jsx(Se,{children:"Nouvelle Saisie Quotidienne"}),e.jsx(we,{children:e.jsxs(n,{container:!0,spacing:2,children:[e.jsx(n,{item:!0,xs:12,sm:6,children:e.jsx(A,{fullWidth:!0,label:"Date de saisie",type:"date",value:d.date_saisie||"",onChange:a=>h({...d,date_saisie:a.target.value}),margin:"normal",InputLabelProps:{shrink:!0},required:!0})}),e.jsx(n,{item:!0,xs:12,sm:6,children:e.jsx(A,{fullWidth:!0,label:"ID Volaille",type:"number",value:d.volaille_id||"",onChange:a=>h({...d,volaille_id:parseInt(a.target.value)}),margin:"normal"})}),e.jsx(n,{item:!0,xs:12,sm:6,children:e.jsx(A,{fullWidth:!0,label:"Nombre de morts",type:"number",value:d.nombre_morts||0,onChange:a=>h({...d,nombre_morts:parseInt(a.target.value)}),margin:"normal",required:!0})}),e.jsx(n,{item:!0,xs:12,sm:6,children:e.jsx(A,{fullWidth:!0,label:"Nombre de malades",type:"number",value:d.nombre_malades||0,onChange:a=>h({...d,nombre_malades:parseInt(a.target.value)}),margin:"normal"})}),e.jsx(n,{item:!0,xs:12,sm:6,children:e.jsx(A,{fullWidth:!0,label:"Température moyenne (°C)",type:"number",value:d.temperature_moyenne||"",onChange:a=>h({...d,temperature_moyenne:parseFloat(a.target.value)}),margin:"normal"})}),e.jsx(n,{item:!0,xs:12,sm:6,children:e.jsx(A,{fullWidth:!0,label:"Humidité moyenne (%)",type:"number",value:d.humidite_moyenne||"",onChange:a=>h({...d,humidite_moyenne:parseFloat(a.target.value)}),margin:"normal"})}),e.jsx(n,{item:!0,xs:12,children:e.jsx(A,{fullWidth:!0,label:"Incidents",multiline:!0,rows:2,value:d.incidents||"",onChange:a=>h({...d,incidents:a.target.value}),margin:"normal"})}),e.jsx(n,{item:!0,xs:12,children:e.jsx(A,{fullWidth:!0,label:"Observations",multiline:!0,rows:2,value:d.observations||"",onChange:a=>h({...d,observations:a.target.value}),margin:"normal"})})]})}),e.jsxs(Ie,{children:[e.jsx(M,{onClick:()=>s(!1),children:"Annuler"}),e.jsx(M,{onClick:p,variant:"contained",disabled:j,children:j?e.jsx(H,{size:20}):"Enregistrer"})]})]})]})},Aa=({eleveurId:t})=>{const[r,l]=i.useState([]),[x,s]=i.useState(""),[d,h]=i.useState(!1),j=async()=>{h(!0);try{const g=localStorage.getItem("token"),p=await pe.get(`/api/eleveurs/${t}/activites`,{headers:{Authorization:`Bearer ${g}`},params:x?{type_activite:x}:{}});l(p.data.data.activites)}catch(g){console.error("Erreur lors de la récupération des activités:",g)}finally{h(!1)}};return i.useEffect(()=>{j()},[x,t]),e.jsxs(_,{elevation:2,sx:{p:3,height:"400px"},children:[e.jsxs(u,{sx:{display:"flex",justifyContent:"space-between",alignItems:"center",mb:2},children:[e.jsxs(c,{variant:"h6",sx:{display:"flex",alignItems:"center"},children:[e.jsx(xn,{sx:{mr:1}}),"Vue Multi-Activités"]}),e.jsxs(Q,{size:"small",sx:{minWidth:120},children:[e.jsx(J,{children:"Filtrer"}),e.jsxs(K,{value:x,onChange:g=>s(g.target.value),label:"Filtrer",children:[e.jsx(I,{value:"",children:"Toutes"}),e.jsx(I,{value:"poussins",children:"Poussins"}),e.jsx(I,{value:"dindes",children:"Dindes"}),e.jsx(I,{value:"poulets_chair",children:"Poulets de chair"}),e.jsx(I,{value:"pondeuses",children:"Pondeuses"})]})]})]}),e.jsx(u,{sx:{height:"320px",overflow:"auto"},children:d?e.jsx(u,{display:"flex",justifyContent:"center",alignItems:"center",height:"100%",children:e.jsx(H,{})}):e.jsx(n,{container:!0,spacing:2,children:r.map((g,p)=>e.jsx(n,{item:!0,xs:12,sm:6,children:e.jsx(Z,{sx:{height:"100%"},children:e.jsxs(Y,{children:[e.jsxs(c,{variant:"h6",component:"div",sx:{display:"flex",alignItems:"center"},children:[e.jsx(ye,{sx:{mr:1,color:gs[p%gs.length]}}),g.type_activite]}),e.jsxs(c,{variant:"body2",color:"text.secondary",gutterBottom:!0,children:["Race: ",g.race]}),e.jsxs(u,{sx:{mt:2},children:[e.jsxs(c,{variant:"body2",children:["Stock: ",e.jsx("strong",{children:g.stock_total})]}),e.jsxs(c,{variant:"body2",children:["Revenus: ",e.jsxs("strong",{children:[g.revenus_totaux,"€"]})]}),e.jsxs(c,{variant:"body2",children:["Lots: ",e.jsx("strong",{children:g.nombre_lots})]}),g.production_oeufs>0&&e.jsxs(c,{variant:"body2",children:["Œufs: ",e.jsx("strong",{children:g.production_oeufs})]})]})]})})},p))})})]})},Da=()=>{const{user:t}=fe(),[r,l]=i.useState(null),[x,s]=i.useState([]),[d,h]=i.useState(!0),[j,g]=i.useState(null),[p,a]=i.useState(!1),[D,E]=i.useState(0),v=t?.profile_id;i.useEffect(()=>{(!t||!t.profile_id)&&navigate("/login")},[t]);const f=async()=>{if(!v){g("Utilisateur non authentifié"),h(!1);return}try{a(!0);const o=localStorage.getItem("token");if(!o){g("Session expirée. Veuillez vous reconnecter."),navigate("/login");return}const[k,V]=await Promise.all([pe.get(`/api/eleveurs/${v}/dashboard`,{headers:{Authorization:`Bearer ${o}`}}),pe.get(`/api/eleveurs/${v}/ouvriers`,{headers:{Authorization:`Bearer ${o}`}})]);l(k.data.data),s(V.data.data.ouvriers),g(null)}catch(o){console.error("Erreur lors de la récupération des données:",o),o.response?.status===403?g("Accès refusé. Vous n'avez pas les droits nécessaires pour accéder à ces données."):g("Erreur lors du chargement des données du dashboard")}finally{h(!1),a(!1)}};if(i.useEffect(()=>{f();const o=setInterval(f,5*60*1e3);return()=>clearInterval(o)},[v]),d)return e.jsx(ee,{maxWidth:"lg",sx:{mt:4,mb:4},children:e.jsx(u,{display:"flex",justifyContent:"center",alignItems:"center",minHeight:"400px",children:e.jsx(H,{size:60})})});if(j)return e.jsxs(ee,{maxWidth:"lg",sx:{mt:4,mb:4},children:[e.jsx(te,{severity:"error",sx:{mb:2},children:j}),e.jsx(M,{variant:"contained",onClick:f,children:"Réessayer"})]});const{stats:P,alertes:W,ventesRecentes:b,saisiesQuotidiennes:y,productionOeufs:C,graphiques:m}=r;return e.jsxs(ee,{maxWidth:"lg",sx:{mt:4,mb:4},children:[e.jsxs(u,{sx:{display:"flex",justifyContent:"space-between",alignItems:"center",mb:4},children:[e.jsx(c,{variant:"h4",component:"h1",gutterBottom:!0,children:"Dashboard Éleveur"}),e.jsxs(u,{sx:{display:"flex",gap:2},children:[e.jsx(Pt,{badgeContent:W.length,color:"error",children:e.jsx(B,{color:"warning",children:e.jsx(et,{})})}),e.jsx(M,{variant:"outlined",startIcon:e.jsx(De,{}),onClick:()=>{},children:"Nouvelle vente"}),e.jsx(M,{variant:"contained",startIcon:e.jsx(Zt,{}),onClick:f,disabled:p,children:p?e.jsx(H,{size:20}):"Actualiser"})]})]}),e.jsxs(n,{container:!0,spacing:3,sx:{mb:4},children:[e.jsx(n,{item:!0,xs:12,sm:6,md:2,children:e.jsx(He,{title:"Total Volailles",value:P.totalVolailles,icon:e.jsx(ye,{}),color:"#1976d2"})}),e.jsx(n,{item:!0,xs:12,sm:6,md:2,children:e.jsx(He,{title:"Poussins",value:P.totalPoussins,icon:e.jsx(ye,{}),color:"#2e7d32"})}),e.jsx(n,{item:!0,xs:12,sm:6,md:2,children:e.jsx(He,{title:"Dindes",value:P.totalDindes,icon:e.jsx(ye,{}),color:"#ed6c02"})}),e.jsx(n,{item:!0,xs:12,sm:6,md:2,children:e.jsx(He,{title:"Poulets Chair",value:P.totalPouletsChair,icon:e.jsx(ye,{}),color:"#9c27b0"})}),e.jsx(n,{item:!0,xs:12,sm:6,md:2,children:e.jsx(He,{title:"Pondeuses",value:P.totalPondeuses,icon:e.jsx(Rt,{}),color:"#d32f2f"})}),e.jsx(n,{item:!0,xs:12,sm:6,md:2,children:e.jsx(He,{title:"Valeur Cheptel",value:`${P.valeurEstimeeCheptel.toFixed(0)}€`,icon:e.jsx(De,{}),color:"#1565c0"})})]}),W&&W.length>0&&e.jsx(n,{container:!0,spacing:3,sx:{mb:4},children:e.jsx(n,{item:!0,xs:12,children:e.jsxs(_,{elevation:2,sx:{p:3},children:[e.jsxs(c,{variant:"h6",gutterBottom:!0,sx:{display:"flex",alignItems:"center"},children:[e.jsx(et,{sx:{mr:1,color:"warning.main"}}),"Alertes Actives (",W.length,")"]}),e.jsx(n,{container:!0,spacing:2,children:W.slice(0,4).map(o=>e.jsx(n,{item:!0,xs:12,sm:6,md:3,children:e.jsx(Z,{sx:{border:`2px solid ${o.priorite==="critique"?"#f44336":"#ff9800"}`,bgcolor:o.priorite==="critique"?"#ffebee":"#fff3e0"},children:e.jsxs(Y,{children:[e.jsx(c,{variant:"h6",component:"div",noWrap:!0,children:o.titre}),e.jsx(c,{variant:"body2",color:"text.secondary",children:o.message}),e.jsx(se,{label:o.priorite.toUpperCase(),color:o.priorite==="critique"?"error":"warning",size:"small",sx:{mt:1}})]})})},o.id))})]})})}),e.jsx(_,{elevation:2,sx:{mb:4},children:e.jsxs(xt,{value:D,onChange:(o,k)=>E(k),children:[e.jsx(ge,{label:"Vue d'ensemble"}),e.jsx(ge,{label:"Gestion Ouvriers"}),e.jsx(ge,{label:"Saisies Quotidiennes"}),e.jsx(ge,{label:"Multi-Activités"})]})}),D===0&&e.jsxs(n,{container:!0,spacing:3,children:[e.jsx(n,{item:!0,xs:12,md:6,children:e.jsxs(_,{elevation:2,sx:{p:3,height:"400px",overflow:"auto"},children:[e.jsxs(c,{variant:"h6",gutterBottom:!0,sx:{display:"flex",alignItems:"center"},children:[e.jsx(De,{sx:{mr:1}}),"Ventes Récentes"]}),e.jsx(le,{children:b.map(o=>e.jsxs(oe,{divider:!0,children:[e.jsx(je,{children:e.jsx(re,{sx:{bgcolor:"success.main"},children:e.jsx(De,{})})}),e.jsx(ae,{primary:`${o.quantite} ${o.espece||"unités"}`,secondary:e.jsxs(u,{children:[e.jsxs(c,{variant:"body2",color:"text.secondary",children:["Acheteur: ",o.acheteur]}),e.jsxs(c,{variant:"body2",color:"text.secondary",children:["Montant: ",o.total_amount,"€"]}),e.jsx(c,{variant:"caption",color:"text.secondary",children:new Date(o.created_at).toLocaleDateString()})]})})]},o.id))})]})}),e.jsx(n,{item:!0,xs:12,md:6,children:e.jsxs(_,{elevation:2,sx:{p:3,height:"400px",overflow:"auto"},children:[e.jsxs(c,{variant:"h6",gutterBottom:!0,sx:{display:"flex",alignItems:"center"},children:[e.jsx(Rt,{sx:{mr:1}}),"Production d'Œufs Récente"]}),e.jsx(le,{children:C.map(o=>e.jsxs(oe,{divider:!0,children:[e.jsx(je,{children:e.jsx(re,{sx:{bgcolor:"warning.main"},children:e.jsx(Rt,{})})}),e.jsx(ae,{primary:`${o.quantite_oeufs} œufs`,secondary:e.jsxs(u,{children:[e.jsxs(c,{variant:"body2",color:"text.secondary",children:["Ferme: ",o.ferme_nom||"Principale"]}),e.jsxs(c,{variant:"body2",color:"text.secondary",children:["Taux de ponte: ",o.taux_ponte,"%"]}),e.jsx(c,{variant:"caption",color:"text.secondary",children:new Date(o.date_production).toLocaleDateString()})]})})]},o.id))})]})}),e.jsx(n,{item:!0,xs:12,md:6,children:e.jsxs(_,{elevation:2,sx:{p:3,height:"400px"},children:[e.jsx(c,{variant:"h6",gutterBottom:!0,children:"Évolution des Ventes"}),e.jsx(Re,{width:"100%",height:"85%",children:e.jsxs(Ht,{data:m.evolutionVentes,children:[e.jsx(Ye,{strokeDasharray:"3 3"}),e.jsx(Qe,{dataKey:"mois",tickFormatter:o=>new Date(o).toLocaleDateString("fr-FR",{month:"short"})}),e.jsx(Je,{}),e.jsx(We,{}),e.jsx(dt,{}),e.jsx(At,{type:"monotone",dataKey:"chiffre_affaires",stroke:"#1976d2",fill:"#1976d2",fillOpacity:.3,name:"Chiffre d'affaires"})]})})]})}),e.jsx(n,{item:!0,xs:12,md:6,children:e.jsxs(_,{elevation:2,sx:{p:3,height:"400px"},children:[e.jsx(c,{variant:"h6",gutterBottom:!0,children:"Production d'Œufs (Tendances)"}),e.jsx(Re,{width:"100%",height:"85%",children:e.jsxs(Ir,{data:m.tendancesOeufs,children:[e.jsx(Ye,{strokeDasharray:"3 3"}),e.jsx(Qe,{dataKey:"jour",tickFormatter:o=>new Date(o).toLocaleDateString("fr-FR",{day:"2-digit",month:"2-digit"})}),e.jsx(Je,{}),e.jsx(We,{}),e.jsx(dt,{}),e.jsx(ts,{type:"monotone",dataKey:"production_quotidienne",stroke:"#ff9800",name:"Production quotidienne"}),e.jsx(ts,{type:"monotone",dataKey:"taux_ponte_moyen",stroke:"#4caf50",name:"Taux de ponte moyen"})]})})]})})]}),D===1&&e.jsx(n,{container:!0,spacing:3,children:e.jsx(n,{item:!0,xs:12,children:e.jsx(wa,{eleveurId:v,ouvriers:x,onRefresh:f})})}),D===2&&e.jsx(n,{container:!0,spacing:3,children:e.jsx(n,{item:!0,xs:12,children:e.jsx(Ia,{saisies:y,eleveurId:v,onRefresh:f})})}),D===3&&e.jsx(n,{container:!0,spacing:3,children:e.jsx(n,{item:!0,xs:12,children:e.jsx(Aa,{eleveurId:v})})}),e.jsx(Kt,{color:"primary","aria-label":"refresh",sx:{position:"fixed",bottom:16,right:16},onClick:f,disabled:p,children:p?e.jsx(H,{size:24}):e.jsx(Ws,{})})]})},js=["#0088FE","#00C49F","#FFBB28","#FF8042","#8884D8"],Ct=({title:t,value:r,icon:l,color:x,subtitle:s})=>e.jsx(_,{elevation:2,sx:{p:3,height:"100%",display:"flex",flexDirection:"column",background:`linear-gradient(135deg, ${x}15 0%, ${x}05 100%)`,border:`1px solid ${x}30`},children:e.jsxs(u,{sx:{display:"flex",alignItems:"center",mb:2},children:[e.jsx(re,{sx:{bgcolor:x,mr:2},children:l}),e.jsxs(u,{children:[e.jsx(c,{variant:"h4",component:"div",fontWeight:"bold",color:x,children:r}),e.jsx(c,{variant:"body2",color:"text.secondary",children:t}),s&&e.jsx(c,{variant:"caption",color:"text.secondary",children:s})]})]})}),ka=({open:t,onClose:r,type:l,onSubmit:x})=>{const[s,d]=i.useState({}),[h,j]=i.useState(!1),g=async()=>{j(!0);try{await x(s),d({}),r()}catch(a){console.error("Erreur lors de la soumission:",a)}finally{j(!1)}},p=()=>{if(l==="consultation")return e.jsxs(e.Fragment,{children:[e.jsx(A,{fullWidth:!0,label:"ID Éleveur",type:"number",value:s.eleveur_id||"",onChange:a=>d({...s,eleveur_id:parseInt(a.target.value)}),margin:"normal",required:!0}),e.jsx(A,{fullWidth:!0,label:"Date de consultation",type:"datetime-local",value:s.date_consultation||"",onChange:a=>d({...s,date_consultation:a.target.value}),margin:"normal",required:!0,InputLabelProps:{shrink:!0}}),e.jsx(A,{fullWidth:!0,label:"Motif",multiline:!0,rows:3,value:s.motif||"",onChange:a=>d({...s,motif:a.target.value}),margin:"normal",required:!0}),e.jsxs(Q,{fullWidth:!0,margin:"normal",children:[e.jsx(J,{children:"Urgence"}),e.jsxs(K,{value:s.urgence||!1,onChange:a=>d({...s,urgence:a.target.value}),children:[e.jsx(I,{value:!1,children:"Non"}),e.jsx(I,{value:!0,children:"Oui"})]})]})]});if(l==="prescription")return e.jsxs(e.Fragment,{children:[e.jsx(A,{fullWidth:!0,label:"ID Éleveur",type:"number",value:s.eleveur_id||"",onChange:a=>d({...s,eleveur_id:parseInt(a.target.value)}),margin:"normal",required:!0}),e.jsx(A,{fullWidth:!0,label:"ID Volaille",type:"number",value:s.volaille_id||"",onChange:a=>d({...s,volaille_id:parseInt(a.target.value)}),margin:"normal"}),e.jsx(A,{fullWidth:!0,label:"Médicament",value:s.medicament||"",onChange:a=>d({...s,medicament:a.target.value}),margin:"normal",required:!0}),e.jsx(A,{fullWidth:!0,label:"Dosage",value:s.dosage||"",onChange:a=>d({...s,dosage:a.target.value}),margin:"normal",required:!0}),e.jsx(A,{fullWidth:!0,label:"Durée du traitement",value:s.duree_traitement||"",onChange:a=>d({...s,duree_traitement:a.target.value}),margin:"normal",required:!0}),e.jsx(A,{fullWidth:!0,label:"Instructions",multiline:!0,rows:3,value:s.instructions||"",onChange:a=>d({...s,instructions:a.target.value}),margin:"normal"})]})};return e.jsxs(Ce,{open:t,onClose:r,maxWidth:"sm",fullWidth:!0,children:[e.jsx(Se,{children:l==="consultation"?"Programmer une consultation":"Créer une prescription"}),e.jsx(we,{children:p()}),e.jsxs(Ie,{children:[e.jsx(M,{onClick:r,children:"Annuler"}),e.jsx(M,{onClick:g,variant:"contained",disabled:h,children:h?e.jsx(H,{size:20}):"Créer"})]})]})},Pa=()=>{const{user:t}=fe(),[r,l]=i.useState(null),[x,s]=i.useState([]),[d,h]=i.useState(!0),[j,g]=i.useState(null),[p,a]=i.useState({open:!1,type:null}),[D,E]=i.useState(!1),v=async()=>{try{E(!0);const o=localStorage.getItem("token"),[k,V]=await Promise.all([pe.get("/api/veterinaire/dashboard",{headers:{Authorization:`Bearer ${o}`}}),pe.get("/api/veterinaire/notifications",{headers:{Authorization:`Bearer ${o}`}})]);l(k.data.data),s(V.data.data.notifications),g(null)}catch(o){console.error("Erreur lors de la récupération des données:",o),g("Erreur lors du chargement des données du dashboard")}finally{h(!1),E(!1)}},f=async(o,k)=>{try{const V=localStorage.getItem("token"),U=o==="consultation"?"/api/veterinaire/consultations/quick":"/api/veterinaire/prescriptions/quick";await pe.post(U,k,{headers:{Authorization:`Bearer ${V}`}}),v()}catch(V){throw console.error("Erreur lors de l'action rapide:",V),V}};if(i.useEffect(()=>{v();const o=setInterval(v,5*60*1e3);return()=>clearInterval(o)},[]),d)return e.jsx(ee,{maxWidth:"lg",sx:{mt:4,mb:4},children:e.jsx(u,{display:"flex",justifyContent:"center",alignItems:"center",minHeight:"400px",children:e.jsx(H,{size:60})})});if(j)return e.jsxs(ee,{maxWidth:"lg",sx:{mt:4,mb:4},children:[e.jsx(te,{severity:"error",sx:{mb:2},children:j}),e.jsx(M,{variant:"contained",onClick:v,children:"Réessayer"})]});const{stats:P,consultationsAVenir:W,prescriptionsRecentes:b,consultationsHistorique:y,graphiques:C,alertesSante:m}=r;return e.jsxs(ee,{maxWidth:"lg",sx:{mt:4,mb:4},children:[e.jsxs(u,{sx:{display:"flex",justifyContent:"space-between",alignItems:"center",mb:4},children:[e.jsx(c,{variant:"h4",component:"h1",gutterBottom:!0,children:"Dashboard Vétérinaire"}),e.jsxs(u,{sx:{display:"flex",gap:2},children:[e.jsx(Pt,{badgeContent:x.length,color:"error",children:e.jsx(B,{color:"primary",children:e.jsx(Le,{})})}),e.jsx(M,{variant:"outlined",startIcon:e.jsx(as,{}),onClick:()=>a({open:!0,type:"consultation"}),children:"Nouvelle consultation"}),e.jsx(M,{variant:"contained",startIcon:e.jsx(Ze,{}),onClick:()=>a({open:!0,type:"prescription"}),children:"Nouvelle prescription"})]})]}),e.jsxs(n,{container:!0,spacing:3,sx:{mb:4},children:[e.jsx(n,{item:!0,xs:12,sm:6,md:3,children:e.jsx(Ct,{title:"Total Prescriptions",value:P.totalPrescriptions,subtitle:`+${P.prescriptionsMois} ce mois`,icon:e.jsx(Ze,{}),color:"#1976d2"})}),e.jsx(n,{item:!0,xs:12,sm:6,md:3,children:e.jsx(Ct,{title:"Total Consultations",value:P.totalConsultations,subtitle:`+${P.consultationsMois} ce mois`,icon:e.jsx(is,{}),color:"#2e7d32"})}),e.jsx(n,{item:!0,xs:12,sm:6,md:3,children:e.jsx(Ct,{title:"Éleveurs Suivis",value:P.eleveursSuivis,subtitle:"Clients actifs",icon:e.jsx(zs,{}),color:"#ed6c02"})}),e.jsx(n,{item:!0,xs:12,sm:6,md:3,children:e.jsx(Ct,{title:"Prochaine Semaine",value:P.consultationsSemaineProchaine,subtitle:"Consultations programmées",icon:e.jsx(mn,{}),color:"#9c27b0"})})]}),m&&m.length>0&&e.jsx(n,{container:!0,spacing:3,sx:{mb:4},children:e.jsx(n,{item:!0,xs:12,children:e.jsxs(_,{elevation:2,sx:{p:3},children:[e.jsxs(c,{variant:"h6",gutterBottom:!0,sx:{display:"flex",alignItems:"center"},children:[e.jsx(et,{sx:{mr:1,color:"warning.main"}}),"Alertes Santé Récentes"]}),e.jsx(le,{children:m.slice(0,5).map(o=>e.jsxs(oe,{divider:!0,children:[e.jsx(je,{children:e.jsx(re,{sx:{bgcolor:o.priorite==="critique"?"error.main":"warning.main"},children:e.jsx(et,{})})}),e.jsx(ae,{primary:o.titre,secondary:e.jsxs(u,{children:[e.jsx(c,{variant:"body2",color:"text.secondary",children:o.message}),e.jsxs(c,{variant:"caption",color:"text.secondary",children:["Éleveur: ",o.eleveur_nom," ",o.eleveur_prenom," - ",new Date(o.date_declenchement).toLocaleDateString()]})]})}),e.jsx(se,{label:o.priorite,color:o.priorite==="critique"?"error":"warning",size:"small"})]},o.id))})]})})}),e.jsxs(n,{container:!0,spacing:3,children:[e.jsx(n,{item:!0,xs:12,md:6,children:e.jsxs(_,{elevation:2,sx:{p:3,height:"400px",overflow:"auto"},children:[e.jsxs(c,{variant:"h6",gutterBottom:!0,sx:{display:"flex",alignItems:"center"},children:[e.jsx(as,{sx:{mr:1}}),"Consultations à Venir"]}),e.jsx(le,{children:W.map(o=>e.jsxs(oe,{divider:!0,children:[e.jsx(je,{children:e.jsx(re,{sx:{bgcolor:o.urgence?"error.main":"primary.main"},children:e.jsx(is,{})})}),e.jsx(ae,{primary:`${o.eleveur_nom} ${o.eleveur_prenom}`,secondary:e.jsxs(u,{children:[e.jsx(c,{variant:"body2",color:"text.secondary",children:o.motif}),e.jsx(c,{variant:"caption",color:"text.secondary",children:new Date(o.date_consultation).toLocaleString()}),o.urgence&&e.jsx(se,{label:"URGENT",color:"error",size:"small",sx:{ml:1}})]})}),e.jsx(B,{size:"small",children:e.jsx(rt,{})})]},o.id))})]})}),e.jsx(n,{item:!0,xs:12,md:6,children:e.jsxs(_,{elevation:2,sx:{p:3,height:"400px",overflow:"auto"},children:[e.jsxs(c,{variant:"h6",gutterBottom:!0,sx:{display:"flex",alignItems:"center"},children:[e.jsx(Ze,{sx:{mr:1}}),"Prescriptions Récentes"]}),e.jsx(le,{children:b.map(o=>e.jsxs(oe,{divider:!0,children:[e.jsx(je,{children:e.jsx(re,{sx:{bgcolor:"success.main"},children:e.jsx(Ze,{})})}),e.jsx(ae,{primary:o.medicament,secondary:e.jsxs(u,{children:[e.jsxs(c,{variant:"body2",color:"text.secondary",children:[o.eleveur_nom," ",o.eleveur_prenom]}),e.jsxs(c,{variant:"body2",color:"text.secondary",children:["Dosage: ",o.dosage]}),e.jsx(c,{variant:"caption",color:"text.secondary",children:new Date(o.created_at).toLocaleDateString()})]})}),e.jsx(se,{label:o.status,color:o.status==="active"?"success":"default",size:"small"})]},o.id))})]})}),e.jsx(n,{item:!0,xs:12,md:6,children:e.jsxs(_,{elevation:2,sx:{p:3,height:"400px"},children:[e.jsx(c,{variant:"h6",gutterBottom:!0,children:"Consultations par Mois"}),e.jsx(Re,{width:"100%",height:"85%",children:e.jsxs(Ht,{data:C.consultationsParMois,children:[e.jsx(Ye,{strokeDasharray:"3 3"}),e.jsx(Qe,{dataKey:"mois",tickFormatter:o=>new Date(o).toLocaleDateString("fr-FR",{month:"short"})}),e.jsx(Je,{}),e.jsx(We,{labelFormatter:o=>new Date(o).toLocaleDateString("fr-FR",{month:"long",year:"numeric"})}),e.jsx(dt,{}),e.jsx(At,{type:"monotone",dataKey:"nombre_consultations",stroke:"#1976d2",fill:"#1976d2",fillOpacity:.3,name:"Consultations"}),e.jsx(At,{type:"monotone",dataKey:"consultations_terminees",stroke:"#2e7d32",fill:"#2e7d32",fillOpacity:.3,name:"Terminées"})]})})]})}),e.jsx(n,{item:!0,xs:12,md:6,children:e.jsxs(_,{elevation:2,sx:{p:3,height:"400px"},children:[e.jsx(c,{variant:"h6",gutterBottom:!0,children:"Types de Consultations"}),e.jsx(Re,{width:"100%",height:"85%",children:e.jsxs(ys,{children:[e.jsx(Cs,{data:C.typesConsultations,cx:"50%",cy:"50%",labelLine:!1,label:({motif:o,pourcentage:k})=>`${o}: ${k}%`,outerRadius:80,fill:"#8884d8",dataKey:"nombre",children:C.typesConsultations.map((o,k)=>e.jsx(Ss,{fill:js[k%js.length]},`cell-${k}`))}),e.jsx(We,{})]})})]})})]}),e.jsx(Kt,{color:"primary","aria-label":"refresh",sx:{position:"fixed",bottom:16,right:16},onClick:v,disabled:D,children:D?e.jsx(H,{size:24}):e.jsx(Fe,{})}),e.jsx(ka,{open:p.open,type:p.type,onClose:()=>a({open:!1,type:null}),onSubmit:o=>f(p.type,o)})]})},St=({title:t,value:r,icon:l,color:x,subtitle:s,trend:d})=>e.jsx(_,{elevation:2,sx:{p:3,height:"100%",display:"flex",flexDirection:"column",background:`linear-gradient(135deg, ${x}15 0%, ${x}05 100%)`,border:`1px solid ${x}30`},children:e.jsxs(u,{sx:{display:"flex",alignItems:"center",mb:2},children:[e.jsx(re,{sx:{bgcolor:x,mr:2},children:l}),e.jsxs(u,{sx:{flex:1},children:[e.jsx(c,{variant:"h4",component:"div",fontWeight:"bold",color:x,children:typeof r=="number"&&r>1e3?`${(r/1e3).toFixed(1)}k`:r}),e.jsx(c,{variant:"body2",color:"text.secondary",children:t}),s&&e.jsx(c,{variant:"caption",color:"text.secondary",children:s})]}),d&&e.jsx(u,{sx:{textAlign:"right"},children:e.jsxs(c,{variant:"caption",color:d>0?"success.main":"error.main",sx:{display:"flex",alignItems:"center"},children:[e.jsx(Fe,{sx:{fontSize:16,mr:.5}}),d>0?"+":"",d,"%"]})})]})}),Ea=({open:t,onClose:r,type:l,onSubmit:x})=>{const[s,d]=i.useState({}),[h,j]=i.useState(!1),g=async()=>{j(!0);try{await x(s),d({}),r()}catch(a){console.error("Erreur lors de la soumission:",a)}finally{j(!1)}},p=()=>{if(l==="product")return e.jsxs(e.Fragment,{children:[e.jsx(A,{fullWidth:!0,label:"Nom du produit",value:s.name||"",onChange:a=>d({...s,name:a.target.value}),margin:"normal",required:!0}),e.jsx(A,{fullWidth:!0,label:"Description",multiline:!0,rows:3,value:s.description||"",onChange:a=>d({...s,description:a.target.value}),margin:"normal"}),e.jsx(A,{fullWidth:!0,label:"Prix",type:"number",value:s.price||"",onChange:a=>d({...s,price:parseFloat(a.target.value)}),margin:"normal",required:!0}),e.jsx(A,{fullWidth:!0,label:"Quantité en stock",type:"number",value:s.stock_quantity||"",onChange:a=>d({...s,stock_quantity:parseInt(a.target.value)}),margin:"normal",required:!0}),e.jsx(A,{fullWidth:!0,label:"Catégorie",value:s.category||"",onChange:a=>d({...s,category:a.target.value}),margin:"normal",required:!0}),e.jsx(A,{fullWidth:!0,label:"Seuil d'alerte stock",type:"number",value:s.stock_alert_threshold||10,onChange:a=>d({...s,stock_alert_threshold:parseInt(a.target.value)}),margin:"normal"})]});if(l==="stock")return e.jsxs(e.Fragment,{children:[e.jsx(A,{fullWidth:!0,label:"Quantité",type:"number",value:s.stock_quantity||"",onChange:a=>d({...s,stock_quantity:parseInt(a.target.value)}),margin:"normal",required:!0}),e.jsxs(Q,{fullWidth:!0,margin:"normal",children:[e.jsx(J,{children:"Opération"}),e.jsxs(K,{value:s.operation||"set",onChange:a=>d({...s,operation:a.target.value}),children:[e.jsx(I,{value:"set",children:"Définir"}),e.jsx(I,{value:"add",children:"Ajouter"}),e.jsx(I,{value:"subtract",children:"Soustraire"})]})]})]})};return e.jsxs(Ce,{open:t,onClose:r,maxWidth:"sm",fullWidth:!0,children:[e.jsx(Se,{children:l==="product"?"Ajouter un produit":"Mettre à jour le stock"}),e.jsx(we,{children:p()}),e.jsxs(Ie,{children:[e.jsx(M,{onClick:r,children:"Annuler"}),e.jsx(M,{onClick:g,variant:"contained",disabled:h,children:h?e.jsx(H,{size:20}):"Enregistrer"})]})]})},Ta=({recommendations:t})=>{const[r,l]=i.useState(0),x=(s,d)=>{l(d)};return e.jsxs(_,{elevation:2,sx:{p:3,height:"400px"},children:[e.jsxs(c,{variant:"h6",gutterBottom:!0,sx:{display:"flex",alignItems:"center"},children:[e.jsx(Jt,{sx:{mr:1,color:"primary.main"}}),"Recommandations IA"]}),e.jsxs(xt,{value:r,onChange:x,sx:{mb:2},children:[e.jsx(ge,{label:"Prix"}),e.jsx(ge,{label:"Stock"}),e.jsx(ge,{label:"Tendances"})]}),e.jsxs(u,{sx:{height:"300px",overflow:"auto"},children:[r===0&&e.jsx(le,{children:t.price_optimization?.map((s,d)=>e.jsx(oe,{divider:!0,children:e.jsx(ae,{primary:s.product_name,secondary:e.jsxs(u,{children:[e.jsxs(c,{variant:"body2",children:["Prix actuel: ",s.current_price,"€ → Suggéré: ",s.suggested_price,"€"]}),e.jsx(c,{variant:"caption",color:"text.secondary",children:s.reason})]})})},d))}),r===1&&e.jsx(le,{children:t.inventory_suggestions?.map((s,d)=>e.jsxs(oe,{divider:!0,children:[e.jsx(je,{children:e.jsx(re,{sx:{bgcolor:s.action==="restock"?"warning.main":"info.main"},children:e.jsx(jn,{})})}),e.jsx(ae,{primary:s.product_name,secondary:e.jsxs(u,{children:[e.jsxs(c,{variant:"body2",children:["Action: ",s.action==="restock"?"Réapprovisionner":"Réduire le stock"]}),e.jsx(c,{variant:"caption",color:"text.secondary",children:s.reason})]})})]},d))}),r===2&&e.jsx(le,{children:t.market_trends?.map((s,d)=>e.jsxs(oe,{divider:!0,children:[e.jsx(je,{children:e.jsx(re,{sx:{bgcolor:"success.main"},children:e.jsx(Fe,{})})}),e.jsx(ae,{primary:s.trend,secondary:e.jsxs(c,{variant:"caption",color:"text.secondary",children:["Confiance: ",(s.confidence*100).toFixed(0),"%"]})})]},d))})]})]})},Ma=()=>{const{user:t}=fe(),[r,l]=i.useState(null),[x,s]=i.useState({}),[d,h]=i.useState(!0),[j,g]=i.useState(null),[p,a]=i.useState({open:!1,type:null,productId:null}),[D,E]=i.useState(!1),v=async()=>{try{E(!0);const m=localStorage.getItem("token"),[o,k]=await Promise.all([pe.get("/api/marchand/dashboard",{headers:{Authorization:`Bearer ${m}`}}),pe.get("/api/marchand/ai/recommendations",{headers:{Authorization:`Bearer ${m}`}})]);l(o.data.data),s(k.data.data.recommendations),g(null)}catch(m){console.error("Erreur lors de la récupération des données:",m),g("Erreur lors du chargement des données du dashboard")}finally{h(!1),E(!1)}},f=async(m,o)=>{try{const k=localStorage.getItem("token");m==="product"?await pe.post("/api/marchand/products/quick",o,{headers:{Authorization:`Bearer ${k}`}}):m==="stock"&&await pe.patch(`/api/marchand/products/${p.productId}/stock`,o,{headers:{Authorization:`Bearer ${k}`}}),v()}catch(k){throw console.error("Erreur lors de l'action rapide:",k),k}};if(i.useEffect(()=>{v();const m=setInterval(v,5*60*1e3);return()=>clearInterval(m)},[]),d)return e.jsx(ee,{maxWidth:"lg",sx:{mt:4,mb:4},children:e.jsx(u,{display:"flex",justifyContent:"center",alignItems:"center",minHeight:"400px",children:e.jsx(H,{size:60})})});if(j)return e.jsxs(ee,{maxWidth:"lg",sx:{mt:4,mb:4},children:[e.jsx(te,{severity:"error",sx:{mb:2},children:j}),e.jsx(M,{variant:"contained",onClick:v,children:"Réessayer"})]});const{stats:P,commandesRecentes:W,produitsPopulaires:b,alertesStock:y,graphiques:C}=r;return e.jsxs(ee,{maxWidth:"lg",sx:{mt:4,mb:4},children:[e.jsxs(u,{sx:{display:"flex",justifyContent:"space-between",alignItems:"center",mb:4},children:[e.jsx(c,{variant:"h4",component:"h1",gutterBottom:!0,children:"Dashboard Marchand"}),e.jsxs(u,{sx:{display:"flex",gap:2},children:[e.jsx(Pt,{badgeContent:y.length,color:"error",children:e.jsx(B,{color:"warning",children:e.jsx(et,{})})}),e.jsx(M,{variant:"outlined",startIcon:e.jsx(tt,{}),onClick:()=>a({open:!0,type:"product"}),children:"Nouveau produit"}),e.jsx(M,{variant:"contained",startIcon:e.jsx(pn,{}),onClick:v,disabled:D,children:D?e.jsx(H,{size:20}):"Actualiser"})]})]}),e.jsxs(n,{container:!0,spacing:3,sx:{mb:4},children:[e.jsx(n,{item:!0,xs:12,sm:6,md:3,children:e.jsx(St,{title:"Total Produits",value:P.totalProduits,subtitle:`+${P.produitsAjoutesMois} ce mois`,icon:e.jsx(ot,{}),color:"#1976d2"})}),e.jsx(n,{item:!0,xs:12,sm:6,md:3,children:e.jsx(St,{title:"Total Commandes",value:P.totalCommandes,subtitle:`+${P.commandesMois} ce mois`,icon:e.jsx(Wt,{}),color:"#2e7d32"})}),e.jsx(n,{item:!0,xs:12,sm:6,md:3,children:e.jsx(St,{title:"Chiffre d'Affaires",value:`${P.chiffreAffairesTotal.toFixed(0)}€`,subtitle:`+${P.chiffreAffairesMois.toFixed(0)}€ ce mois`,icon:e.jsx(De,{}),color:"#ed6c02"})}),e.jsx(n,{item:!0,xs:12,sm:6,md:3,children:e.jsx(St,{title:"Note Moyenne",value:P.noteMoyenne.toFixed(1),subtitle:`${P.commandesEnAttente} en attente`,icon:e.jsx(gn,{}),color:"#9c27b0"})})]}),y&&y.length>0&&e.jsx(n,{container:!0,spacing:3,sx:{mb:4},children:e.jsx(n,{item:!0,xs:12,children:e.jsxs(_,{elevation:2,sx:{p:3},children:[e.jsxs(c,{variant:"h6",gutterBottom:!0,sx:{display:"flex",alignItems:"center"},children:[e.jsx(et,{sx:{mr:1,color:"warning.main"}}),"Alertes de Stock"]}),e.jsx(n,{container:!0,spacing:2,children:y.slice(0,4).map(m=>e.jsx(n,{item:!0,xs:12,sm:6,md:3,children:e.jsx(Z,{sx:{border:`2px solid ${m.niveau_alerte==="rupture"?"#f44336":"#ff9800"}`,bgcolor:m.niveau_alerte==="rupture"?"#ffebee":"#fff3e0"},children:e.jsxs(Y,{children:[e.jsx(c,{variant:"h6",component:"div",noWrap:!0,children:m.name}),e.jsxs(c,{variant:"body2",color:"text.secondary",children:["Stock: ",m.stock_quantity," / ",m.stock_alert_threshold]}),e.jsx(se,{label:m.niveau_alerte.toUpperCase(),color:m.niveau_alerte==="rupture"?"error":"warning",size:"small",sx:{mt:1}}),e.jsx(M,{size:"small",variant:"outlined",sx:{mt:1,ml:1},onClick:()=>a({open:!0,type:"stock",productId:m.id}),children:"Réapprovisionner"})]})})},m.id))})]})})}),e.jsxs(n,{container:!0,spacing:3,children:[e.jsx(n,{item:!0,xs:12,md:6,children:e.jsxs(_,{elevation:2,sx:{p:3,height:"400px",overflow:"auto"},children:[e.jsxs(c,{variant:"h6",gutterBottom:!0,sx:{display:"flex",alignItems:"center"},children:[e.jsx(Wt,{sx:{mr:1}}),"Commandes Récentes"]}),e.jsx(le,{children:W.map(m=>e.jsxs(oe,{divider:!0,children:[e.jsx(je,{children:e.jsx(re,{sx:{bgcolor:"primary.main"},children:e.jsx(Wt,{})})}),e.jsx(ae,{primary:`Commande #${m.order_number||m.id}`,secondary:e.jsxs(u,{children:[e.jsxs(c,{variant:"body2",color:"text.secondary",children:["Client: ",m.client_nom||"Client anonyme"]}),e.jsxs(c,{variant:"body2",color:"text.secondary",children:["Montant: ",m.total_amount,"€ - ",m.nombre_articles," articles"]}),e.jsx(c,{variant:"caption",color:"text.secondary",children:new Date(m.created_at).toLocaleDateString()})]})}),e.jsx(se,{label:m.status,color:m.status==="delivered"?"success":"primary",size:"small"})]},m.id))})]})}),e.jsx(n,{item:!0,xs:12,md:6,children:e.jsxs(_,{elevation:2,sx:{p:3,height:"400px",overflow:"auto"},children:[e.jsxs(c,{variant:"h6",gutterBottom:!0,sx:{display:"flex",alignItems:"center"},children:[e.jsx(Fe,{sx:{mr:1}}),"Produits Populaires"]}),e.jsx(le,{children:b.map(m=>e.jsxs(oe,{divider:!0,children:[e.jsx(je,{children:e.jsx(re,{sx:{bgcolor:"success.main"},children:e.jsx(ot,{})})}),e.jsx(ae,{primary:m.name,secondary:e.jsxs(u,{children:[e.jsxs(c,{variant:"body2",color:"text.secondary",children:["Vendu: ",m.quantite_vendue," unités"]}),e.jsxs(c,{variant:"body2",color:"text.secondary",children:["Revenus: ",m.revenus_generes,"€"]}),e.jsxs(c,{variant:"caption",color:"text.secondary",children:[m.nombre_commandes," commandes"]})]})}),e.jsx(B,{size:"small",children:e.jsx(rt,{})})]},m.id))})]})}),e.jsx(n,{item:!0,xs:12,md:6,children:e.jsxs(_,{elevation:2,sx:{p:3,height:"400px"},children:[e.jsx(c,{variant:"h6",gutterBottom:!0,children:"Évolution des Ventes (30 jours)"}),e.jsx(Re,{width:"100%",height:"85%",children:e.jsxs(Ht,{data:C.ventesParJour,children:[e.jsx(Ye,{strokeDasharray:"3 3"}),e.jsx(Qe,{dataKey:"jour",tickFormatter:m=>new Date(m).toLocaleDateString("fr-FR",{day:"2-digit",month:"2-digit"})}),e.jsx(Je,{}),e.jsx(We,{labelFormatter:m=>new Date(m).toLocaleDateString("fr-FR"),formatter:(m,o)=>[o==="chiffre_affaires"?`${m}€`:m,o==="chiffre_affaires"?"Chiffre d'affaires":"Commandes"]}),e.jsx(dt,{}),e.jsx(At,{type:"monotone",dataKey:"chiffre_affaires",stroke:"#1976d2",fill:"#1976d2",fillOpacity:.3,name:"Chiffre d'affaires"}),e.jsx(Ut,{dataKey:"nombre_commandes",fill:"#2e7d32",name:"Nombre de commandes"})]})})]})}),e.jsx(n,{item:!0,xs:12,md:6,children:e.jsx(Ta,{recommendations:x})})]}),e.jsx(Kt,{color:"primary","aria-label":"refresh",sx:{position:"fixed",bottom:16,right:16},onClick:v,disabled:D,children:D?e.jsx(H,{size:24}):e.jsx(Ws,{})}),e.jsx(Ea,{open:p.open,type:p.type,onClose:()=>a({open:!1,type:null,productId:null}),onSubmit:m=>f(p.type,m)})]})},fs=()=>{const{role:t}=Ar(),{loginAs:r}=fe();ke();const[l,x]=i.useState([]),[s,d]=i.useState(!0),[h,j]=i.useState(null),[g,p]=i.useState(0),[a,D]=i.useState(10),[E,v]=i.useState(0),[f,P]=i.useState(!1),[W,b]=i.useState(null),[y,C]=i.useState({username:"",email:"",first_name:"",last_name:"",role:"eleveur",phone:"",address:"",password:""}),[m,o]=i.useState({totalUsers:0,totalAdmins:0,totalEleveurs:0,totalVeterinaires:0,totalMarchands:0}),V={eleveurs:"eleveur",veterinaires:"veterinaire",marchands:"marchand"}[t]||t,U=async()=>{try{d(!0);let R=`/admin/users?page=${g+1}&limit=${a}`;V&&(R+=`&role=${V}`);const w=await N.get(R);w.data&&Array.isArray(w.data.users)?(x(w.data.users),v(w.data.total||0),j(null)):(console.error("Format de réponse invalide:",w.data),x([]),v(0),j("Format de données invalide reçu du serveur"))}catch(R){console.error("Erreur lors du chargement des utilisateurs:",R),console.error("Stack trace:",R.stack),x([]),v(0),j(`Erreur lors du chargement des utilisateurs: ${R.message||"Erreur inconnue"}`)}finally{d(!1)}},$=async()=>{try{const R=await N.get("/admin/stats");o({totalUsers:R.data.totalUsers||0,totalAdmins:R.data.totalAdmins||0,totalEleveurs:R.data.totalEleveurs||0,totalVeterinaires:R.data.totalVeterinaires||0,totalMarchands:R.data.totalMarchands||0})}catch(R){console.error("Erreur lors du chargement des statistiques:",R)}};i.useEffect(()=>{U(),$()},[g,a]);const G=(R,w)=>{p(w)},z=R=>{D(parseInt(R.target.value,10)),p(0)},O=(R=null)=>{if(R){b(R);const w=typeof R.role=="object"&&R.role!==null?R.role.name:R.role;C({username:R.username||"",email:R.email||"",first_name:R.first_name||"",last_name:R.last_name||"",role:w||"eleveur",phone:R.phone||"",address:R.address||"",password:""})}else b(null),C({username:"",email:"",first_name:"",last_name:"",role:"eleveur",phone:"",address:"",password:""});P(!0)},L=()=>{P(!1),b(null)},T=R=>{const{name:w,value:me}=R.target;C(nt=>({...nt,[w]:me}))},q=async()=>{try{W?await N.put(`/admin/users/${W.id}`,y):await N.post("/admin/users",y),L(),U(),$()}catch(R){console.error("Erreur lors de la sauvegarde:",R),j("Erreur lors de la sauvegarde de l'utilisateur")}},X=async R=>{if(window.confirm("Êtes-vous sûr de vouloir supprimer cet utilisateur ?"))try{await N.delete(`/admin/users/${R}`),U(),$()}catch(w){console.error("Erreur lors de la suppression:",w),j("Erreur lors de la suppression de l'utilisateur")}},xe=async R=>{try{const w=await N.post(`/admin/login-as-user/${R}`);w.data&&w.data.token&&r(w.data.token,w.data.user,w.data.impersonationStart,w.data.expiresIn)}catch(w){console.error("Erreur lors de la connexion en tant qu'utilisateur:",w),j("Erreur lors de la connexion en tant qu'utilisateur")}},$e=R=>{const w=typeof R=="object"&&R!==null?R.name:R;return{admin:"error",eleveur:"primary",veterinaire:"success",marchand:"warning"}[w]||"default"},Pe=R=>{const w=typeof R=="object"&&R!==null?R.name:R;return{admin:"Administrateur",eleveur:"Éleveur",veterinaire:"Vétérinaire",marchand:"Marchand"}[w]||w};return s&&l.length===0?e.jsx(u,{sx:{display:"flex",justifyContent:"center",alignItems:"center",height:400},children:e.jsx(c,{children:"Chargement..."})}):e.jsxs(u,{sx:{p:3},children:[e.jsx(c,{variant:"h4",gutterBottom:!0,children:"Gestion des Utilisateurs"}),h&&e.jsx(te,{severity:"error",sx:{mb:2},children:h}),e.jsxs(n,{container:!0,spacing:3,sx:{mb:3},children:[e.jsx(n,{item:!0,xs:12,sm:6,md:2.4,children:e.jsx(Z,{children:e.jsxs(Y,{children:[e.jsx(c,{color:"textSecondary",gutterBottom:!0,children:"Total Utilisateurs"}),e.jsx(c,{variant:"h4",children:m.totalUsers})]})})}),e.jsx(n,{item:!0,xs:12,sm:6,md:2.4,children:e.jsx(Z,{children:e.jsxs(Y,{children:[e.jsx(c,{color:"textSecondary",gutterBottom:!0,children:"Administrateurs"}),e.jsx(c,{variant:"h4",color:"error",children:m.totalAdmins})]})})}),e.jsx(n,{item:!0,xs:12,sm:6,md:2.4,children:e.jsx(Z,{children:e.jsxs(Y,{children:[e.jsx(c,{color:"textSecondary",gutterBottom:!0,children:"Éleveurs"}),e.jsx(c,{variant:"h4",color:"primary",children:m.totalEleveurs})]})})}),e.jsx(n,{item:!0,xs:12,sm:6,md:2.4,children:e.jsx(Z,{children:e.jsxs(Y,{children:[e.jsx(c,{color:"textSecondary",gutterBottom:!0,children:"Vétérinaires"}),e.jsx(c,{variant:"h4",color:"success.main",children:m.totalVeterinaires})]})})}),e.jsx(n,{item:!0,xs:12,sm:6,md:2.4,children:e.jsx(Z,{children:e.jsxs(Y,{children:[e.jsx(c,{color:"textSecondary",gutterBottom:!0,children:"Marchands"}),e.jsx(c,{variant:"h4",color:"warning.main",children:m.totalMarchands})]})})})]}),e.jsxs(u,{sx:{display:"flex",justifyContent:"space-between",alignItems:"center",mb:2},children:[e.jsxs(c,{variant:"h6",children:["Liste des Utilisateurs (",E,")"]}),e.jsx(M,{variant:"contained",startIcon:e.jsx(tt,{}),onClick:()=>O(),children:"Ajouter un utilisateur"})]}),e.jsxs(ze,{component:_,children:[e.jsxs(Ne,{children:[e.jsx(_e,{children:e.jsxs(ie,{children:[e.jsx(S,{children:"Avatar"}),e.jsx(S,{children:"Nom d'utilisateur"}),e.jsx(S,{children:"Nom complet"}),e.jsx(S,{children:"Email"}),e.jsx(S,{children:"Rôle"}),e.jsx(S,{children:"Téléphone"}),e.jsx(S,{children:"Actions"})]})}),e.jsx(Be,{children:l.map(R=>e.jsxs(ie,{children:[e.jsx(S,{children:e.jsx(re,{sx:{bgcolor:"primary.main"},children:R.first_name?R.first_name[0]:R.username?R.username[0]:"U"})}),e.jsx(S,{children:R.username}),e.jsx(S,{children:R.first_name&&R.last_name?`${R.first_name} ${R.last_name}`:"-"}),e.jsx(S,{children:R.email}),e.jsx(S,{children:e.jsx(se,{label:Pe(R.role),color:$e(R.role),size:"small"})}),e.jsx(S,{children:R.phone||"-"}),e.jsxs(S,{children:[e.jsx(de,{title:"Modifier",children:e.jsx(B,{size:"small",onClick:()=>O(R),children:e.jsx(Oe,{})})}),e.jsx(de,{title:"Supprimer",children:e.jsx(B,{size:"small",onClick:()=>X(R.id),color:"error",children:e.jsx(st,{})})}),(typeof R.role=="object"&&R.role!==null?R.role.name:R.role)!=="admin"&&e.jsx(de,{title:"Se connecter en tant que cet utilisateur",children:e.jsx(B,{size:"small",onClick:()=>xe(R.id),color:"primary",children:e.jsx(be,{})})})]})]},R.id))})]}),e.jsx(fn,{rowsPerPageOptions:[5,10,25],component:"div",count:E,rowsPerPage:a,page:g,onPageChange:G,onRowsPerPageChange:z})]}),e.jsxs(Ce,{open:f,onClose:L,maxWidth:"md",fullWidth:!0,children:[e.jsx(Se,{children:W?"Modifier l'utilisateur":"Ajouter un utilisateur"}),e.jsxs(we,{children:[e.jsx(vn,{children:W?"Modifier les informations de l'utilisateur existant.":"Créer un nouvel utilisateur en remplissant les détails ci-dessous."}),e.jsxs(n,{container:!0,spacing:2,sx:{mt:1},children:[e.jsx(n,{item:!0,xs:12,sm:6,children:e.jsx(A,{name:"username",label:"Nom d'utilisateur",value:y.username,onChange:T,fullWidth:!0,required:!0})}),e.jsx(n,{item:!0,xs:12,sm:6,children:e.jsx(A,{name:"email",label:"Email",type:"email",value:y.email,onChange:T,fullWidth:!0,required:!0})}),e.jsx(n,{item:!0,xs:12,sm:6,children:e.jsx(A,{name:"password",label:W?"Mot de passe (laisser vide pour ne pas changer)":"Mot de passe",type:"password",value:y.password,onChange:T,fullWidth:!0,required:!W,helperText:W?"Laissez vide pour conserver le mot de passe actuel":""})}),e.jsx(n,{item:!0,xs:12,sm:6,children:e.jsx(A,{name:"first_name",label:"Prénom",value:y.first_name,onChange:T,fullWidth:!0})}),e.jsx(n,{item:!0,xs:12,sm:6,children:e.jsx(A,{name:"last_name",label:"Nom",value:y.last_name,onChange:T,fullWidth:!0})}),e.jsx(n,{item:!0,xs:12,sm:6,children:e.jsxs(Q,{fullWidth:!0,children:[e.jsx(J,{children:"Rôle"}),e.jsxs(K,{name:"role",value:y.role,onChange:T,label:"Rôle",children:[e.jsx(I,{value:"eleveur",children:"Éleveur"}),e.jsx(I,{value:"veterinaire",children:"Vétérinaire"}),e.jsx(I,{value:"marchand",children:"Marchand"}),e.jsx(I,{value:"admin",children:"Administrateur"})]})]})}),e.jsx(n,{item:!0,xs:12,sm:6,children:e.jsx(A,{name:"phone",label:"Téléphone",value:y.phone,onChange:T,fullWidth:!0})}),e.jsx(n,{item:!0,xs:12,children:e.jsx(A,{name:"address",label:"Adresse",value:y.address,onChange:T,fullWidth:!0,multiline:!0,rows:2})})]})]}),e.jsxs(Ie,{children:[e.jsx(M,{onClick:L,children:"Annuler"}),e.jsx(M,{onClick:q,variant:"contained",children:W?"Modifier":"Ajouter"})]})]})]})},wt={getPosts:async(t=1,r=10,l=null)=>{try{const x=new URLSearchParams({page:t.toString(),limit:r.toString()});return l&&x.append("status",l),(await N.get(`/blog/posts?${x}`)).data}catch(x){throw new Error(x.response?.data?.message||"Erreur lors de la récupération des articles")}},getPostById:async t=>{try{return(await N.get(`/blog/posts/${t}`)).data}catch(r){throw new Error(r.response?.data?.message||"Erreur lors de la récupération de l'article")}},createPost:async t=>{try{return(await N.post("/blog/posts",t)).data}catch(r){throw new Error(r.response?.data?.message||"Erreur lors de la création de l'article")}},updatePost:async(t,r)=>{try{return(await N.put(`/blog/posts/${t}`,r)).data}catch(l){throw new Error(l.response?.data?.message||"Erreur lors de la mise à jour de l'article")}},deletePost:async t=>{try{return(await N.delete(`/blog/posts/${t}`)).data}catch(r){throw new Error(r.response?.data?.message||"Erreur lors de la suppression de l'article")}},getAdminPosts:async(t=1,r=10)=>{try{const l=new URLSearchParams({page:t.toString(),limit:r.toString()}),s=(await N.get(`/blog/admin/posts?${l}`)).data;if(!s||typeof s!="object")throw new Error("Format de réponse invalide du serveur");return Array.isArray(s.posts)||(console.warn("La propriété posts n'est pas un tableau, initialisation avec un tableau vide"),s.posts=[]),typeof s.total!="number"&&(s.total=s.posts.length),s}catch(l){if(console.error("Erreur dans blogService.getAdminPosts:",l),l.response?.status===404)return{posts:[],total:0,page:parseInt(t),limit:parseInt(r),totalPages:0};throw new Error(l.response?.data?.message||l.message||"Erreur lors de la récupération des articles")}}},La=()=>{const{user:t}=fe(),[r,l]=i.useState([]),[x,s]=i.useState(!0),[d,h]=i.useState(""),[j,g]=i.useState(""),[p,a]=i.useState(!1),[D,E]=i.useState(null),[v,f]=i.useState({title:"",content:"",excerpt:"",author:"",tags:"",status:"draft"}),[P,W]=i.useState(1),[b,y]=i.useState(1),[C,m]=i.useState(null);i.useEffect(()=>{o()},[P]);const o=async()=>{try{s(!0),h("");const L=await wt.getAdminPosts(P,10);console.log("API response data:",L),L&&Array.isArray(L.posts)?(L.posts.forEach((T,q)=>{console.log(`Post ${q} structure:`,{id:T.id,title:T.title,author:T.author,status:T.status,tags:T.tags})}),l(L.posts),y(Math.ceil((L.total||0)/10))):(console.warn("Réponse API inattendue:",L),l([]),y(1),h("Format de réponse inattendu du serveur"))}catch(L){console.error("Error fetching blog posts:",L),l([]),y(1),h(L.message||"Erreur lors de la récupération des articles de blog")}finally{s(!1)}},k=L=>{const{name:T,value:q}=L.target;f(X=>({...X,[T]:q}))},V=(L=null)=>{if(L){E(L);const T=L.author?L.author.username||`${L.author.first_name||""} ${L.author.last_name||""}`.trim():t?.username||"";let q="";try{if(typeof L.tags=="string"){const X=JSON.parse(L.tags);q=Array.isArray(X)?X.join(", "):""}else Array.isArray(L.tags)&&(q=L.tags.join(", "))}catch(X){console.warn("Error parsing tags:",X),q=""}f({title:L.title,content:L.content,excerpt:L.excerpt||"",author:T,tags:q,status:L.status||"draft"})}else E(null),f({title:"",content:"",excerpt:"",author:t?.username||"",tags:"",status:"draft"});a(!0)},U=()=>{a(!1)},$=async()=>{try{s(!0);const L={...v,tags:v.tags.split(",").map(T=>T.trim()).filter(T=>T)};D?(await wt.updatePost(D.id,L),g("Article mis à jour avec succès")):(await wt.createPost(L),g("Article créé avec succès")),o(),U()}catch(L){console.error("Error saving blog post:",L),h(L.message||"Erreur lors de l'enregistrement de l'article")}finally{s(!1)}},G=L=>{m(L)},z=async()=>{try{s(!0),await wt.deletePost(C),g("Article supprimé avec succès"),o(),m(null)}catch(L){console.error("Error deleting blog post:",L),h(L.message||"Erreur lors de la suppression de l'article")}finally{s(!1)}},O=(L,T)=>{W(T)};return e.jsxs(u,{children:[e.jsxs(_,{elevation:3,sx:{p:3,mb:3},children:[e.jsxs(u,{display:"flex",justifyContent:"space-between",alignItems:"center",mb:2,children:[e.jsxs(c,{variant:"h5",component:"h1",gutterBottom:!0,sx:{display:"flex",alignItems:"center",gap:1},children:[e.jsx(ct,{color:"primary"})," Gestion du Blog"]}),e.jsx(M,{variant:"contained",color:"primary",startIcon:e.jsx(tt,{}),onClick:()=>V(),children:"Nouvel Article"})]}),d&&e.jsx(te,{severity:"error",sx:{mb:2},children:d}),j&&e.jsx(te,{severity:"success",sx:{mb:2},children:j}),x&&!r.length?e.jsx(u,{display:"flex",justifyContent:"center",my:4,children:e.jsx(H,{})}):e.jsxs(e.Fragment,{children:[e.jsx(ze,{component:_,sx:{mt:2},children:e.jsxs(Ne,{children:[e.jsx(_e,{children:e.jsxs(ie,{children:[e.jsx(S,{children:"Titre"}),e.jsx(S,{children:"Auteur"}),e.jsx(S,{children:"Date"}),e.jsx(S,{children:"Statut"}),e.jsx(S,{children:"Tags"}),e.jsx(S,{align:"right",children:"Actions"})]})}),e.jsx(Be,{children:Array.isArray(r)&&r.length>0?r.map(L=>e.jsxs(ie,{children:[e.jsx(S,{children:L.title}),e.jsx(S,{children:L.author?`${L.author.first_name||""} ${L.author.last_name||""}`.trim()||L.author.username:"N/A"}),e.jsx(S,{children:new Date(L.created_at).toLocaleDateString()}),e.jsx(S,{children:e.jsx(se,{label:L.status==="published"?"Publié":"Brouillon",color:L.status==="published"?"success":"default",size:"small"})}),e.jsx(S,{children:(()=>{try{return(typeof L.tags=="string"?JSON.parse(L.tags):Array.isArray(L.tags)?L.tags:[]).map(q=>e.jsx(se,{label:q,size:"small",sx:{mr:.5,mb:.5}},q))}catch{return null}})()}),e.jsxs(S,{align:"right",children:[e.jsx(B,{color:"primary",onClick:()=>V(L),size:"small",children:e.jsx(Oe,{fontSize:"small"})}),e.jsx(B,{color:"error",onClick:()=>G(L.id),size:"small",children:e.jsx(st,{fontSize:"small"})}),e.jsx(B,{color:"info",size:"small",onClick:()=>window.open(`/blog/${L.id}`,"_blank"),children:e.jsx(rt,{fontSize:"small"})})]})]},L.id)):e.jsx(ie,{children:e.jsx(S,{colSpan:6,align:"center",children:x?e.jsxs(u,{display:"flex",alignItems:"center",justifyContent:"center",py:2,children:[e.jsx(H,{size:20,sx:{mr:1}}),"Chargement des articles..."]}):"Aucun article de blog trouvé"})})})]})}),b>1&&e.jsx(u,{display:"flex",justifyContent:"center",mt:3,children:e.jsx(Rs,{count:b,page:P,onChange:O,color:"primary"})})]})]}),e.jsxs(Ce,{open:p,onClose:U,maxWidth:"md",fullWidth:!0,children:[e.jsx(Se,{children:D?"Modifier l'article":"Nouvel article"}),e.jsxs(we,{children:[e.jsx(A,{autoFocus:!0,margin:"dense",name:"title",label:"Titre",type:"text",fullWidth:!0,value:v.title,onChange:k,required:!0,sx:{mb:2}}),e.jsx(A,{margin:"dense",name:"excerpt",label:"Extrait",type:"text",fullWidth:!0,value:v.excerpt,onChange:k,sx:{mb:2}}),e.jsx(A,{margin:"dense",name:"content",label:"Contenu",multiline:!0,rows:10,fullWidth:!0,value:v.content,onChange:k,required:!0,sx:{mb:2}}),e.jsxs(n,{container:!0,spacing:2,children:[e.jsx(n,{item:!0,xs:12,sm:6,children:e.jsx(A,{margin:"dense",name:"author",label:"Auteur",type:"text",fullWidth:!0,value:v.author,onChange:k,sx:{mb:2}})}),e.jsx(n,{item:!0,xs:12,sm:6,children:e.jsxs(A,{margin:"dense",name:"status",label:"Statut",select:!0,fullWidth:!0,value:v.status,onChange:k,sx:{mb:2},children:[e.jsx("option",{value:"draft",children:"Brouillon"}),e.jsx("option",{value:"published",children:"Publié"})]})})]}),e.jsx(A,{margin:"dense",name:"tags",label:"Tags (séparés par des virgules)",type:"text",fullWidth:!0,value:v.tags,onChange:k,sx:{mb:2}})]}),e.jsxs(Ie,{children:[e.jsx(M,{onClick:U,children:"Annuler"}),e.jsx(M,{onClick:$,variant:"contained",color:"primary",children:x?e.jsx(H,{size:24}):"Enregistrer"})]})]}),e.jsxs(Ce,{open:!!C,onClose:()=>m(null),children:[e.jsx(Se,{children:"Confirmer la suppression"}),e.jsx(we,{children:e.jsx(c,{children:"Êtes-vous sûr de vouloir supprimer cet article ? Cette action est irréversible."})}),e.jsxs(Ie,{children:[e.jsx(M,{onClick:()=>m(null),children:"Annuler"}),e.jsx(M,{onClick:z,color:"error",variant:"contained",children:x?e.jsx(H,{size:24}):"Supprimer"})]})]}),e.jsx(kt,{open:!!d||!!j,autoHideDuration:6e3,onClose:()=>{h(""),g("")},children:e.jsx(te,{onClose:()=>{h(""),g("")},severity:d?"error":"success",sx:{width:"100%"},children:d||j})})]})},Ra=()=>{const[t,r]=i.useState({topic:"",keywords:"",tone:"professional",length:"medium",language:"french",includeImages:!0}),[l,x]=i.useState(""),[s,d]=i.useState(!1),[h,j]=i.useState(""),[g,p]=i.useState(""),[a,D]=i.useState([]),[E,v]=i.useState(!1),f=o=>{const{name:k,value:V}=o.target;r(U=>({...U,[k]:V}))},P=o=>{const{name:k,checked:V}=o.target;r(U=>({...U,[k]:V}))},W=async o=>{o.preventDefault(),d(!0),j(""),p("");try{const k=await N.post("/ai/blog",t);x(k.data.content),p("Contenu généré avec succès!");const V={id:Date.now(),topic:t.topic,content:k.data.content,timestamp:new Date().toISOString()};D([V,...a.slice(0,9)])}catch(k){console.error("Error generating blog content:",k),j(k.response?.data?.message||"Erreur lors de la génération du contenu")}finally{d(!1)}},b=()=>{navigator.clipboard.writeText(l),p("Contenu copié dans le presse-papiers!")},y=async()=>{try{d(!0);const o={title:t.topic,content:l,excerpt:l.substring(0,150)+"...",tags:t.keywords.split(",").map(V=>V.trim()).filter(V=>V),status:"draft"},k=await N.post("/admin/blog",o);p("Article enregistré comme brouillon!")}catch(o){console.error("Error saving blog post:",o),j(o.response?.data?.message||"Erreur lors de l'enregistrement de l'article")}finally{d(!1)}},C=o=>{x(o.content),r(k=>({...k,topic:o.topic})),v(!1)},m=async o=>{if(l)try{d(!0);const k=await N.post("/ai/translate",{content:l,targetLanguage:o});x(k.data.translatedContent),p(`Contenu traduit en ${o==="french"?"français":"arabe"}!`)}catch(k){console.error("Error translating content:",k),j(k.response?.data?.message||"Erreur lors de la traduction du contenu")}finally{d(!1)}};return e.jsx(u,{children:e.jsxs(_,{elevation:3,sx:{p:3,mb:3},children:[e.jsxs(u,{display:"flex",justifyContent:"space-between",alignItems:"center",mb:2,children:[e.jsxs(c,{variant:"h5",component:"h1",gutterBottom:!0,sx:{display:"flex",alignItems:"center",gap:1},children:[e.jsx(ht,{color:"primary"})," Générateur de Blog IA"]}),e.jsx(M,{variant:"outlined",color:"primary",startIcon:e.jsx(Ns,{}),onClick:()=>v(!E),children:"Historique"})]}),h&&e.jsx(te,{severity:"error",sx:{mb:2},children:h}),g&&e.jsx(te,{severity:"success",sx:{mb:2},children:g}),e.jsxs(n,{container:!0,spacing:3,children:[e.jsx(n,{item:!0,xs:12,md:E?4:6,children:e.jsxs("form",{onSubmit:W,children:[e.jsx(A,{label:"Sujet de l'article",name:"topic",value:t.topic,onChange:f,fullWidth:!0,required:!0,margin:"normal"}),e.jsx(A,{label:"Mots-clés (séparés par des virgules)",name:"keywords",value:t.keywords,onChange:f,fullWidth:!0,margin:"normal"}),e.jsxs(n,{container:!0,spacing:2,sx:{mt:1},children:[e.jsx(n,{item:!0,xs:12,sm:6,children:e.jsxs(Q,{fullWidth:!0,children:[e.jsx(J,{children:"Ton"}),e.jsxs(K,{name:"tone",value:t.tone,label:"Ton",onChange:f,children:[e.jsx(I,{value:"professional",children:"Professionnel"}),e.jsx(I,{value:"casual",children:"Décontracté"}),e.jsx(I,{value:"enthusiastic",children:"Enthousiaste"}),e.jsx(I,{value:"informative",children:"Informatif"}),e.jsx(I,{value:"authoritative",children:"Autoritaire"})]})]})}),e.jsx(n,{item:!0,xs:12,sm:6,children:e.jsxs(Q,{fullWidth:!0,children:[e.jsx(J,{children:"Longueur"}),e.jsxs(K,{name:"length",value:t.length,label:"Longueur",onChange:f,children:[e.jsx(I,{value:"short",children:"Court (300-500 mots)"}),e.jsx(I,{value:"medium",children:"Moyen (500-800 mots)"}),e.jsx(I,{value:"long",children:"Long (800-1200 mots)"})]})]})})]}),e.jsxs(n,{container:!0,spacing:2,sx:{mt:1},children:[e.jsx(n,{item:!0,xs:12,sm:6,children:e.jsxs(Q,{fullWidth:!0,children:[e.jsx(J,{children:"Langue"}),e.jsxs(K,{name:"language",value:t.language,label:"Langue",onChange:f,children:[e.jsx(I,{value:"french",children:"Français"}),e.jsx(I,{value:"arabic",children:"Arabe"})]})]})}),e.jsx(n,{item:!0,xs:12,sm:6,sx:{display:"flex",alignItems:"center"},children:e.jsx(Q,{component:"fieldset",children:e.jsxs("label",{children:[e.jsx("input",{type:"checkbox",name:"includeImages",checked:t.includeImages,onChange:P}),"Inclure des suggestions d'images"]})})})]}),e.jsx(M,{type:"submit",variant:"contained",color:"primary",fullWidth:!0,sx:{mt:3},disabled:s,startIcon:s?e.jsx(H,{size:20}):e.jsx(ht,{}),children:s?"Génération en cours...":"Générer le contenu"})]})}),E&&e.jsxs(n,{item:!0,xs:12,md:4,children:[e.jsx(c,{variant:"h6",gutterBottom:!0,children:"Historique des générations"}),e.jsx(u,{sx:{maxHeight:"500px",overflow:"auto"},children:a.length>0?a.map(o=>e.jsx(Z,{sx:{mb:2,cursor:"pointer"},onClick:()=>C(o),children:e.jsxs(Y,{children:[e.jsx(c,{variant:"subtitle1",gutterBottom:!0,children:o.topic}),e.jsx(c,{variant:"body2",color:"text.secondary",children:new Date(o.timestamp).toLocaleString()}),e.jsxs(c,{variant:"body2",noWrap:!0,sx:{mt:1},children:[o.content.substring(0,100),"..."]})]})},o.id)):e.jsx(c,{variant:"body2",color:"text.secondary",children:"Aucun historique disponible"})})]}),e.jsx(n,{item:!0,xs:12,md:E?4:6,children:e.jsxs(u,{sx:{position:"relative"},children:[e.jsx(c,{variant:"h6",gutterBottom:!0,children:"Contenu généré"}),l&&e.jsxs(u,{sx:{position:"absolute",top:0,right:0},children:[e.jsx(de,{title:"Copier le contenu",children:e.jsx(B,{onClick:b,children:e.jsx(Xt,{})})}),e.jsx(de,{title:"Enregistrer comme brouillon",children:e.jsx(B,{onClick:y,children:e.jsx(Xe,{})})}),e.jsx(de,{title:"Éditer dans le gestionnaire de blog",children:e.jsx(B,{onClick:()=>window.location.href="/admin/blog",children:e.jsx(Oe,{})})})]}),e.jsx(A,{multiline:!0,rows:20,fullWidth:!0,variant:"outlined",value:l,onChange:o=>x(o.target.value),placeholder:"Le contenu généré apparaîtra ici..."}),l&&e.jsxs(u,{sx:{mt:2,display:"flex",justifyContent:"space-between"},children:[e.jsxs(u,{children:[e.jsx(se,{icon:e.jsx(Ke,{}),label:"Traduire en français",onClick:()=>m("french"),sx:{mr:1},disabled:t.language==="french"}),e.jsx(se,{icon:e.jsx(Ke,{}),label:"Traduire en arabe",onClick:()=>m("arabic"),disabled:t.language==="arabic"})]}),e.jsx(M,{variant:"contained",color:"primary",startIcon:e.jsx(Xe,{}),onClick:y,children:"Enregistrer comme brouillon"})]})]})})]})]})})},Ge=({type:t,data:r})=>e.jsxs(u,{sx:{height:300,display:"flex",alignItems:"center",justifyContent:"center",border:"1px dashed #ccc"},children:[t==="bar"&&e.jsx(lt,{sx:{fontSize:60,color:"primary.main"}}),t==="line"&&e.jsx(Cn,{sx:{fontSize:60,color:"success.main"}}),t==="pie"&&e.jsx(Sn,{sx:{fontSize:60,color:"warning.main"}}),e.jsxs(c,{variant:"body2",color:"text.secondary",sx:{ml:2},children:[t==="bar"?"Graphique à barres":t==="line"?"Graphique linéaire":"Graphique circulaire"," -",r||"Données de démonstration"]})]}),Wa=()=>{const[t,r]=i.useState({dataType:"sales",timeRange:"month",analysisType:"trends",compareWithPrevious:!0,language:"french"}),[l,x]=i.useState(null),[s,d]=i.useState(""),[h,j]=i.useState(!1),[g,p]=i.useState(!1),[a,D]=i.useState(""),[E,v]=i.useState(""),[f,P]=i.useState(0);i.useEffect(()=>{W()},[t.dataType,t.timeRange]);const W=async()=>{try{p(!0);const $=await N.get(`/admin/data?type=${t.dataType}&timeRange=${t.timeRange}`);x($.data)}catch($){console.error("Error fetching platform data:",$),D("Erreur lors de la récupération des données de la plateforme")}finally{p(!1)}},b=$=>{const{name:G,value:z}=$.target;r(O=>({...O,[G]:z}))},y=$=>{const{name:G,checked:z}=$.target;r(O=>({...O,[G]:z}))},C=($,G)=>{P(G)},m=async $=>{$.preventDefault(),j(!0),D(""),v("");try{const G={...t,platformData:l},z=await N.post("/ai/analyze",G);d(z.data.analysis),v("Analyse générée avec succès!")}catch(G){console.error("Error generating analysis:",G),D(G.response?.data?.message||"Erreur lors de la génération de l'analyse")}finally{j(!1)}},o=()=>{navigator.clipboard.writeText(s),v("Analyse copiée dans le presse-papiers!")},k=async()=>{try{j(!0);const $={title:`Analyse ${t.analysisType} des ${t.dataType} - ${new Date().toLocaleDateString()}`,content:s,dataType:t.dataType,timeRange:t.timeRange,analysisType:t.analysisType,rawData:l},G=await N.post("/admin/reports",$);v("Rapport enregistré avec succès!")}catch($){console.error("Error saving report:",$),D($.response?.data?.message||"Erreur lors de l'enregistrement du rapport")}finally{j(!1)}},V=()=>{if(!l)return;const $=JSON.stringify(l,null,2),G=`data:application/json;charset=utf-8,${encodeURIComponent($)}`,z=`poultray-data-${t.dataType}-${t.timeRange}-${new Date().toISOString().split("T")[0]}.json`,O=document.createElement("a");O.setAttribute("href",G),O.setAttribute("download",z),O.click(),v("Données exportées avec succès!")},U=()=>{if(!l)return null;switch(t.dataType){case"sales":return e.jsxs(n,{container:!0,spacing:3,children:[e.jsx(n,{item:!0,xs:12,md:6,children:e.jsxs(Z,{children:[e.jsx(ve,{title:"Ventes par période"}),e.jsx(Y,{children:e.jsx(Ge,{type:"bar",data:"Ventes par période"})})]})}),e.jsx(n,{item:!0,xs:12,md:6,children:e.jsxs(Z,{children:[e.jsx(ve,{title:"Répartition des ventes par catégorie"}),e.jsx(Y,{children:e.jsx(Ge,{type:"pie",data:"Répartition des ventes"})})]})})]});case"users":return e.jsxs(n,{container:!0,spacing:3,children:[e.jsx(n,{item:!0,xs:12,md:6,children:e.jsxs(Z,{children:[e.jsx(ve,{title:"Nouveaux utilisateurs"}),e.jsx(Y,{children:e.jsx(Ge,{type:"line",data:"Croissance des utilisateurs"})})]})}),e.jsx(n,{item:!0,xs:12,md:6,children:e.jsxs(Z,{children:[e.jsx(ve,{title:"Répartition par rôle"}),e.jsx(Y,{children:e.jsx(Ge,{type:"pie",data:"Répartition des utilisateurs"})})]})})]});case"products":return e.jsxs(n,{container:!0,spacing:3,children:[e.jsx(n,{item:!0,xs:12,md:6,children:e.jsxs(Z,{children:[e.jsx(ve,{title:"Produits les plus populaires"}),e.jsx(Y,{children:e.jsx(Ge,{type:"bar",data:"Popularité des produits"})})]})}),e.jsx(n,{item:!0,xs:12,md:6,children:e.jsxs(Z,{children:[e.jsx(ve,{title:"Évolution des stocks"}),e.jsx(Y,{children:e.jsx(Ge,{type:"line",data:"Évolution des stocks"})})]})})]});default:return null}};return e.jsx(u,{children:e.jsxs(_,{elevation:3,sx:{p:3,mb:3},children:[e.jsxs(u,{display:"flex",justifyContent:"space-between",alignItems:"center",mb:2,children:[e.jsxs(c,{variant:"h5",component:"h1",gutterBottom:!0,sx:{display:"flex",alignItems:"center",gap:1},children:[e.jsx(bn,{color:"primary"})," Analyse de Données IA"]}),e.jsx(M,{variant:"outlined",color:"primary",startIcon:e.jsx(os,{}),onClick:V,disabled:!l,children:"Exporter les données"})]}),a&&e.jsx(te,{severity:"error",sx:{mb:2},children:a}),E&&e.jsx(te,{severity:"success",sx:{mb:2},children:E}),e.jsxs(xt,{value:f,onChange:C,sx:{mb:3},children:[e.jsx(ge,{label:"Analyse",icon:e.jsx(ht,{})}),e.jsx(ge,{label:"Visualisation",icon:e.jsx(lt,{})}),e.jsx(ge,{label:"Données brutes",icon:e.jsx(yn,{})})]}),f===0&&e.jsxs(n,{container:!0,spacing:3,children:[e.jsx(n,{item:!0,xs:12,md:5,children:e.jsxs("form",{onSubmit:m,children:[e.jsxs(Q,{fullWidth:!0,margin:"normal",children:[e.jsx(J,{children:"Type de données"}),e.jsxs(K,{name:"dataType",value:t.dataType,label:"Type de données",onChange:b,children:[e.jsx(I,{value:"sales",children:"Ventes"}),e.jsx(I,{value:"users",children:"Utilisateurs"}),e.jsx(I,{value:"products",children:"Produits"}),e.jsx(I,{value:"engagement",children:"Engagement"}),e.jsx(I,{value:"performance",children:"Performance"})]})]}),e.jsxs(Q,{fullWidth:!0,margin:"normal",children:[e.jsx(J,{children:"Période"}),e.jsxs(K,{name:"timeRange",value:t.timeRange,label:"Période",onChange:b,children:[e.jsx(I,{value:"day",children:"Aujourd'hui"}),e.jsx(I,{value:"week",children:"Cette semaine"}),e.jsx(I,{value:"month",children:"Ce mois"}),e.jsx(I,{value:"quarter",children:"Ce trimestre"}),e.jsx(I,{value:"year",children:"Cette année"}),e.jsx(I,{value:"custom",children:"Personnalisée"})]})]}),t.timeRange==="custom"&&e.jsxs(n,{container:!0,spacing:2,sx:{mt:1},children:[e.jsx(n,{item:!0,xs:6,children:e.jsx(A,{label:"Date de début",type:"date",fullWidth:!0,InputLabelProps:{shrink:!0}})}),e.jsx(n,{item:!0,xs:6,children:e.jsx(A,{label:"Date de fin",type:"date",fullWidth:!0,InputLabelProps:{shrink:!0}})})]}),e.jsxs(Q,{fullWidth:!0,margin:"normal",children:[e.jsx(J,{children:"Type d'analyse"}),e.jsxs(K,{name:"analysisType",value:t.analysisType,label:"Type d'analyse",onChange:b,children:[e.jsx(I,{value:"trends",children:"Tendances"}),e.jsx(I,{value:"forecast",children:"Prévisions"}),e.jsx(I,{value:"anomalies",children:"Anomalies"}),e.jsx(I,{value:"correlations",children:"Corrélations"}),e.jsx(I,{value:"summary",children:"Résumé"})]})]}),e.jsxs(Q,{fullWidth:!0,margin:"normal",children:[e.jsx(J,{children:"Langue"}),e.jsxs(K,{name:"language",value:t.language,label:"Langue",onChange:b,children:[e.jsx(I,{value:"french",children:"Français"}),e.jsx(I,{value:"arabic",children:"Arabe"})]})]}),e.jsx(u,{sx:{mt:2},children:e.jsxs("label",{children:[e.jsx("input",{type:"checkbox",name:"compareWithPrevious",checked:t.compareWithPrevious,onChange:y}),"Comparer avec la période précédente"]})}),e.jsx(M,{type:"submit",variant:"contained",color:"primary",fullWidth:!0,sx:{mt:3},disabled:h||g||!l,startIcon:h?e.jsx(H,{size:20}):e.jsx(ht,{}),children:h?"Analyse en cours...":"Générer l'analyse"})]})}),e.jsx(n,{item:!0,xs:12,md:7,children:e.jsxs(u,{sx:{position:"relative"},children:[e.jsx(c,{variant:"h6",gutterBottom:!0,children:"Analyse générée"}),s&&e.jsxs(u,{sx:{position:"absolute",top:0,right:0},children:[e.jsx(de,{title:"Copier l'analyse",children:e.jsx(B,{onClick:o,children:e.jsx(Xt,{})})}),e.jsx(de,{title:"Enregistrer le rapport",children:e.jsx(B,{onClick:k,children:e.jsx(Xe,{})})})]}),e.jsx(A,{multiline:!0,rows:20,fullWidth:!0,variant:"outlined",value:s,onChange:$=>d($.target.value),placeholder:"L'analyse générée apparaîtra ici..."}),s&&e.jsx(u,{sx:{mt:2,display:"flex",justifyContent:"flex-end"},children:e.jsx(M,{variant:"contained",color:"primary",startIcon:e.jsx(Xe,{}),onClick:k,children:"Enregistrer le rapport"})})]})})]}),f===1&&e.jsxs(u,{children:[e.jsx(c,{variant:"h6",gutterBottom:!0,children:"Visualisation des données"}),g?e.jsx(u,{display:"flex",justifyContent:"center",my:4,children:e.jsx(H,{})}):l?U():e.jsx(te,{severity:"info",children:"Sélectionnez un type de données et une période pour afficher les visualisations"})]}),f===2&&e.jsxs(u,{children:[e.jsxs(c,{variant:"h6",gutterBottom:!0,sx:{display:"flex",alignItems:"center",justifyContent:"space-between"},children:[e.jsx("span",{children:"Données brutes"}),e.jsx(M,{variant:"outlined",size:"small",startIcon:e.jsx(os,{}),onClick:V,disabled:!l,children:"Exporter"})]}),g?e.jsx(u,{display:"flex",justifyContent:"center",my:4,children:e.jsx(H,{})}):l?e.jsx(A,{multiline:!0,rows:20,fullWidth:!0,variant:"outlined",value:JSON.stringify(l,null,2),InputProps:{readOnly:!0}}):e.jsx(te,{severity:"info",children:"Sélectionnez un type de données et une période pour afficher les données brutes"})]})]})})},za=()=>{const[t,r]=i.useState({pageName:"",pageType:"about",sections:"",tone:"professional",length:"medium",language:"french",includeImages:!0}),[l,x]=i.useState(""),[s,d]=i.useState(!1),[h,j]=i.useState(""),[g,p]=i.useState(""),[a,D]=i.useState([]),[E,v]=i.useState(!1),f=m=>{const{name:o,value:k}=m.target;r(V=>({...V,[o]:k}))},P=async m=>{m.preventDefault(),d(!0),j(""),p("");try{const o=await N.post("/ai/page-content",t);x(o.data.content),p("Contenu généré avec succès!");const k={id:Date.now(),pageName:t.pageName,pageType:t.pageType,content:o.data.content,timestamp:new Date().toISOString()};D([k,...a.slice(0,9)])}catch(o){console.error("Error generating page content:",o),j(o.response?.data?.message||"Erreur lors de la génération du contenu")}finally{d(!1)}},W=()=>{navigator.clipboard.writeText(l),p("Contenu copié dans le presse-papiers!")},b=async()=>{try{d(!0);const m={title:t.pageName,content:l,type:t.pageType,language:t.language};await N.post("/admin/pages",m),p("Page enregistrée avec succès!")}catch(m){console.error("Error saving page:",m),j(m.response?.data?.message||"Erreur lors de l'enregistrement de la page")}finally{d(!1)}},y=m=>{x(m.content),r({...t,pageName:m.pageName,pageType:m.pageType}),v(!1)},C=async m=>{try{d(!0);const o=await N.post("/ai/translate",{text:l,targetLanguage:m});x(o.data.translatedText),r({...t,language:m}),p(`Contenu traduit en ${m==="french"?"français":"arabe"} avec succès!`)}catch(o){console.error("Error translating content:",o),j(o.response?.data?.message||"Erreur lors de la traduction du contenu")}finally{d(!1)}};return e.jsxs(u,{sx:{p:3},children:[e.jsxs(c,{variant:"h4",gutterBottom:!0,children:[e.jsx(wn,{sx:{mr:1,verticalAlign:"middle",color:"primary.main"}}),"Générateur de Contenu de Page"]}),e.jsx(c,{variant:"body1",color:"text.secondary",paragraph:!0,children:"Utilisez l'IA pour générer du contenu pour les pages statiques de votre site web."}),e.jsxs(n,{container:!0,spacing:3,children:[e.jsx(n,{item:!0,xs:12,md:6,children:e.jsxs(_,{sx:{p:3,height:"100%"},children:[e.jsx(c,{variant:"h6",gutterBottom:!0,children:"Paramètres de génération"}),e.jsx(ue,{sx:{mb:3}}),e.jsx("form",{onSubmit:P,children:e.jsxs(n,{container:!0,spacing:2,children:[e.jsx(n,{item:!0,xs:12,children:e.jsx(A,{fullWidth:!0,label:"Nom de la page",name:"pageName",value:t.pageName,onChange:f,required:!0})}),e.jsx(n,{item:!0,xs:12,children:e.jsxs(Q,{fullWidth:!0,children:[e.jsx(J,{children:"Type de page"}),e.jsxs(K,{name:"pageType",value:t.pageType,onChange:f,label:"Type de page",children:[e.jsx(I,{value:"about",children:"À propos"}),e.jsx(I,{value:"contact",children:"Contact"}),e.jsx(I,{value:"services",children:"Services"}),e.jsx(I,{value:"faq",children:"FAQ"}),e.jsx(I,{value:"terms",children:"Conditions d'utilisation"}),e.jsx(I,{value:"privacy",children:"Politique de confidentialité"})]})]})}),e.jsx(n,{item:!0,xs:12,children:e.jsx(A,{fullWidth:!0,label:"Sections (séparées par des virgules)",name:"sections",value:t.sections,onChange:f,placeholder:"Ex: introduction, mission, équipe",helperText:"Laissez vide pour une génération automatique des sections"})}),e.jsx(n,{item:!0,xs:12,sm:6,children:e.jsxs(Q,{fullWidth:!0,children:[e.jsx(J,{children:"Ton"}),e.jsxs(K,{name:"tone",value:t.tone,onChange:f,label:"Ton",children:[e.jsx(I,{value:"professional",children:"Professionnel"}),e.jsx(I,{value:"friendly",children:"Amical"}),e.jsx(I,{value:"formal",children:"Formel"}),e.jsx(I,{value:"informative",children:"Informatif"})]})]})}),e.jsx(n,{item:!0,xs:12,sm:6,children:e.jsxs(Q,{fullWidth:!0,children:[e.jsx(J,{children:"Longueur"}),e.jsxs(K,{name:"length",value:t.length,onChange:f,label:"Longueur",children:[e.jsx(I,{value:"short",children:"Courte"}),e.jsx(I,{value:"medium",children:"Moyenne"}),e.jsx(I,{value:"long",children:"Longue"})]})]})}),e.jsx(n,{item:!0,xs:12,sm:6,children:e.jsxs(Q,{fullWidth:!0,children:[e.jsx(J,{children:"Langue"}),e.jsxs(K,{name:"language",value:t.language,onChange:f,label:"Langue",children:[e.jsx(I,{value:"french",children:"Français"}),e.jsx(I,{value:"arabic",children:"Arabe"})]})]})}),e.jsx(n,{item:!0,xs:12,children:e.jsxs(u,{sx:{display:"flex",gap:2,mt:2},children:[e.jsx(M,{type:"submit",variant:"contained",color:"primary",disabled:s,startIcon:s?e.jsx(H,{size:20}):e.jsx(ht,{}),children:"Générer le contenu"}),e.jsx(M,{variant:"outlined",onClick:()=>v(!E),startIcon:e.jsx(Ns,{}),children:"Historique"})]})}),h&&e.jsx(n,{item:!0,xs:12,children:e.jsx(te,{severity:"error",children:h})}),g&&e.jsx(n,{item:!0,xs:12,children:e.jsx(te,{severity:"success",children:g})})]})}),E&&a.length>0&&e.jsxs(u,{sx:{mt:3},children:[e.jsx(c,{variant:"h6",gutterBottom:!0,children:"Historique de génération"}),e.jsx(ue,{sx:{mb:2}}),e.jsx(u,{sx:{maxHeight:300,overflow:"auto"},children:a.map(m=>e.jsx(Z,{sx:{mb:2,cursor:"pointer"},onClick:()=>y(m),children:e.jsxs(Y,{children:[e.jsx(c,{variant:"subtitle1",children:m.pageName}),e.jsxs(c,{variant:"body2",color:"text.secondary",children:["Type: ",m.pageType," | ",new Date(m.timestamp).toLocaleString()]})]})},m.id))})]})]})}),e.jsx(n,{item:!0,xs:12,md:6,children:e.jsxs(_,{sx:{p:3,height:"100%"},children:[e.jsxs(u,{sx:{display:"flex",justifyContent:"space-between",alignItems:"center",mb:2},children:[e.jsx(c,{variant:"h6",children:"Contenu généré"}),l&&e.jsxs(u,{children:[e.jsx(de,{title:"Copier le contenu",children:e.jsx(B,{onClick:W,children:e.jsx(Xt,{})})}),e.jsx(de,{title:"Enregistrer comme page",children:e.jsx(B,{onClick:b,disabled:s,children:e.jsx(Xe,{})})}),e.jsx(de,{title:"Traduire en français",children:e.jsx(B,{onClick:()=>C("french"),disabled:s||t.language==="french",children:e.jsx(se,{icon:e.jsx(Ke,{}),label:"FR",size:"small",color:t.language==="french"?"primary":"default"})})}),e.jsx(de,{title:"Traduire en arabe",children:e.jsx(B,{onClick:()=>C("arabic"),disabled:s||t.language==="arabic",children:e.jsx(se,{icon:e.jsx(Ke,{}),label:"AR",size:"small",color:t.language==="arabic"?"primary":"default"})})})]})]}),e.jsx(ue,{sx:{mb:2}}),s?e.jsx(u,{sx:{display:"flex",justifyContent:"center",my:5},children:e.jsx(H,{})}):l?e.jsx(u,{sx:{p:2,border:"1px solid #e0e0e0",borderRadius:1,minHeight:400,maxHeight:600,overflow:"auto",bgcolor:"#f9f9f9",whiteSpace:"pre-wrap"},children:l}):e.jsx(u,{sx:{p:2,border:"1px dashed #e0e0e0",borderRadius:1,minHeight:400,display:"flex",alignItems:"center",justifyContent:"center"},children:e.jsx(c,{variant:"body1",color:"text.secondary",align:"center",children:'Le contenu généré apparaîtra ici. Configurez les paramètres et cliquez sur "Générer le contenu".'})})]})})]})]})},Na=()=>{const{t}=Et(),[r,l]=i.useState("fr"),[x,s]=i.useState({}),[d,h]=i.useState({}),[j,g]=i.useState(!0),[p,a]=i.useState(!1),[D,E]=i.useState(""),[v,f]=i.useState(null),[P,W]=i.useState({open:!1,message:"",severity:"success"});i.useEffect(()=>{(async()=>{g(!0);try{let O;r==="fr"?O=await Ve(()=>Promise.resolve().then(()=>Fn),void 0):O=await Ve(()=>Promise.resolve().then(()=>Vn),void 0),s(JSON.parse(JSON.stringify(O))),h(JSON.parse(JSON.stringify(O)))}catch(O){console.error("Erreur lors du chargement des traductions:",O),W({open:!0,message:"Erreur lors du chargement des traductions",severity:"error"})}finally{g(!1)}})()},[r]);const b=z=>{l(z.target.value)},y=(z,O,L)=>{s(T=>{const q={...T};return q[z]||(q[z]={}),q[z][O]=L,q})},C=z=>(O,L)=>{f(L?z:null)},m=async()=>{a(!0);try{await new Promise(z=>setTimeout(z,1e3)),console.log("Traductions sauvegardées:",x),W({open:!0,message:"Traductions sauvegardées avec succès",severity:"success"}),h(JSON.parse(JSON.stringify(x)))}catch(z){console.error("Erreur lors de la sauvegarde des traductions:",z),W({open:!0,message:"Erreur lors de la sauvegarde des traductions",severity:"error"})}finally{a(!1)}},o=z=>{const O=`new_key_${Date.now()}`;y(z,O,"")},k=(z,O)=>{s(L=>{const T={...L};if(T[z]){const q={...T[z]};delete q[O],T[z]=q}return T})},V=()=>{const z=`new_section_${Date.now()}`;s(O=>({...O,[z]:{}})),f(z)},U=()=>{W(z=>({...z,open:!1}))},$=Object.keys(x).filter(z=>!D||z.toLowerCase().includes(D.toLowerCase())?!0:Object.entries(x[z]||{}).some(([O,L])=>O.toLowerCase().includes(D.toLowerCase())||typeof L=="string"&&L.toLowerCase().includes(D.toLowerCase()))),G=JSON.stringify(x)!==JSON.stringify(d);return j?e.jsx(u,{sx:{display:"flex",justifyContent:"center",alignItems:"center",height:"100%",p:4},children:e.jsx(H,{})}):e.jsxs(u,{sx:{p:3},children:[e.jsx(c,{variant:"h4",gutterBottom:!0,children:t("admin.translations")}),e.jsx(_,{sx:{mb:3,p:2},children:e.jsxs(n,{container:!0,spacing:2,alignItems:"center",children:[e.jsx(n,{item:!0,xs:12,md:4,children:e.jsxs(Q,{fullWidth:!0,children:[e.jsx(J,{id:"language-select-label",children:"Langue"}),e.jsxs(K,{labelId:"language-select-label",value:r,label:"Langue",onChange:b,children:[e.jsx(I,{value:"fr",children:"Français"}),e.jsx(I,{value:"ar",children:"العربية (Arabe)"})]})]})}),e.jsx(n,{item:!0,xs:12,md:6,children:e.jsx(A,{fullWidth:!0,placeholder:"Rechercher des traductions...",value:D,onChange:z=>E(z.target.value),InputProps:{startAdornment:e.jsx(Yt,{position:"start",children:e.jsx(In,{})})}})}),e.jsx(n,{item:!0,xs:12,md:2,children:e.jsx(M,{fullWidth:!0,variant:"contained",color:"primary",startIcon:e.jsx(An,{}),onClick:m,disabled:p||!G,children:p?"Sauvegarde...":"Sauvegarder"})})]})}),e.jsx(u,{sx:{mb:2},children:e.jsx(M,{variant:"outlined",startIcon:e.jsx(ls,{}),onClick:V,children:"Ajouter une section"})}),$.length===0?e.jsx(_,{sx:{p:3,textAlign:"center"},children:e.jsxs(c,{children:['Aucune traduction trouvée pour "',D,'"']})}):$.map(z=>e.jsxs(As,{expanded:v===z,onChange:C(z),sx:{mb:2},children:[e.jsx(Ds,{expandIcon:e.jsx(ks,{}),children:e.jsx(c,{variant:"h6",children:z})}),e.jsxs(Ps,{children:[e.jsx(u,{sx:{mb:2},children:e.jsx(M,{size:"small",startIcon:e.jsx(ls,{}),onClick:()=>o(z),children:"Ajouter une clé"})}),e.jsx(ue,{sx:{mb:2}}),Object.keys(x[z]||{}).length===0?e.jsx(c,{variant:"body2",color:"text.secondary",sx:{fontStyle:"italic"},children:"Aucune clé dans cette section. Ajoutez-en une avec le bouton ci-dessus."}):Object.entries(x[z]||{}).map(([O,L])=>e.jsxs(n,{container:!0,spacing:2,sx:{mb:2},children:[e.jsx(n,{item:!0,xs:12,md:3,children:e.jsx(A,{fullWidth:!0,label:"Clé",value:O,disabled:!0,size:"small"})}),e.jsx(n,{item:!0,xs:12,md:8,children:e.jsx(A,{fullWidth:!0,label:"Valeur",value:typeof L=="string"?L:JSON.stringify(L),onChange:T=>y(z,O,T.target.value),multiline:typeof L=="string"&&L.length>50,rows:typeof L=="string"&&L.length>50?3:1,size:"small"})}),e.jsx(n,{item:!0,xs:12,md:1,sx:{display:"flex",alignItems:"center"},children:e.jsx(B,{color:"error",onClick:()=>k(z,O),size:"small",children:e.jsx(Dn,{})})})]},O))]})]},z)),e.jsx(kt,{open:P.open,autoHideDuration:6e3,onClose:U,anchorOrigin:{vertical:"bottom",horizontal:"right"},children:e.jsx(te,{onClose:U,severity:P.severity,sx:{width:"100%"},children:P.message})})]})},_a="http://localhost:3003/api",Dt=pe.create({baseURL:_a,headers:{"Content-Type":"application/json"}});Dt.interceptors.request.use(t=>{const r=localStorage.getItem("token");return r&&(t.headers.Authorization=`Bearer ${r}`),t},t=>Promise.reject(t));Dt.interceptors.response.use(t=>t,async t=>(t.response&&t.response.status===401&&(localStorage.removeItem("token"),window.location.href="/login"),Promise.reject(t)));const vs=()=>{const[t,r]=i.useState([]),[l,x]=i.useState(!0),[s,d]=i.useState(null);i.useEffect(()=>{h()},[]);const h=async()=>{try{const p=await Dt.get("/admin/roles");r(p.data),x(!1)}catch(p){console.error("Erreur lors de la récupération des rôles:",p),d("Erreur lors de la récupération des rôles"),x(!1)}},j=p=>{console.log("Modifier le rôle:",p)},g=async p=>{if(window.confirm("Êtes-vous sûr de vouloir supprimer ce rôle ?"))try{await Dt.delete(`/admin/roles/${p}`),h()}catch(a){console.error("Erreur lors de la suppression du rôle:",a),d("Erreur lors de la suppression du rôle")}};return l?e.jsx(c,{children:"Chargement..."}):s?e.jsx(c,{color:"error",children:s}):e.jsx(ee,{maxWidth:"lg",sx:{mt:4,mb:4},children:e.jsx(n,{container:!0,spacing:3,children:e.jsxs(n,{item:!0,xs:12,children:[e.jsxs(u,{display:"flex",justifyContent:"space-between",alignItems:"center",mb:3,children:[e.jsx(c,{variant:"h4",component:"h1",gutterBottom:!0,children:"Gestion des Rôles"}),e.jsx(M,{variant:"contained",color:"primary",onClick:()=>j(null),children:"Ajouter un rôle"})]}),e.jsx(_,{sx:{p:2},children:e.jsx(ze,{children:e.jsxs(Ne,{children:[e.jsx(_e,{children:e.jsxs(ie,{children:[e.jsx(S,{children:"Nom du rôle"}),e.jsx(S,{children:"Description"}),e.jsx(S,{children:"Permissions"}),e.jsx(S,{children:"Statut"}),e.jsx(S,{align:"right",children:"Actions"})]})}),e.jsx(Be,{children:t.map(p=>e.jsxs(ie,{children:[e.jsx(S,{children:p.name}),e.jsx(S,{children:p.description}),e.jsx(S,{children:p.permissions&&p.permissions.map((a,D)=>e.jsx(se,{label:a,size:"small",sx:{m:.5}},D))}),e.jsx(S,{children:e.jsx(se,{label:p.is_active?"Actif":"Inactif",color:p.is_active?"success":"default",size:"small"})}),e.jsxs(S,{align:"right",children:[e.jsx(B,{onClick:()=>j(p.id),size:"small",color:"primary",children:e.jsx(Oe,{})}),e.jsx(B,{onClick:()=>g(p.id),size:"small",color:"error",children:e.jsx(st,{})})]})]},p.id))})]})})})]})})})},Ba=()=>{const t=ke(),r=Ot(),[l,x]=i.useState([]),[s,d]=i.useState(!1),h=(v,f="info")=>{const P=new Date().toLocaleTimeString(),W={timestamp:P,message:v,type:f};x(b=>[...b,W]),console.log(`[${P}] ${v}`)};i.useEffect(()=>{h(`🌍 Page chargée: ${r.pathname}`,"info")},[r.pathname]);const j=async()=>{d(!0),h("🔐 Début du test de connexion","info");try{const v={email:"<EMAIL>",password:"admin123"};h(`📡 Tentative de connexion avec: ${v.email}`,"info");const f=await fetch("/api/auth/login",{method:"POST",headers:{"Content-Type":"application/json"},body:JSON.stringify(v)});if(f.ok){const P=await f.json();h(`✅ Connexion réussie pour ${P.user.email}`,"success"),h(`👤 Rôle utilisateur: ${P.user.role}`,"info"),localStorage.setItem("token",P.token),localStorage.setItem("user",JSON.stringify(P.user));const W=g(P.user.role);h(`🎯 Redirection prévue vers: ${W}`,"info"),await new Promise(b=>setTimeout(b,100)),h(`🚀 Navigation vers: ${W}`,"info"),t(W,{replace:!0})}else{const P=await f.text();h(`❌ Erreur de connexion: ${f.status} - ${P}`,"error")}}catch(v){h(`❌ Erreur réseau: ${v.message}`,"error")}finally{d(!1)}},g=v=>({admin:"/admin/dashboard",eleveur:"/eleveur/dashboard",veterinaire:"/veterinaire/dashboard",marchand:"/marchand/dashboard"})[v]||"/dashboard",p=v=>{h(`🧭 Test de navigation vers: ${v}`,"info"),t(v)},a=()=>{x([])},D=()=>{localStorage.removeItem("token"),localStorage.removeItem("user"),h("🗑️ Authentification effacée","info")},E=()=>{const v=localStorage.getItem("token"),f=localStorage.getItem("user");if(h("🔍 Vérification du statut d'authentification:","info"),h(`Token présent: ${!!v}`,"info"),f)try{const P=JSON.parse(f);h(`Utilisateur: ${P.email} (${P.role})`,"info")}catch{h("❌ Erreur parsing utilisateur","error")}else h("Aucun utilisateur stocké","info")};return e.jsx(u,{sx:{p:3,maxWidth:800,mx:"auto"},children:e.jsxs(_,{elevation:3,sx:{p:3},children:[e.jsx(c,{variant:"h4",gutterBottom:!0,children:"🐛 Navigation Debugger"}),e.jsxs(c,{variant:"body2",color:"text.secondary",sx:{mb:3},children:["URL actuelle: ",r.pathname]}),e.jsxs(u,{sx:{mb:3,display:"flex",gap:1,flexWrap:"wrap"},children:[e.jsx(M,{variant:"contained",onClick:j,disabled:s,children:s?e.jsx(H,{size:20}):"🔐 Test Login"}),e.jsx(M,{variant:"outlined",onClick:()=>p("/admin/dashboard"),children:"Admin Dashboard"}),e.jsx(M,{variant:"outlined",onClick:()=>p("/eleveur/dashboard"),children:"Éleveur Dashboard"}),e.jsx(M,{variant:"outlined",onClick:E,children:"🔍 Check Auth"}),e.jsx(M,{variant:"outlined",onClick:D,children:"🗑️ Clear Auth"}),e.jsx(M,{variant:"outlined",onClick:a,children:"📋 Clear Logs"})]}),e.jsx(_,{variant:"outlined",sx:{p:2,height:400,overflow:"auto",backgroundColor:"#f5f5f5",fontFamily:"monospace"},children:l.length===0?e.jsx(c,{color:"text.secondary",children:"Aucun log pour le moment. Cliquez sur un bouton pour commencer les tests."}):l.map((v,f)=>e.jsx(u,{sx:{mb:1},children:e.jsxs(c,{variant:"body2",color:v.type==="error"?"error":v.type==="success"?"success.main":"text.primary",children:["[",v.timestamp,"] ",v.message]})},f))})]})})},$a=i.lazy(()=>Ve(()=>import("./ApiConfig.CQDJF2D9.js"),__vite__mapDeps([0,1,2,3]))),qa=i.lazy(()=>Ve(()=>import("./SmtpConfigPage.DegdsIxF.js"),__vite__mapDeps([4,1,2,5,3]))),Fa=i.lazy(()=>Ve(()=>import("./GeneralSettings.C4Na4802.js"),__vite__mapDeps([6,1,2,5,3]))),Va=i.lazy(()=>Ve(()=>import("./SecuritySettingsPage.D6hbTTNO.js"),__vite__mapDeps([7,1,2,5,3]))),Oa=i.lazy(()=>Ve(()=>import("./LoginAsUser.BJn28_kZ.js"),__vite__mapDeps([8,1,2,3]))),Ua=Es({palette:{primary:{main:"#2E7D32"},secondary:{main:"#FFA000"},background:{default:"#f5f5f5"}},typography:{fontFamily:'"Roboto", "Helvetica", "Arial", sans-serif',h1:{fontSize:"2.5rem",fontWeight:500},h2:{fontSize:"2rem",fontWeight:500}}});function Ha(){return e.jsx(fa,{children:e.jsxs(Ts,{theme:Ua,children:[e.jsx(kn,{}),e.jsx(_n,{children:e.jsx(On,{children:e.jsx(Dr,{future:{v7_startTransition:!0,v7_relativeSplatPath:!0},children:e.jsxs(kr,{children:[e.jsx(F,{path:"/",element:e.jsx(jt,{children:e.jsx(aa,{})})}),e.jsx(F,{path:"/login",element:e.jsx(ca,{})}),e.jsx(F,{path:"/register",element:e.jsx(da,{})}),e.jsx(F,{path:"/debug-navigation",element:e.jsx(Ba,{})}),e.jsx(F,{path:"/eleveurs",element:e.jsx(jt,{children:e.jsx(ia,{})})}),e.jsx(F,{path:"/volailles",element:e.jsx(jt,{children:e.jsx(Nt,{})})}),e.jsx(F,{path:"/dashboard",element:e.jsx(jt,{children:e.jsx(la,{})})}),e.jsxs(F,{path:"/admin/*",element:e.jsx(bt,{requiredRole:"admin"}),children:[e.jsx(F,{index:!0,element:e.jsx(Me,{to:"dashboard"})}),e.jsx(F,{path:"dashboard",element:e.jsx(Sa,{})}),e.jsx(F,{path:"profile",element:e.jsx(i.Suspense,{fallback:e.jsx("div",{children:"Chargement..."}),children:e.jsx(yt,{})})}),e.jsx(F,{path:"roles-plans",element:e.jsx(i.Suspense,{fallback:e.jsx("div",{children:"Chargement..."}),children:e.jsx(vs,{})})}),e.jsx(F,{path:"homepage",element:e.jsx(pa,{})}),e.jsx(F,{path:"users",element:e.jsx(i.Suspense,{fallback:e.jsx("div",{children:"Chargement..."}),children:e.jsx(fs,{})})}),e.jsx(F,{path:"users/:role",element:e.jsx(i.Suspense,{fallback:e.jsx("div",{children:"Chargement..."}),children:e.jsx(fs,{})})}),e.jsx(F,{path:"volailles",element:e.jsx(Nt,{})}),e.jsx(F,{path:"blog",element:e.jsx(i.Suspense,{fallback:e.jsx("div",{children:"Chargement..."}),children:e.jsx(La,{})})}),e.jsx(F,{path:"notifications",element:e.jsx(i.Suspense,{fallback:e.jsx("div",{children:"Chargement..."}),children:e.jsx(va,{})})}),e.jsx(F,{path:"translations",element:e.jsx(i.Suspense,{fallback:e.jsx("div",{children:"Chargement..."}),children:e.jsx(Na,{})})}),e.jsx(F,{path:"roles-plans",element:e.jsx(i.Suspense,{fallback:e.jsx("div",{children:"Chargement..."}),children:e.jsx(vs,{})})}),e.jsx(F,{path:"ai/blog-generator",element:e.jsx(i.Suspense,{fallback:e.jsx("div",{children:"Chargement..."}),children:e.jsx(Ra,{})})}),e.jsx(F,{path:"ai/data-analysis",element:e.jsx(i.Suspense,{fallback:e.jsx("div",{children:"Chargement..."}),children:e.jsx(Wa,{})})}),e.jsx(F,{path:"ai/page-content",element:e.jsx(i.Suspense,{fallback:e.jsx("div",{children:"Chargement..."}),children:e.jsx(za,{})})}),e.jsx(F,{path:"ai/api-config",element:e.jsx(i.Suspense,{fallback:e.jsx("div",{children:"Chargement..."}),children:e.jsx($a,{})})}),e.jsx(F,{path:"settings/smtp",element:e.jsx(i.Suspense,{fallback:e.jsx("div",{children:"Chargement..."}),children:e.jsx(qa,{})})}),e.jsx(F,{path:"settings/general",element:e.jsx(i.Suspense,{fallback:e.jsx("div",{children:"Chargement..."}),children:e.jsx(Fa,{})})}),e.jsx(F,{path:"settings/security",element:e.jsx(i.Suspense,{fallback:e.jsx("div",{children:"Chargement..."}),children:e.jsx(Va,{})})}),e.jsx(F,{path:"users/login-as",element:e.jsx(i.Suspense,{fallback:e.jsx("div",{children:"Chargement..."}),children:e.jsx(Oa,{})})}),e.jsx(F,{path:"*",element:e.jsx(Me,{to:"/admin/dashboard",replace:!0})})]}),e.jsxs(F,{path:"/eleveur/*",element:e.jsx(bt,{requiredRole:"eleveur"}),children:[e.jsx(F,{path:"dashboard",element:e.jsx(Da,{})}),e.jsx(F,{path:"profile",element:e.jsx(i.Suspense,{fallback:e.jsx("div",{children:"Chargement..."}),children:e.jsx(yt,{})})}),e.jsx(F,{path:"volailles",element:e.jsx(Nt,{})}),e.jsx(F,{path:"*",element:e.jsx(Me,{to:"/eleveur/dashboard",replace:!0})})]}),e.jsxs(F,{path:"/veterinaire/*",element:e.jsx(bt,{requiredRole:"veterinaire"}),children:[e.jsx(F,{path:"dashboard",element:e.jsx(Pa,{})}),e.jsx(F,{path:"profile",element:e.jsx(i.Suspense,{fallback:e.jsx("div",{children:"Chargement..."}),children:e.jsx(yt,{})})}),e.jsx(F,{path:"*",element:e.jsx(Me,{to:"/veterinaire/dashboard",replace:!0})})]}),e.jsxs(F,{path:"/marchand/*",element:e.jsx(bt,{requiredRole:"marchand"}),children:[e.jsx(F,{path:"dashboard",element:e.jsx(Ma,{})}),e.jsx(F,{path:"profile",element:e.jsx(i.Suspense,{fallback:e.jsx("div",{children:"Chargement..."}),children:e.jsx(yt,{})})}),e.jsx(F,{path:"*",element:e.jsx(Me,{to:"/marchand/dashboard",replace:!0})})]}),e.jsx(F,{path:"*",element:e.jsx(Me,{to:"/",replace:!0})})]})})})})]})})}Pr.createRoot(document.getElementById("root")).render(e.jsx(qe.StrictMode,{children:e.jsx(Ha,{})}));export{N as a,fe as b,Et as u};
//# sourceMappingURL=index.WVN8qCy5.js.map
