<!DOCTYPE html>
<html lang="fr">
<head>
  <meta charset="UTF-8">
  <meta name="viewport" content="width=device-width, initial-scale=1.0">
  <title>Test React Structure</title>
  <style>
    body {
      font-family: Arial, sans-serif;
      margin: 0;
      padding: 0;
      background-color: #f5f5f5;
    }
    #root {
      min-height: 100vh;
      display: flex;
      flex-direction: column;
    }
    .navbar {
      background-color: #4CAF50;
      color: white;
      padding: 1rem;
      display: flex;
      justify-content: space-between;
      align-items: center;
    }
    .logo {
      font-weight: bold;
      font-size: 1.5rem;
    }
    .nav-links {
      display: flex;
      gap: 1rem;
    }
    .hero {
      background: linear-gradient(135deg, #81C784 0%, #4CAF50 100%);
      color: white;
      padding: 4rem 2rem;
      display: flex;
      flex-direction: column;
      align-items: center;
      text-align: center;
      min-height: 60vh;
    }
    .hero h1 {
      font-size: 2.5rem;
      margin-bottom: 1rem;
    }
    .hero p {
      font-size: 1.2rem;
      margin-bottom: 2rem;
      max-width: 600px;
    }
    .button {
      background-color: #FFC107;
      color: black;
      border: none;
      padding: 0.75rem 1.5rem;
      border-radius: 30px;
      font-weight: bold;
      cursor: pointer;
      box-shadow: 0 4px 6px rgba(0, 0, 0, 0.1);
      transition: transform 0.2s;
    }
    .button:hover {
      transform: translateY(-3px);
    }
    .features {
      padding: 4rem 2rem;
      text-align: center;
    }
    .features h2 {
      color: #4CAF50;
      margin-bottom: 3rem;
      position: relative;
    }
    .features h2:after {
      content: "";
      position: absolute;
      bottom: -10px;
      left: 50%;
      width: 80px;
      height: 4px;
      background: #4CAF50;
      transform: translateX(-50%);
    }
    .feature-grid {
      display: grid;
      grid-template-columns: repeat(auto-fit, minmax(250px, 1fr));
      gap: 2rem;
      max-width: 1200px;
      margin: 0 auto;
    }
    .feature-card {
      background: white;
      border-radius: 8px;
      padding: 2rem;
      box-shadow: 0 4px 10px rgba(0, 0, 0, 0.1);
      transition: transform 0.3s, box-shadow 0.3s;
    }
    .feature-card:hover {
      transform: translateY(-10px);
      box-shadow: 0 12px 20px rgba(0, 0, 0, 0.1);
    }
    .footer {
      background-color: #333;
      color: white;
      padding: 3rem 2rem;
      margin-top: auto;
    }
  </style>
</head>
<body>
  <div id="root">
    <div class="navbar">
      <div class="logo">Poultray DZ</div>
      <div class="nav-links">
        <a href="#" style="color: white; text-decoration: none;">Accueil</a>
        <a href="#" style="color: white; text-decoration: none;">Fonctionnalités</a>
        <a href="#" style="color: white; text-decoration: none;">Tarifs</a>
        <a href="#" style="color: white; text-decoration: none;">Contact</a>
      </div>
      <div>
        <button class="button" style="background-color: white; color: #4CAF50;">Connexion</button>
      </div>
    </div>
    
    <div class="hero">
      <h1>Révolutionnez votre élevage de volailles</h1>
      <p>La première plateforme algérienne dédiée à la gestion et à la commercialisation des volailles.</p>
      <button class="button">Créer un compte</button>
    </div>
    
    <div class="features">
      <h2>Nos Fonctionnalités</h2>
      <div class="feature-grid">
        <div class="feature-card">
          <h3>Gestion des Éleveurs</h3>
          <p>Suivez et gérez facilement tous vos éleveurs partenaires avec des profils détaillés et des statistiques en temps réel.</p>
        </div>
        <div class="feature-card">
          <h3>Suivi Vétérinaire</h3>
          <p>Accédez à des services vétérinaires à distance et suivez la santé de vos volailles avec des rapports détaillés.</p>
        </div>
        <div class="feature-card">
          <h3>Marketplace</h3>
          <p>Vendez et achetez des volailles directement sur la plateforme avec un système de paiement sécurisé.</p>
        </div>
        <div class="feature-card">
          <h3>Agriculture Intelligente</h3>
          <p>Optimisez votre production grâce à des outils d'analyse avancés et des recommandations personnalisées.</p>
        </div>
      </div>
    </div>
    
    <div class="footer">
      <p>© 2023 Poultray DZ. Tous droits réservés.</p>
    </div>
  </div>
</body>
</html>