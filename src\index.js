require('dotenv').config();
const express = require('express');
const cors = require('cors');
const swaggerUi = require('swagger-ui-express');
const YAML = require('yamljs');
const path = require('path');
const os = require('os');
// Import de l'instance Sequelize pour les opérations de base de données
const sequelize = require('./config/database');

const app = express();

// Configuration CORS
const corsOptions = {
  origin: function (origin, callback) {
    if (!origin) return callback(null, true);

    const localAddresses = [];
    const interfaces = os.networkInterfaces();
    Object.keys(interfaces).forEach((iface) => {
      interfaces[iface].forEach((details) => {
        if (details.family === 'IPv4') {
          localAddresses.push(`http://${details.address}`);
          localAddresses.push(`http://${details.address}:${process.env.PORT || 3003}`);
        }
      });
    });

    const allowedOrigins = [
      'http://localhost',
      'http://localhost:3000',
      'http://localhost:3003',
      'http://127.0.0.1',
      'http://127.0.0.1:3000',
      'http://127.0.0.1:3003',
      ...localAddresses
    ];

    if (process.env.NODE_ENV === 'development' || allowedOrigins.some(allowed => origin.startsWith(allowed))) {
      callback(null, true);
    } else {
      callback(new Error('Not allowed by CORS'));
    }
  },
  credentials: true,
  methods: ['GET', 'POST', 'PUT', 'DELETE', 'OPTIONS', 'PATCH'],
  allowedHeaders: ['Content-Type', 'Authorization', 'x-auth-token', 'x-refresh-token'],
  exposedHeaders: ['x-auth-token-refreshed'] // Exposer l'en-tête pour le rafraîchissement du token
};

// Middleware essentiels
app.use(express.json());
app.use(express.urlencoded({ extended: true }));
app.use(cors(corsOptions));

// Logging middleware
app.use((req, res, next) => {
  console.log(`${new Date().toISOString()} - ${req.method} ${req.originalUrl}`);
  next();
});

// Base routes
app.get('/', (req, res) => {
  res.json({
    message: 'Bienvenue sur l\'API Poultray DZ',
    docs: '/api-docs',
    api: '/api'
  });
});

// API routes
app.get('/api', (req, res) => {
  res.json({
    status: 'success',
    message: 'API Poultray DZ - Points d\'entrée disponibles',
    version: '1.0.0',
    endpoints: {
      auth: '/api/auth',
      eleveur: '/api/eleveur',
      veterinaire: '/api/veterinaire',
      volaille: '/api/volaille',
      admin: '/api/admin',
      marchand: '/api/marchand',
      blog: '/api/blog',
      marketplace: '/api/marketplace',
      docs: '/api-docs'
    }
  });
});

// Health check endpoint
app.get('/api/health', (req, res) => {
  res.json({
    status: 'OK',
    message: 'Server is running',
    timestamp: new Date().toISOString(),
    uptime: process.uptime(),
    environment: process.env.NODE_ENV || 'development'
  });
});

// Configuration pour servir les fichiers statiques du frontend
const configureStaticFiles = require('./config/static');
configureStaticFiles(app);

// Middleware pour valider les routes
const validateRoutes = (req, res, next) => {
  // Log route access in development
  if (process.env.NODE_ENV === 'development') {
    console.log(`${req.method} ${req.originalUrl} - ${new Date().toISOString()}`);
  }

  // Add CORS headers for preflight requests
  if (req.method === 'OPTIONS') {
    res.header('Access-Control-Allow-Methods', 'GET, POST, PUT, DELETE, PATCH');
    return res.status(200).json({
      status: 'success',
      message: 'CORS preflight response'
    });
  }

  next();
};

// Apply route validation middleware
app.use(validateRoutes);

// Fonction pour ajouter les colonnes manquantes avec des valeurs par défaut
async function addMissingColumns() {
  try {
    // Ajouter date_modification à la table eleveurs si elle n'existe pas
    await sequelize.query(`
      ALTER TABLE eleveurs
      ADD COLUMN IF NOT EXISTS date_modification TIMESTAMP DEFAULT CURRENT_TIMESTAMP
    `);

    // Ajouter date_modification à la table volailles si elle n'existe pas
    await sequelize.query(`
      ALTER TABLE volailles
      ADD COLUMN IF NOT EXISTS date_modification TIMESTAMP DEFAULT CURRENT_TIMESTAMP
    `);

    // Ajouter lot_numero à la table volailles si elle n'existe pas
    await sequelize.query(`
      ALTER TABLE volailles
      ADD COLUMN IF NOT EXISTS lot_numero VARCHAR(50)
    `);

    // Create ENUM type if it doesn't exist
    await sequelize.query(`
      DO $$
      BEGIN
        IF NOT EXISTS (SELECT 1 FROM pg_type WHERE typname = 'enum_volailles_type_elevage') THEN
          CREATE TYPE enum_volailles_type_elevage AS ENUM ('chair', 'pondeuse', 'reproducteur', 'mixte');
        END IF;
      END $$;
    `);

    // Add type_elevage column
    await sequelize.query(`
      ALTER TABLE volailles
      ADD COLUMN IF NOT EXISTS type_elevage enum_volailles_type_elevage DEFAULT 'chair'
    `);

    // Create ENUM type for statut if it doesn't exist
    await sequelize.query(`
      DO $$
      BEGIN
        IF NOT EXISTS (SELECT 1 FROM pg_type WHERE typname = 'enum_volailles_statut') THEN
          CREATE TYPE enum_volailles_statut AS ENUM ('actif', 'vendu', 'abattu', 'transfere', 'archive');
        END IF;
      END $$;
    `);

    // Add statut column
    await sequelize.query(`
      ALTER TABLE volailles
      ADD COLUMN IF NOT EXISTS statut enum_volailles_statut DEFAULT 'actif'
    `);

    // Add date_acquisition column
    await sequelize.query(`
      ALTER TABLE volailles
      ADD COLUMN IF NOT EXISTS date_acquisition TIMESTAMP DEFAULT CURRENT_TIMESTAMP
    `);

    console.log('Colonnes manquantes ajoutées avec succès');
  } catch (error) {
    console.log('Certaines colonnes existent déjà ou erreur mineure:', error.message);
  }
}

// Fonction asynchrone pour initialiser la base de données
async function initDatabase() {
  try {
    // D'abord ajouter les colonnes manquantes avec des valeurs par défaut
    await addMissingColumns();

    // Désactiver temporairement la synchronisation automatique
    // await sequelize.sync({
    //   force: false,
    //   alter: true,
    //   logging: console.log
    // });
    console.log('Base de données synchronisée avec succès');

    const { createNotificationsTable, ensureApiConfigTable, seedApiKeys } = require('./utils/seeders');

    // Créer la table de notifications
    await createNotificationsTable();

    // Vérifier et mettre à jour la structure de la table ApiConfig
    await ensureApiConfigTable();

    // Initialiser les clés API par défaut
    await seedApiKeys();
  } catch (err) {
    console.error('Erreur lors de la synchronisation:', err);
    process.exit(1);
  }
}

// Initialiser la base de données
initDatabase();

// API Version prefix
const API_PREFIX = '/api';

// Mount all API routes
const authRoutes = require('./routes/authRoutes');
const eleveurRoutes = require('./routes/eleveurRoutes');
const veterinaireRoutes = require('./routes/veterinaireRoutes');
const volailleRoutes = require('./routes/volailleRoutes');
const adminRoutes = require('./routes/adminRoutes');
const marchandRoutes = require('./routes/marchandRoutes');
const blogRoutes = require('./routes/blogRoutes');
const marketplaceRoutes = require('./routes/marketplaceRoutes');

// Additional route imports for missing endpoints (essential routes only)
const translationRoutes = require('./routes/translationRoutes');
const homepageSectionRoutes = require('./routes/homepageSectionRoutes');
const notificationRoutes = require('./routes/notificationRoutes');
const produitRoutes = require('./routes/produitRoutes');

// Mount main API routes
app.use('/api/auth', authRoutes);
app.use('/api/eleveurs', eleveurRoutes);
app.use('/api/veterinaire', veterinaireRoutes);
app.use('/api/volailles', volailleRoutes);
app.use('/api/admin', adminRoutes);
app.use('/api/marchand', marchandRoutes);
app.use('/api/blog', blogRoutes);
app.use('/api/marketplace', marketplaceRoutes);

// Mount additional API routes (essential routes only)
app.use('/api/translations', translationRoutes);
app.use('/api', homepageSectionRoutes); // Homepage sections at /api/homepage/sections
app.use('/api/notifications', notificationRoutes);
app.use('/api/produits', produitRoutes);

// Error handling middleware
app.use((err, req, res, next) => {
  console.error('Error:', err);
  res.status(500).json({
    message: 'Une erreur est survenue',
    error: process.env.NODE_ENV === 'development' ? err.message : undefined
  });
});

// Start the server
const port = process.env.PORT || 3003;
const host = process.env.HOST || 'localhost';

// Démarrer le serveur avec gestion d'erreur pour les adresses déjà utilisées
const server = app.listen(port, host, () => {
  console.log(`Server is running on http://${host}:${port}`);
  console.log(`Environment: ${process.env.NODE_ENV || 'development'}`);
  console.log(`API Documentation: http://${host}:${port}/api-docs`);
});

// Gestion des erreurs de démarrage du serveur
server.on('error', err => {
  if (err.code === 'EADDRINUSE') {
    console.error(`Port ${port} in use. Free it or set PORT to another value.`);
    process.exit(1);
  }
  throw err;
});

module.exports = app;
