class Eleveur {
  final int id;
  final int userId;
  final String nomExploitation;
  final String adresse;
  final String telephone;
  final int capaciteTotale;
  final List<String> specialites;

  Eleveur({
    required this.id,
    required this.userId,
    required this.nomExploitation,
    required this.adresse,
    required this.telephone,
    required this.capaciteTotale,
    required this.specialites,
  });

  factory Eleveur.fromJson(Map<String, dynamic> json) {
    return Eleveur(
      id: json["id"],
      userId: json["user_id"],
      nomExploitation: json["nom_exploitation"],
      adresse: json["adresse"],
      telephone: json["telephone"],
      capaciteTotale: json["capacite_totale"],
      specialites: List<String>.from(json["specialites"]),
    );
  }

  Map<String, dynamic> toJson() {
    return {
      "id": id,
      "user_id": userId,
      "nom_exploitation": nomExploitation,
      "adresse": adresse,
      "telephone": telephone,
      "capacite_totale": capaciteTotale,
      "specialites": specialites,
    };
  }
}


