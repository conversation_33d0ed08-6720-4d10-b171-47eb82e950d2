const { User, Role } = require('../models');
const jwt = require('jsonwebtoken');

const authController = {
  // Récupérer les informations de l'utilisateur connecté
  getCurrentUser: async (req, res) => {
    try {
      const user = await User.findByPk(req.user.id, {
        include: [{ model: Role, as: 'role' }],
        attributes: { exclude: ['password'] }
      });

      if (!user) {
        return res.status(404).json({ message: 'Utilisateur non trouvé' });
      }

      res.json(user);
    } catch (error) {
      console.error('Erreur lors de la récupération de l\'utilisateur:', error);
      res.status(500).json({ message: 'Erreur serveur' });
    }
  },

  // Vérifier le token JWT
  verifyToken: async (req, res) => {
    try {
      const token = req.header('Authorization')?.replace('Bearer ', '');
      
      if (!token) {
        return res.status(401).json({ message: 'Token non fourni' });
      }

      const decoded = jwt.verify(token, process.env.JWT_SECRET);
      const user = await User.findByPk(decoded.id, {
        include: [{ model: Role, as: 'role' }],
        attributes: { exclude: ['password'] }
      });

      if (!user) {
        return res.status(404).json({ message: 'Utilisateur non trouvé' });
      }

      res.json({ valid: true, user });
    } catch (error) {
      console.error('Erreur lors de la vérification du token:', error);
      res.status(401).json({ message: 'Token invalide' });
    }
  },

  // Rafraîchir le token JWT
  refreshToken: async (req, res) => {
    try {
      const token = req.header('Authorization')?.replace('Bearer ', '');
      
      if (!token) {
        return res.status(401).json({ message: 'Token non fourni' });
      }

      const decoded = jwt.verify(token, process.env.JWT_SECRET);
      const user = await User.findByPk(decoded.id);

      if (!user) {
        return res.status(404).json({ message: 'Utilisateur non trouvé' });
      }

      const newToken = await user.generateToken();
      res.json({ token: newToken });
    } catch (error) {
      console.error('Erreur lors du rafraîchissement du token:', error);
      res.status(401).json({ message: 'Token invalide' });
    }
  }
};

module.exports = authController;