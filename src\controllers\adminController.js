const { User, Role, Eleveur, Veterinaire, Consultation, Vente } = require('../models');
const { Op } = require('sequelize');

const adminController = {
  // Récupérer les statistiques globales
  getStats: async (req, res) => {
    try {
      console.log('Début de la récupération des statistiques');
      
      // Vérifier les modèles et la connexion à la base de données
      console.log('Vérification des modèles et de la base de données...');
      
      // Vérifier si les tables existent
      try {
        await User.describe();
        await Role.describe();
        await Consultation.describe();
        await Vente.describe();
      } catch (error) {
        console.error('Erreur lors de la vérification des tables:', error);
        return res.status(500).json({
          error: 'Erreur de structure de la base de données',
          details: 'Une ou plusieurs tables requises sont manquantes'
        });
      }

      // Initialiser les statistiques avec des valeurs par défaut
      let stats = {
        users: {
          total: 0,
          eleveurs: 0,
          veterinaires: 0
        },
        consultations: {
          total: 0,
          thisMonth: 0
        },
        ventes: {
          total: 0,
          thisMonth: 0
        }
      };

      // Compter les utilisateurs avec gestion d'erreurs
      try {
        console.log('Comptage des utilisateurs...');
        stats.users.total = await User.count() || 0;
        
        // Vérifier si la table roles contient les rôles nécessaires
        const roles = await Role.findAll();
        console.log('Rôles disponibles:', roles.map(r => r.name));

        // Compter les éleveurs
        stats.users.eleveurs = await User.count({
          include: [{
            model: Role,
            as: 'role',
            where: { name: 'eleveur' }
          }]
        }) || 0;

        // Compter les vétérinaires
        stats.users.veterinaires = await User.count({
          include: [{
            model: Role,
            as: 'role',
            where: { name: 'veterinaire' }
          }]
        }) || 0;
      } catch (error) {
        console.error('Erreur lors du comptage des utilisateurs:', error);
      }

      // Compter les consultations avec gestion d'erreurs
      try {
        console.log('Comptage des consultations...');
        stats.consultations.total = await Consultation.count() || 0;
        stats.consultations.thisMonth = await Consultation.count({
          where: {
            createdAt: {
              [Op.gte]: new Date(new Date().setDate(1))
            }
          }
        }) || 0;
      } catch (error) {
        console.error('Erreur lors du comptage des consultations:', error);
      }

      // Compter les ventes avec gestion d'erreurs
      try {
        console.log('Comptage des ventes...');
        stats.ventes.total = await Vente.count() || 0;
        stats.ventes.thisMonth = await Vente.count({
          where: {
            createdAt: {
              [Op.gte]: new Date(new Date().setDate(1))
            }
          }
        }) || 0;
      } catch (error) {
        console.error('Erreur lors du comptage des ventes:', error);
      }

      console.log('Statistiques complètes:', stats);
      res.json(stats);
    } catch (error) {
      console.error('Erreur lors de la récupération des statistiques:', error);
      res.status(500).json({ message: 'Erreur serveur' });
    }
  },

  // Récupérer la liste des utilisateurs par rôle
  getUsersByRole: async (req, res) => {
    try {
      const { role } = req.query;
      const page = parseInt(req.query.page) || 1;
      const limit = parseInt(req.query.limit) || 10;
      const offset = (page - 1) * limit;

      const users = await User.findAndCountAll({
        include: [{
          model: Role,
          as: 'role',
          where: role ? { name: role } : {}
        }],
        attributes: { exclude: ['password'] },
        limit,
        offset,
        order: [['createdAt', 'DESC']]
      });

      res.json({
        users: users.rows,
        total: users.count,
        page,
        totalPages: Math.ceil(users.count / limit)
      });
    } catch (error) {
      console.error('Erreur lors de la récupération des utilisateurs:', error);
      res.status(500).json({ message: 'Erreur serveur' });
    }
  },

  // Mettre à jour le statut d'un utilisateur
  updateUserStatus: async (req, res) => {
    try {
      const { id } = req.params;
      const { status } = req.body;

      const user = await User.findByPk(id);
      if (!user) {
        return res.status(404).json({ message: 'Utilisateur non trouvé' });
      }

      await user.update({ status });
      res.json({ message: 'Statut mis à jour avec succès' });
    } catch (error) {
      console.error('Erreur lors de la mise à jour du statut:', error);
      res.status(500).json({ message: 'Erreur serveur' });
    }
  }
};

module.exports = adminController;