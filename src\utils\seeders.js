const { DataTypes } = require('sequelize');
const sequelize = require('../config/database');

// Création de la table notifications
async function createNotificationsTable() {
  try {
    await sequelize.define('Notification', {
      id: {
        type: DataTypes.INTEGER,
        primaryKey: true,
        autoIncrement: true
      },
      type: {
        type: DataTypes.STRING,
        allowNull: false
      },
      message: {
        type: DataTypes.TEXT,
        allowNull: false
      },
      destinataire_id: {
        type: DataTypes.INTEGER,
        allowNull: false
      },
      lu: {
        type: DataTypes.BOOLEAN,
        defaultValue: false
      },
      date_creation: {
        type: DataTypes.DATE,
        defaultValue: DataTypes.NOW
      }
    }).sync();
    console.log('Table des notifications créée avec succès');
  } catch (error) {
    console.error('Erreur lors de la création de la table des notifications:', error);
  }
}

// Création et vérification de la table ApiConfig
async function ensureApiConfigTable() {
  try {
    const ApiConfig = sequelize.define('ApiConfig', {
      id: {
        type: DataTypes.INTEGER,
        primaryKey: true,
        autoIncrement: true
      },
      cle: {
        type: DataTypes.STRING,
        unique: true,
        allowNull: false
      },
      valeur: {
        type: DataTypes.TEXT,
        allowNull: true
      },
      description: {
        type: DataTypes.TEXT
      },
      actif: {
        type: DataTypes.BOOLEAN,
        defaultValue: true
      }
    });
    
    await ApiConfig.sync({ force: true });
    console.log('Table ApiConfig vérifiée/créée avec succès');
    return ApiConfig;
  } catch (error) {
    console.error('Erreur lors de la vérification/création de la table ApiConfig:', error);
    throw error;
  }
}

// Initialisation des clés API par défaut
async function seedApiKeys() {
  try {
    const ApiConfig = await ensureApiConfigTable();
    const defaultKeys = [
      {
        cle: 'OPENAI_API_KEY',
        valeur: process.env.OPENAI_API_KEY || 'default_key',
        description: 'Clé API pour OpenAI'
      },
      {
        cle: 'SMTP_CONFIG',
        valeur: JSON.stringify({
          host: process.env.SMTP_HOST || 'smtp.example.com',
          port: process.env.SMTP_PORT || 587,
          secure: process.env.SMTP_SECURE === 'true',
          auth: {
            user: process.env.SMTP_USER || '<EMAIL>',
            pass: process.env.SMTP_PASS || 'default_password'
          }
        }),
        description: 'Configuration SMTP pour l\'envoi d\'emails'
      }
    ];

    for (const key of defaultKeys) {
      const [config, created] = await ApiConfig.findOrCreate({
        where: { cle: key.cle },
        defaults: {
          ...key,
          createdAt: new Date(),
          updatedAt: new Date()
        }
      });
      console.log(`Configuration ${key.cle} ${created ? 'créée' : 'existante'}`);
    }

    console.log('Clés API initialisées avec succès');
  } catch (error) {
    console.error('Erreur lors de l\'initialisation des clés API:', error);
    throw error;
  }
}

module.exports = {
  createNotificationsTable,
  ensureApiConfigTable,
  seedApiKeys
};