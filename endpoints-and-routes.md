# Documentation des Endpoints et Routes Poultray DZ

## Endpoints API Backend

### Authentification
```
POST   /api/auth/login            # Connexion utilisateur
POST   /api/auth/register         # Inscription nouvel utilisateur
POST   /api/auth/logout           # Déconnexion
GET    /api/auth/verify           # Vérification token
POST   /api/auth/refresh-token    # Rafraîchissement token
```

### Utilisateurs
```
GET    /api/users                 # Liste des utilisateurs
GET    /api/users/:id             # Détails utilisateur
PUT    /api/users/:id             # Mise à jour utilisateur
DELETE /api/users/:id             # Suppression utilisateur
PATCH  /api/users/:id/role        # Modification rôle utilisateur
```

### Éleveurs
```
GET    /api/eleveurs                    # Liste des éleveurs
GET    /api/eleveurs/:id                # Détails éleveur
POST   /api/eleveurs                    # Création éleveur
PUT    /api/eleveurs/:id                # Mise à jour éleveur
DELETE /api/eleveurs/:id                # Suppression éleveur
GET    /api/eleveurs/:id/statistiques   # Statistiques éleveur
```

### Volailles
```
GET    /api/volailles                   # Liste des volailles
GET    /api/volailles/:id               # Détails volaille
POST   /api/volailles                   # Ajout volaille
PUT    /api/volailles/:id               # Mise à jour volaille
DELETE /api/volailles/:id               # Suppression volaille
GET    /api/volailles/categories        # Liste des catégories
```

### Vétérinaires
```
GET    /api/veterinaires                # Liste des vétérinaires
GET    /api/veterinaires/:id            # Détails vétérinaire
POST   /api/veterinaires                # Ajout vétérinaire
PUT    /api/veterinaires/:id            # Mise à jour vétérinaire
DELETE /api/veterinaires/:id            # Suppression vétérinaire
GET    /api/veterinaires/:id/clients    # Liste des clients
```

### Consultations
```
GET    /api/consultations               # Liste des consultations
GET    /api/consultations/:id           # Détails consultation
POST   /api/consultations               # Création consultation
PUT    /api/consultations/:id           # Mise à jour consultation
DELETE /api/consultations/:id           # Suppression consultation
```

### Commandes
```
GET    /api/commandes                   # Liste des commandes
GET    /api/commandes/:id               # Détails commande
POST   /api/commandes                   # Création commande
PUT    /api/commandes/:id               # Mise à jour commande
DELETE /api/commandes/:id               # Suppression commande
PATCH  /api/commandes/:id/status        # Mise à jour statut
```

### Alertes Stock
```
GET    /api/alertes-stock               # Liste des alertes
POST   /api/alertes-stock               # Création alerte
DELETE /api/alertes-stock/:id           # Suppression alerte
PATCH  /api/alertes-stock/:id/status    # Mise à jour statut
```

### Statistiques
```
GET    /api/stats/global                # Statistiques globales
GET    /api/stats/ventes                # Statistiques ventes
GET    /api/stats/eleveurs              # Statistiques éleveurs
GET    /api/stats/veterinaires          # Statistiques vétérinaires
```

## Routes Frontend

### Pages Publiques
```
/                       # Accueil
/about                  # À propos
/contact                # Contact
/services               # Services
/marketplace            # Place de marché
```

### Authentification
```
/login                  # Connexion
/register               # Inscription
/forgot-password        # Mot de passe oublié
/reset-password         # Réinitialisation mot de passe
```

### Tableaux de Bord
```
/dashboard              # Tableau de bord principal
/dashboard/eleveur      # Tableau de bord éleveur
/dashboard/veterinaire  # Tableau de bord vétérinaire
/dashboard/admin        # Tableau de bord administrateur
```

### Gestion Éleveur
```
/eleveur/profile        # Profil éleveur
/eleveur/volailles     # Gestion volailles
/eleveur/commandes     # Gestion commandes
/eleveur/statistiques  # Statistiques éleveur
```

### Gestion Vétérinaire
```
/veterinaire/profile    # Profil vétérinaire
/veterinaire/clients    # Gestion clients
/veterinaire/consultations # Gestion consultations
/veterinaire/agenda     # Agenda rendez-vous
```

### Administration
```
/admin/users            # Gestion utilisateurs
/admin/eleveurs         # Gestion éleveurs
/admin/veterinaires     # Gestion vétérinaires
/admin/categories       # Gestion catégories
/admin/settings         # Paramètres système
```

### Place de Marché
```
/marketplace/products   # Liste produits
/marketplace/cart       # Panier
/marketplace/checkout   # Paiement
/marketplace/orders     # Commandes
```

### Profil et Paramètres
```
/profile                # Profil utilisateur
/settings               # Paramètres compte
/notifications          # Centre notifications
```

## Notes d'Utilisation

1. Tous les endpoints API nécessitent un token JWT valide dans le header Authorization:
```
Authorization: Bearer <token>
```

2. Les réponses API suivent le format standard:
```json
{
  "success": true/false,
  "data": {},
  "message": "Message descriptif",
  "errors": []
}
```

3. Gestion des erreurs:
- 400: Requête invalide
- 401: Non authentifié
- 403: Non autorisé
- 404: Ressource non trouvée
- 500: Erreur serveur

4. Pagination:
```
GET /api/resource?page=1&limit=10&sort=createdAt:desc
```

5. Filtrage:
```
GET /api/resource?field=value&status=active
```

6. Protection CSRF activée sur toutes les routes POST/PUT/DELETE

7. Rate limiting: 100 requêtes/minute par IP