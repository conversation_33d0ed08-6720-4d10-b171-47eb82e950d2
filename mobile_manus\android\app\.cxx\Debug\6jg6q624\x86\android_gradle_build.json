{"buildFiles": ["C:\\Users\\<USER>\\fvm\\versions\\3.10.4\\packages\\flutter_tools\\gradle\\src\\main\\groovy\\CMakeLists.txt"], "cleanCommandsComponents": [["K:\\AndroidSDK\\cmake\\3.22.1\\bin\\ninja.exe", "-C", "K:\\Projets_Sites_Web\\Poultray-dz-TraeDev\\Web_App\\Poultraydz-Trae\\mobile_manus\\android\\app\\.cxx\\Debug\\6jg6q624\\x86", "clean"]], "buildTargetsCommandComponents": ["K:\\AndroidSDK\\cmake\\3.22.1\\bin\\ninja.exe", "-C", "K:\\Projets_Sites_Web\\Poultray-dz-TraeDev\\Web_App\\Poultraydz-Trae\\mobile_manus\\android\\app\\.cxx\\Debug\\6jg6q624\\x86", "{LIST_OF_TARGETS_TO_BUILD}"], "libraries": {}, "toolchains": {"toolchain": {"cCompilerExecutable": "K:\\AndroidSDK\\ndk\\27.0.12077973\\toolchains\\llvm\\prebuilt\\windows-x86_64\\bin\\clang.exe", "cppCompilerExecutable": "K:\\AndroidSDK\\ndk\\27.0.12077973\\toolchains\\llvm\\prebuilt\\windows-x86_64\\bin\\clang++.exe"}}, "cFileExtensions": [], "cppFileExtensions": []}