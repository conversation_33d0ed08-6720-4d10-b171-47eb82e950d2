// ignore: unused_import
import 'package:intl/intl.dart' as intl;
import 'app_localizations.dart';

// ignore_for_file: type=lint

/// The translations for English (`en`).
class AppLocalizationsEn extends AppLocalizations {
  AppLocalizationsEn([String locale = 'en']) : super(locale);

  @override
  String get appTitle => 'Poultry DZ';

  @override
  String get helloWorld => 'Hello World!';

  @override
  String get loginSubtitle => 'Manage your poultry farm efficiently';

  @override
  String get email => 'Email';

  @override
  String get password => 'Password';

  @override
  String get login => 'Login';

  @override
  String get pleaseEnterEmail => 'Please enter your email';

  @override
  String get pleaseEnterValidEmail => 'Please enter a valid email';

  @override
  String get pleaseEnterPassword => 'Please enter your password';

  @override
  String get passwordTooShort => 'Password must be at least 6 characters';

  @override
  String get loginSuccessful => 'Login successful';

  @override
  String get loginFailed => 'Lo<PERSON> failed';

  @override
  String get dontHaveAccount => 'Don\'t have an account? Register here';

  @override
  String get registrationComingSoon => 'Registration feature coming soon';
}
