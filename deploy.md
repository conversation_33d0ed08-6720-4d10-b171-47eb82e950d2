# Poultry DZ Deployment Guide

## Local Development Deployment

### Prerequisites
- Node.js (v16 or higher)
- PostgreSQL (v13 or higher)
- npm or yarn

### Backend Setup

1. Install dependencies:
```bash
npm install
```

2. Configure environment variables:
- Copy `.env.example` to `.env`
- Update database credentials and other configurations

3. Initialize database:
```bash
psql -U postgres -d poultraydz -h localhost -p 5432
# Run migrations
node scripts/run-migrations.js
```

4. Start the backend server:
```bash
npm run dev
```

### Frontend Setup

1. Navigate to frontend directory:
```bash
cd frontend
```

2. Install dependencies:
```bash
npm install
```

3. Configure environment variables:
- Copy `frontend/.env.example` to `frontend/.env`
- Update API endpoint and other configurations

4. Build and start frontend:
```bash
npm run build
npm run preview
```

## Production Deployment (Debian WPS Server)

### Prerequisites
- Node.js (v16 or higher)
- PostgreSQL (v13 or higher)
- PM2 for process management
- Nginx web server

### Backend Deployment

1. Clone repository and install dependencies:
```bash
git clone [repository-url]
cd Poultraydz-Trae
npm install
```

2. Configure production environment:
```bash
cp .env.example .env
# Edit .env with production values
nano .env
```

3. Setup PostgreSQL:
```bash
sudo -u postgres psql
CREATE DATABASE poultraydz;
\q

# Run migrations
node scripts/run-migrations.js
```

4. Install PM2 globally:
```bash
npm install -g pm2
```

5. Create PM2 ecosystem file (ecosystem.config.js):
```javascript
module.exports = {
  apps: [{
    name: 'poultraydz-backend',
    script: 'src/index.js',
    instances: 'max',
    exec_mode: 'cluster',
    env: {
      NODE_ENV: 'production'
    }
  }]
}
```

6. Start backend with PM2:
```bash
pm2 start ecosystem.config.js
pm2 save
```

### Frontend Deployment

1. Build frontend:
```bash
cd frontend
npm install
npm run build
```

2. Configure Nginx:
```nginx
server {
    listen 80;
    server_name your-domain.com;

    # Frontend
    location / {
        root /path/to/frontend/dist;
        try_files $uri $uri/ /index.html;
        expires 30d;
    }

    # Backend API
    location /api {
        proxy_pass http://localhost:3000;
        proxy_http_version 1.1;
        proxy_set_header Upgrade $http_upgrade;
        proxy_set_header Connection 'upgrade';
        proxy_set_header Host $host;
        proxy_cache_bypass $http_upgrade;
    }
}
```

3. Test and reload Nginx:
```bash
sudo nginx -t
sudo systemctl reload nginx
```

### SSL Configuration (Optional)

1. Install Certbot:
```bash
sudo apt install certbot python3-certbot-nginx
```

2. Obtain SSL certificate:
```bash
sudo certbot --nginx -d your-domain.com
```

### Monitoring and Maintenance

1. Monitor backend processes:
```bash
pm2 monit
pm2 logs
```

2. Update application:
```bash
# Stop services
pm2 stop all

# Pull updates
git pull

# Install dependencies
npm install
cd frontend && npm install

# Rebuild frontend
npm run build

# Restart services
pm2 restart all
```

3. Database backup:
```bash
pg_dump -U postgres poultraydz > backup_$(date +%Y%m%d).sql
```

### Security Considerations

1. Configure firewall:
```bash
sudo ufw allow 80/tcp
sudo ufw allow 443/tcp
sudo ufw enable
```

2. Set secure file permissions:
```bash
chmod -R 755 /path/to/application
chown -R www-data:www-data /path/to/application
```

3. Regular security updates:
```bash
sudo apt update
sudo apt upgrade
```

### Troubleshooting

1. Check application logs:
```bash
pm2 logs
sudo journalctl -u nginx
```

2. Verify services status:
```bash
pm2 status
sudo systemctl status nginx
sudo systemctl status postgresql
```

3. Monitor system resources:
```bash
htop
df -h
```