import { useState, useEffect } from 'react';
import { useNavigate, useParams } from 'react-router-dom';
import { useAuth } from '../../contexts/AuthContext';
import {
  Box,
  Paper,
  Typography,
  Table,
  TableBody,
  TableCell,
  TableContainer,
  TableHead,
  TableRow,
  TablePagination,
  Chip,
  IconButton,
  Button,
  Dialog,
  DialogTitle,
  DialogContent,
  DialogActions,
  DialogContentText,
  TextField,
  FormControl,
  InputLabel,
  Select,
  MenuItem,
  Alert,
  Tooltip,
  Avatar,
  Grid,
  Card,
  CardContent,
} from '@mui/material';
import {
  Edit as EditIcon,
  Delete as DeleteIcon,
  Add as AddIcon,
  Person as PersonIcon,
  Email as EmailIcon,
  Phone as PhoneIcon,
  LocationOn as LocationIcon,
  Visibility as ViewIcon,
} from '@mui/icons-material';
import axiosInstance from '../../utils/axiosConfig';

const UsersManagement = () => {
  const { role: urlRole } = useParams(); // Capture le rôle de l'URL, ex: "eleveurs"
  const { loginAs } = useAuth();
  const navigate = useNavigate();
  const [users, setUsers] = useState([]);
  const [loading, setLoading] = useState(true);
  const [error, setError] = useState(null);
  const [page, setPage] = useState(0);
  const [rowsPerPage, setRowsPerPage] = useState(10);
  const [totalUsers, setTotalUsers] = useState(0);
  const [openDialog, setOpenDialog] = useState(false);
  const [selectedUser, setSelectedUser] = useState(null);
  const [formData, setFormData] = useState({
    username: '',
    email: '',
    first_name: '',
    last_name: '',
    role: 'eleveur',
    phone: '',
    address: '',
    password: '', // Add password field
  });
  const [stats, setStats] = useState({
    totalUsers: 0,
    totalAdmins: 0,
    totalEleveurs: 0,
    totalVeterinaires: 0,
    totalMarchands: 0,
  });

  // Mapper les rôles pluriels de l'URL vers les noms de rôles singuliers de la DB
  const roleMapping = {
    'eleveurs': 'eleveur',
    'veterinaires': 'veterinaire',
    'marchands': 'marchand',
    // Ajoutez d'autres mappings si nécessaire, ex: 'tous_les_utilisateurs': '' pour afficher tous
  };

  // Déterminez le rôle à envoyer à l'API
  const roleForApi = roleMapping[urlRole] || urlRole; // Utilise le mapping ou la valeur d'origine si non mappée (utile pour 'all' ou autres non pluriels)

  // Charger les utilisateurs
  const fetchUsers = async () => {
    try {
      setLoading(true);
      let url = `/admin/users?page=${page + 1}&limit=${rowsPerPage}`;
      if (roleForApi) { // Utiliser la version singulière du rôle
        url += `&role=${roleForApi}`;
      }
      const response = await axiosInstance.get(url);

      // Vérification et validation des données reçues
      if (response.data && Array.isArray(response.data.users)) {
        setUsers(response.data.users);
        setTotalUsers(response.data.total || 0);
        setError(null);
      } else {
        console.error('Format de réponse invalide:', response.data);
        setUsers([]);
        setTotalUsers(0);
        setError('Format de données invalide reçu du serveur');
      }
    } catch (err) {
      console.error('Erreur lors du chargement des utilisateurs:', err);
      console.error('Stack trace:', err.stack);
      setUsers([]);
      setTotalUsers(0);
      setError(`Erreur lors du chargement des utilisateurs: ${err.message || 'Erreur inconnue'}`);
    } finally {
      setLoading(false);
    }
  };

  // Charger les statistiques
  const fetchStats = async () => {
    try {
      const response = await axiosInstance.get('/admin/stats');
      setStats({
        totalUsers: response.data.totalUsers || 0,
        totalAdmins: response.data.totalAdmins || 0,
        totalEleveurs: response.data.totalEleveurs || 0,
        totalVeterinaires: response.data.totalVeterinaires || 0,
        totalMarchands: response.data.totalMarchands || 0,
      });
    } catch (err) {
      console.error('Erreur lors du chargement des statistiques:', err);
    }
  };

  useEffect(() => {
    fetchUsers();
    fetchStats();
  }, [page, rowsPerPage]);

  const handleChangePage = (event, newPage) => {
    setPage(newPage);
  };

  const handleChangeRowsPerPage = (event) => {
    setRowsPerPage(parseInt(event.target.value, 10));
    setPage(0);
  };

  const handleOpenDialog = (user = null) => {
    if (user) {
      setSelectedUser(user);
      // Handle role field - if it's an object, extract the name
      const roleValue = typeof user.role === 'object' && user.role !== null ? user.role.name : user.role;
      setFormData({
        username: user.username || '',
        email: user.email || '',
        first_name: user.first_name || '',
        last_name: user.last_name || '',
        role: roleValue || 'eleveur',
        phone: user.phone || '',
        address: user.address || '',
        password: '', // Don't pre-fill password for editing
      });
    } else {
      setSelectedUser(null);
      setFormData({
        username: '',
        email: '',
        first_name: '',
        last_name: '',
        role: 'eleveur',
        phone: '',
        address: '',
        password: '', // Initialize password for new user
      });
    }
    setOpenDialog(true);
  };

  const handleCloseDialog = () => {
    setOpenDialog(false);
    setSelectedUser(null);
  };

  const handleInputChange = (event) => {
    const { name, value } = event.target;
    setFormData(prev => ({
      ...prev,
      [name]: value
    }));
  };

  const handleSubmit = async () => {
    try {
      if (selectedUser) {
        // Modifier un utilisateur existant
        await axiosInstance.put(`/admin/users/${selectedUser.id}`, formData);
      } else {
        // Créer un nouvel utilisateur
        await axiosInstance.post('/admin/users', formData);
      }

      handleCloseDialog();
      fetchUsers();
      fetchStats();
    } catch (err) {
      console.error('Erreur lors de la sauvegarde:', err);
      setError('Erreur lors de la sauvegarde de l\'utilisateur');
    }
  };

  const handleDelete = async (userId) => {
    if (window.confirm('Êtes-vous sûr de vouloir supprimer cet utilisateur ?')) {
      try {
        await axiosInstance.delete(`/admin/users/${userId}`);
        fetchUsers();
        fetchStats();
      } catch (err) {
        console.error('Erreur lors de la suppression:', err);
        setError('Erreur lors de la suppression de l\'utilisateur');
      }
    }
  };

  const handleLoginAsUser = async (userId) => {
    try {
      const response = await axiosInstance.post(`/admin/login-as-user/${userId}`);
      if (response.data && response.data.token) {
        loginAs(response.data.token, response.data.user, response.data.impersonationStart, response.data.expiresIn);
        // Redirection handled by loginAs in AuthContext
      }
    } catch (err) {
      console.error('Erreur lors de la connexion en tant qu\'utilisateur:', err);
      setError('Erreur lors de la connexion en tant qu\'utilisateur');
    }
  };

  const getRoleColor = (role) => {
    // Si role est un objet (nouvelle API), extraire le nom
    const roleName = typeof role === 'object' && role !== null ? role.name : role;

    const colors = {
      admin: 'error',
      eleveur: 'primary',
      veterinaire: 'success',
      marchand: 'warning',
    };
    return colors[roleName] || 'default';
  };

  const getRoleLabel = (role) => {
    // Si role est un objet (nouvelle API), extraire le nom
    const roleName = typeof role === 'object' && role !== null ? role.name : role;

    const labels = {
      admin: 'Administrateur',
      eleveur: 'Éleveur',
      veterinaire: 'Vétérinaire',
      marchand: 'Marchand',
    };
    return labels[roleName] || roleName;
  };

  if (loading && users.length === 0) {
    return (
      <Box sx={{ display: 'flex', justifyContent: 'center', alignItems: 'center', height: 400 }}>
        <Typography>Chargement...</Typography>
      </Box>
    );
  }

  return (
    <Box sx={{ p: 3 }}>
      <Typography variant="h4" gutterBottom>
        Gestion des Utilisateurs
      </Typography>

      {error && (
        <Alert severity="error" sx={{ mb: 2 }}>
          {error}
        </Alert>
      )}

      {/* Statistiques */}
      <Grid container spacing={3} sx={{ mb: 3 }}>
        <Grid item xs={12} sm={6} md={2.4}>
          <Card>
            <CardContent>
              <Typography color="textSecondary" gutterBottom>
                Total Utilisateurs
              </Typography>
              <Typography variant="h4">
                {stats.totalUsers}
              </Typography>
            </CardContent>
          </Card>
        </Grid>
        <Grid item xs={12} sm={6} md={2.4}>
          <Card>
            <CardContent>
              <Typography color="textSecondary" gutterBottom>
                Administrateurs
              </Typography>
              <Typography variant="h4" color="error">
                {stats.totalAdmins}
              </Typography>
            </CardContent>
          </Card>
        </Grid>
        <Grid item xs={12} sm={6} md={2.4}>
          <Card>
            <CardContent>
              <Typography color="textSecondary" gutterBottom>
                Éleveurs
              </Typography>
              <Typography variant="h4" color="primary">
                {stats.totalEleveurs}
              </Typography>
            </CardContent>
          </Card>
        </Grid>
        <Grid item xs={12} sm={6} md={2.4}>
          <Card>
            <CardContent>
              <Typography color="textSecondary" gutterBottom>
                Vétérinaires
              </Typography>
              <Typography variant="h4" color="success.main">
                {stats.totalVeterinaires}
              </Typography>
            </CardContent>
          </Card>
        </Grid>
        <Grid item xs={12} sm={6} md={2.4}>
          <Card>
            <CardContent>
              <Typography color="textSecondary" gutterBottom>
                Marchands
              </Typography>
              <Typography variant="h4" color="warning.main">
                {stats.totalMarchands}
              </Typography>
            </CardContent>
          </Card>
        </Grid>
      </Grid>

      {/* Actions */}
      <Box sx={{ display: 'flex', justifyContent: 'space-between', alignItems: 'center', mb: 2 }}>
        <Typography variant="h6">
          Liste des Utilisateurs ({totalUsers})
        </Typography>
        <Button
          variant="contained"
          startIcon={<AddIcon />}
          onClick={() => handleOpenDialog()}
        >
          Ajouter un utilisateur
        </Button>
      </Box>

      {/* Table des utilisateurs */}
      <TableContainer component={Paper}>
        <Table>
          <TableHead>
            <TableRow>
              <TableCell>Avatar</TableCell>
              <TableCell>Nom d'utilisateur</TableCell>
              <TableCell>Nom complet</TableCell>
              <TableCell>Email</TableCell>
              <TableCell>Rôle</TableCell>
              <TableCell>Téléphone</TableCell>
              <TableCell>Actions</TableCell>
            </TableRow>
          </TableHead>
          <TableBody>
            {users.map((user) => (
              <TableRow key={user.id}>
                <TableCell>
                  <Avatar sx={{ bgcolor: 'primary.main' }}>
                    {user.first_name ? user.first_name[0] : user.username ? user.username[0] : 'U'}
                  </Avatar>
                </TableCell>
                <TableCell>{user.username}</TableCell>
                <TableCell>
                  {user.first_name && user.last_name
                    ? `${user.first_name} ${user.last_name}`
                    : '-'}
                </TableCell>
                <TableCell>{user.email}</TableCell>
                <TableCell>
                  <Chip
                    label={getRoleLabel(user.role)}
                    color={getRoleColor(user.role)}
                    size="small"
                  />
                </TableCell>
                <TableCell>{user.phone || '-'}</TableCell>
                <TableCell>
                  <Tooltip title="Modifier">
                    <IconButton
                      size="small"
                      onClick={() => handleOpenDialog(user)}
                    >
                      <EditIcon />
                    </IconButton>
                  </Tooltip>
                  <Tooltip title="Supprimer">
                    <IconButton
                      size="small"
                      onClick={() => handleDelete(user.id)}
                      color="error"
                    >
                      <DeleteIcon />
                    </IconButton>
                  </Tooltip>
                  {((typeof user.role === 'object' && user.role !== null ? user.role.name : user.role) !== 'admin') && (
                    <Tooltip title="Se connecter en tant que cet utilisateur">
                      <IconButton
                        size="small"
                        onClick={() => handleLoginAsUser(user.id)}
                        color="primary"
                      >
                        <PersonIcon />
                      </IconButton>
                    </Tooltip>
                  )}
                </TableCell>
              </TableRow>
            ))}
          </TableBody>
        </Table>
        <TablePagination
          rowsPerPageOptions={[5, 10, 25]}
          component="div"
          count={totalUsers}
          rowsPerPage={rowsPerPage}
          page={page}
          onPageChange={handleChangePage}
          onRowsPerPageChange={handleChangeRowsPerPage}
        />
      </TableContainer>

      {/* Dialog pour ajouter/modifier un utilisateur */}
      <Dialog open={openDialog} onClose={handleCloseDialog} maxWidth="md" fullWidth>
        <DialogTitle>
          {selectedUser ? 'Modifier l\'utilisateur' : 'Ajouter un utilisateur'}
        </DialogTitle>
        <DialogContent>
          <DialogContentText>
            {selectedUser
              ? "Modifier les informations de l'utilisateur existant." : "Créer un nouvel utilisateur en remplissant les détails ci-dessous."}
          </DialogContentText>
          <Grid container spacing={2} sx={{ mt: 1 }}>
            <Grid item xs={12} sm={6}>
              <TextField
                name="username"
                label="Nom d'utilisateur"
                value={formData.username}
                onChange={handleInputChange}
                fullWidth
                required
              />
            </Grid>
            <Grid item xs={12} sm={6}>
              <TextField
                name="email"
                label="Email"
                type="email"
                value={formData.email}
                onChange={handleInputChange}
                fullWidth
                required
              />
            </Grid>
            {/* Password field - required for new users, optional for edit */}
            <Grid item xs={12} sm={6}>
              <TextField
                name="password"
                label={selectedUser ? "Mot de passe (laisser vide pour ne pas changer)" : "Mot de passe"}
                type="password"
                value={formData.password}
                onChange={handleInputChange}
                fullWidth
                required={!selectedUser}
                helperText={selectedUser ? "Laissez vide pour conserver le mot de passe actuel" : ""}
              />
            </Grid>
            <Grid item xs={12} sm={6}>
              <TextField
                name="first_name"
                label="Prénom"
                value={formData.first_name}
                onChange={handleInputChange}
                fullWidth
              />
            </Grid>
            <Grid item xs={12} sm={6}>
              <TextField
                name="last_name"
                label="Nom"
                value={formData.last_name}
                onChange={handleInputChange}
                fullWidth
              />
            </Grid>
            <Grid item xs={12} sm={6}>
              <FormControl fullWidth>
                <InputLabel>Rôle</InputLabel>
                <Select
                  name="role"
                  value={formData.role}
                  onChange={handleInputChange}
                  label="Rôle"
                >
                  <MenuItem value="eleveur">Éleveur</MenuItem>
                  <MenuItem value="veterinaire">Vétérinaire</MenuItem>
                  <MenuItem value="marchand">Marchand</MenuItem>
                  <MenuItem value="admin">Administrateur</MenuItem>
                </Select>
              </FormControl>
            </Grid>
            <Grid item xs={12} sm={6}>
              <TextField
                name="phone"
                label="Téléphone"
                value={formData.phone}
                onChange={handleInputChange}
                fullWidth
              />
            </Grid>
            <Grid item xs={12}>
              <TextField
                name="address"
                label="Adresse"
                value={formData.address}
                onChange={handleInputChange}
                fullWidth
                multiline
                rows={2}
              />
            </Grid>
          </Grid>
        </DialogContent>
        <DialogActions>
          <Button onClick={handleCloseDialog}>Annuler</Button>
          <Button onClick={handleSubmit} variant="contained">
            {selectedUser ? 'Modifier' : 'Ajouter'}
          </Button>
        </DialogActions>
      </Dialog>
    </Box>
  );
};

export default UsersManagement;
