const axios = require('axios');
const chalk = require('chalk');

// Configuration de chalk
const { green, red, yellow, blue, cyan } = chalk;


// Configuration
const API_BASE_URL = 'http://localhost:3000/api';
let authToken = '';

// Liste des endpoints à tester
const endpoints = {
  auth: [
    { method: 'POST', path: '/auth/login', data: { email: '<EMAIL>', password: 'password123' } },
    { method: 'POST', path: '/auth/register', data: { email: '<EMAIL>', password: 'newpass123', role: 'eleveur' } },
    { method: 'GET', path: '/auth/verify' },
  ],
  users: [
    { method: 'GET', path: '/users' },
    { method: 'GET', path: '/users/1' },
    { method: 'PUT', path: '/users/1', data: { name: 'Updated Name' } },
  ],
  eleveurs: [
    { method: 'GET', path: '/eleveurs' },
    { method: 'GET', path: '/eleveurs/1' },
    { method: 'GET', path: '/eleveurs/1/statistiques' },
  ],
  volailles: [
    { method: 'GET', path: '/volailles' },
    { method: 'GET', path: '/volailles/categories' },
    { method: 'GET', path: '/volailles/1' },
  ],
  veterinaires: [
    { method: 'GET', path: '/veterinaires' },
    { method: 'GET', path: '/veterinaires/1' },
    { method: 'GET', path: '/veterinaires/1/clients' },
  ],
  consultations: [
    { method: 'GET', path: '/consultations' },
    { method: 'GET', path: '/consultations/1' },
  ],
  commandes: [
    { method: 'GET', path: '/commandes' },
    { method: 'GET', path: '/commandes/1' },
  ],
  stats: [
    { method: 'GET', path: '/stats/global' },
    { method: 'GET', path: '/stats/ventes' },
    { method: 'GET', path: '/stats/eleveurs' },
  ]
};

// Utilitaires de formatage
const formatResponse = (status, method, path) => {
  const statusColor = status >= 200 && status < 300 ? green : red;
  return `${statusColor(status)} ${yellow(method)} ${path}`;
};

const formatError = (error) => {
  if (error.response) {
    return `${red(error.response.status)} ${error.response.statusText}`;
  }
  return red(error.message);
};

// Fonction de test d'un endpoint
async function testEndpoint(method, path, data = null) {
  const url = `${API_BASE_URL}${path}`;
  const config = {
    headers: authToken ? { Authorization: `Bearer ${authToken}` } : {}
  };

  try {
    const response = await axios({
      method,
      url,
      data,
      ...config
    });

    console.log(formatResponse(response.status, method, path));
    return true;
  } catch (error) {
    console.log(`${formatResponse(error.response?.status || 500, method, path)}\n  ${formatError(error)}`);
    return false;
  }
}

// Fonction principale de test
async function runTests() {
  console.log(blue('\n🚀 Démarrage des tests des endpoints\n'));

  // Test initial d'authentification
  try {
    const loginResponse = await axios.post(`${API_BASE_URL}/auth/login`, {
      email: '<EMAIL>',
      password: 'password123'
    });
    authToken = loginResponse.data.token;
    console.log(green('✓ Authentification réussie\n'));
  } catch (error) {
    console.log(red('✗ Échec de l\'authentification\n'));
    return;
  }

  // Test de tous les endpoints
  for (const [category, categoryEndpoints] of Object.entries(endpoints)) {
    console.log(cyan(`\n📋 Test des endpoints ${category}:`));

    for (const endpoint of categoryEndpoints) {
      await testEndpoint(endpoint.method, endpoint.path, endpoint.data);
    }
  }

  console.log(blue('\n✨ Tests terminés\n'));
}

// Exécution des tests
runTests().catch(error => {
  console.error(red('\n❌ Erreur lors de l\'exécution des tests:'));
  console.error(error);
});
