import 'package:sqflite/sqflite.dart';
import 'package:path/path.dart';
import 'package:mobile_manus/models/user.dart';
import 'package:mobile_manus/models/eleveur.dart';
import 'package:mobile_manus/models/veterinaire.dart';
import 'package:mobile_manus/models/annonce.dart';
import 'package:mobile_manus/models/feed_consumption_log.dart';
import 'package:mobile_manus/models/feed_plan.dart';

class DatabaseHelper {
  static final DatabaseHelper _instance = DatabaseHelper._internal();
  static Database? _database;

  DatabaseHelper._internal();

  factory DatabaseHelper() => _instance;

  Future<Database> get database async {
    _database ??= await _initDatabase();
    return _database!;
  }

  Future<Database> _initDatabase() async {
    final databasesPath = await getDatabasesPath();
    final path = join(databasesPath, 'mobile_manus.db');

    return await openDatabase(
      path,
      version: 1,
      onCreate: _onCreate,
      onUpgrade: _onUpgrade,
    );
  }

  Future<void> _onCreate(Database db, int version) async {
    // Users table
    await db.execute('''
      CREATE TABLE users (
        id INTEGER PRIMARY KEY AUTOINCREMENT,
        username TEXT UNIQUE NOT NULL,
        email TEXT UNIQUE,
        firstName TEXT,
        lastName TEXT,
        role TEXT NOT NULL,
        isActive INTEGER DEFAULT 1,
        createdAt TEXT,
        updatedAt TEXT,
        syncStatus INTEGER DEFAULT 0,
        lastSyncAt TEXT
      )
    ''');

    // Eleveurs table
    await db.execute('''
      CREATE TABLE eleveurs (
        id INTEGER PRIMARY KEY AUTOINCREMENT,
        userId INTEGER,
        nomFerme TEXT,
        adresse TEXT,
        telephone TEXT,
        nombreBatiments INTEGER,
        capaciteTotale INTEGER,
        typeElevage TEXT,
        dateCreation TEXT,
        syncStatus INTEGER DEFAULT 0,
        lastSyncAt TEXT,
        FOREIGN KEY (userId) REFERENCES users (id)
      )
    ''');

    // Veterinaires table
    await db.execute('''
      CREATE TABLE veterinaires (
        id INTEGER PRIMARY KEY AUTOINCREMENT,
        userId INTEGER,
        numeroOrdre TEXT,
        specialite TEXT,
        adresseCabinet TEXT,
        telephoneCabinet TEXT,
        dateInscription TEXT,
        syncStatus INTEGER DEFAULT 0,
        lastSyncAt TEXT,
        FOREIGN KEY (userId) REFERENCES users (id)
      )
    ''');

    // Feed plans table
    await db.execute('''
      CREATE TABLE feed_plans (
        id INTEGER PRIMARY KEY AUTOINCREMENT,
        eleveurId INTEGER,
        nom TEXT NOT NULL,
        typeAliment TEXT,
        quantiteJournaliere REAL,
        heureDistribution TEXT,
        batimentCible TEXT,
        dateDebut TEXT,
        dateFin TEXT,
        isActive INTEGER DEFAULT 1,
        syncStatus INTEGER DEFAULT 0,
        lastSyncAt TEXT,
        FOREIGN KEY (eleveurId) REFERENCES eleveurs (id)
      )
    ''');

    // Feed consumption logs table
    await db.execute('''
      CREATE TABLE feed_consumption_logs (
        id INTEGER PRIMARY KEY AUTOINCREMENT,
        eleveurId INTEGER,
        feedPlanId INTEGER,
        date TEXT NOT NULL,
        quantiteDistribuee REAL,
        quantiteConsommee REAL,
        gaspillage REAL,
        observations TEXT,
        syncStatus INTEGER DEFAULT 0,
        lastSyncAt TEXT,
        FOREIGN KEY (eleveurId) REFERENCES eleveurs (id),
        FOREIGN KEY (feedPlanId) REFERENCES feed_plans (id)
      )
    ''');

    // Annonces table
    await db.execute('''
      CREATE TABLE annonces (
        id INTEGER PRIMARY KEY AUTOINCREMENT,
        userId INTEGER,
        titre TEXT NOT NULL,
        description TEXT,
        espece TEXT,
        age INTEGER,
        quantite INTEGER,
        prix REAL,
        unite TEXT,
        localisation TEXT,
        images TEXT,
        datePublication TEXT,
        dateExpiration TEXT,
        statut TEXT DEFAULT 'active',
        vues INTEGER DEFAULT 0,
        syncStatus INTEGER DEFAULT 0,
        lastSyncAt TEXT,
        FOREIGN KEY (userId) REFERENCES users (id)
      )
    ''');

    // Sync queue table for offline operations
    await db.execute('''
      CREATE TABLE sync_queue (
        id INTEGER PRIMARY KEY AUTOINCREMENT,
        tableName TEXT NOT NULL,
        recordId INTEGER,
        operation TEXT NOT NULL,
        data TEXT,
        createdAt TEXT NOT NULL,
        attempts INTEGER DEFAULT 0,
        lastAttemptAt TEXT,
        error TEXT
      )
    ''');

    // Settings table
    await db.execute('''
      CREATE TABLE settings (
        key TEXT PRIMARY KEY,
        value TEXT,
        updatedAt TEXT
      )
    ''');
  }

  Future<void> _onUpgrade(Database db, int oldVersion, int newVersion) async {
    // Handle database upgrades here
    if (oldVersion < newVersion) {
      // Add migration logic for future versions
    }
  }

  // User operations
  Future<int> insertUser(User user) async {
    final db = await database;
    return await db.insert('users', user.toJson());
  }

  Future<User?> getUser(int id) async {
    final db = await database;
    final List<Map<String, dynamic>> maps = await db.query(
      'users',
      where: 'id = ?',
      whereArgs: [id],
    );

    if (maps.isNotEmpty) {
      return User.fromJson(maps.first);
    }
    return null;
  }

  Future<User?> getUserByUsername(String username) async {
    final db = await database;
    final List<Map<String, dynamic>> maps = await db.query(
      'users',
      where: 'username = ?',
      whereArgs: [username],
    );

    if (maps.isNotEmpty) {
      return User.fromJson(maps.first);
    }
    return null;
  }

  Future<int> updateUser(User user) async {
    final db = await database;
    return await db.update(
      'users',
      user.toJson(),
      where: 'id = ?',
      whereArgs: [user.id],
    );
  }

  // Eleveur operations
  Future<int> insertEleveur(Eleveur eleveur) async {
    final db = await database;
    return await db.insert('eleveurs', eleveur.toJson());
  }

  Future<Eleveur?> getEleveur(int id) async {
    final db = await database;
    final List<Map<String, dynamic>> maps = await db.query(
      'eleveurs',
      where: 'id = ?',
      whereArgs: [id],
    );

    if (maps.isNotEmpty) {
      return Eleveur.fromJson(maps.first);
    }
    return null;
  }

  Future<Eleveur?> getEleveurByUserId(int userId) async {
    final db = await database;
    final List<Map<String, dynamic>> maps = await db.query(
      'eleveurs',
      where: 'userId = ?',
      whereArgs: [userId],
    );

    if (maps.isNotEmpty) {
      return Eleveur.fromJson(maps.first);
    }
    return null;
  }

  // Feed plan operations
  Future<int> insertFeedPlan(FeedPlan feedPlan) async {
    final db = await database;
    return await db.insert('feed_plans', feedPlan.toJson());
  }

  Future<List<FeedPlan>> getFeedPlans(int eleveurId) async {
    final db = await database;
    final List<Map<String, dynamic>> maps = await db.query(
      'feed_plans',
      where: 'eleveurId = ?',
      whereArgs: [eleveurId],
      orderBy: 'dateDebut DESC',
    );

    return List.generate(maps.length, (i) {
      return FeedPlan.fromJson(maps[i]);
    });
  }

  Future<int> updateFeedPlan(FeedPlan feedPlan) async {
    final db = await database;
    return await db.update(
      'feed_plans',
      feedPlan.toJson(),
      where: 'id = ?',
      whereArgs: [feedPlan.id],
    );
  }

  // Feed consumption log operations
  Future<int> insertFeedConsumptionLog(FeedConsumptionLog log) async {
    final db = await database;
    return await db.insert('feed_consumption_logs', log.toJson());
  }

  Future<List<FeedConsumptionLog>> getFeedConsumptionLogs(int eleveurId, {int? limit}) async {
    final db = await database;
    final List<Map<String, dynamic>> maps = await db.query(
      'feed_consumption_logs',
      where: 'eleveurId = ?',
      whereArgs: [eleveurId],
      orderBy: 'date DESC',
      limit: limit,
    );

    return List.generate(maps.length, (i) {
      return FeedConsumptionLog.fromJson(maps[i]);
    });
  }

  // Annonce operations
  Future<int> insertAnnonce(Annonce annonce) async {
    final db = await database;
    return await db.insert('annonces', annonce.toJson());
  }

  Future<List<Annonce>> getAnnonces({int? limit, String? espece}) async {
    final db = await database;
    String whereClause = 'statut = ?';
    List<dynamic> whereArgs = ['active'];
    
    if (espece != null) {
      whereClause += ' AND espece = ?';
      whereArgs.add(espece);
    }

    final List<Map<String, dynamic>> maps = await db.query(
      'annonces',
      where: whereClause,
      whereArgs: whereArgs,
      orderBy: 'datePublication DESC',
      limit: limit,
    );

    return List.generate(maps.length, (i) {
      return Annonce.fromJson(maps[i]);
    });
  }

  Future<List<Annonce>> getUserAnnonces(int userId) async {
    final db = await database;
    final List<Map<String, dynamic>> maps = await db.query(
      'annonces',
      where: 'userId = ?',
      whereArgs: [userId],
      orderBy: 'datePublication DESC',
    );

    return List.generate(maps.length, (i) {
      return Annonce.fromJson(maps[i]);
    });
  }

  // Sync queue operations
  Future<int> addToSyncQueue({
    required String tableName,
    int? recordId,
    required String operation,
    Map<String, dynamic>? data,
  }) async {
    final db = await database;
    return await db.insert('sync_queue', {
      'tableName': tableName,
      'recordId': recordId,
      'operation': operation,
      'data': data != null ? data.toString() : null,
      'createdAt': DateTime.now().toIso8601String(),
      'attempts': 0,
    });
  }

  Future<List<Map<String, dynamic>>> getPendingSyncItems() async {
    final db = await database;
    return await db.query(
      'sync_queue',
      orderBy: 'createdAt ASC',
    );
  }

  Future<void> removeSyncItem(int id) async {
    final db = await database;
    await db.delete(
      'sync_queue',
      where: 'id = ?',
      whereArgs: [id],
    );
  }

  Future<void> updateSyncItemAttempt(int id, String? error) async {
    final db = await database;
    await db.update(
      'sync_queue',
      {
        'attempts': 'attempts + 1',
        'lastAttemptAt': DateTime.now().toIso8601String(),
        'error': error,
      },
      where: 'id = ?',
      whereArgs: [id],
    );
  }

  // Settings operations
  Future<void> setSetting(String key, String value) async {
    final db = await database;
    await db.insert(
      'settings',
      {
        'key': key,
        'value': value,
        'updatedAt': DateTime.now().toIso8601String(),
      },
      conflictAlgorithm: ConflictAlgorithm.replace,
    );
  }

  Future<String?> getSetting(String key) async {
    final db = await database;
    final List<Map<String, dynamic>> maps = await db.query(
      'settings',
      where: 'key = ?',
      whereArgs: [key],
    );

    if (maps.isNotEmpty) {
      return maps.first['value'] as String?;
    }
    return null;
  }

  // Utility methods
  Future<void> markForSync(String tableName, int recordId, String operation) async {
    await addToSyncQueue(
      tableName: tableName,
      recordId: recordId,
      operation: operation,
    );
  }

  Future<void> clearAllData() async {
    final db = await database;
    await db.delete('users');
    await db.delete('eleveurs');
    await db.delete('veterinaires');
    await db.delete('feed_plans');
    await db.delete('feed_consumption_logs');
    await db.delete('annonces');
    await db.delete('sync_queue');
    await db.delete('settings');
  }

  Future<void> close() async {
    final db = await database;
    await db.close();
  }
}

