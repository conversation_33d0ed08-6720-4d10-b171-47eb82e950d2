import { useState, useEffect } from 'react';
import {
  Box,
  Typography,
  Paper,
  Tabs,
  Tab,
  TextField,
  Button,
  Grid,
  Accordion,
  AccordionSummary,
  AccordionDetails,
  CircularProgress,
  Snackbar,
  Alert,
  IconButton,
  Divider,
  InputAdornment,
  FormControl,
  InputLabel,
  Select,
  MenuItem
} from '@mui/material';
import ExpandMoreIcon from '@mui/icons-material/ExpandMore';
import SearchIcon from '@mui/icons-material/Search';
import SaveIcon from '@mui/icons-material/Save';
import AddIcon from '@mui/icons-material/Add';
import DeleteIcon from '@mui/icons-material/Delete';
import { useLanguage } from '../../contexts/LanguageContext';
import axios from 'axios';

// Composant pour gérer les traductions
const TranslationsManager = () => {
  const { t } = useLanguage();
  const [language, setLanguage] = useState('fr');
  const [translations, setTranslations] = useState({});
  const [originalTranslations, setOriginalTranslations] = useState({});
  const [loading, setLoading] = useState(true);
  const [saving, setSaving] = useState(false);
  const [searchTerm, setSearchTerm] = useState('');
  const [expandedSection, setExpandedSection] = useState(null);
  const [snackbar, setSnackbar] = useState({
    open: false,
    message: '',
    severity: 'success'
  });

  // Charger les traductions
  useEffect(() => {
    const fetchTranslations = async () => {
      setLoading(true);
      try {
        // Dans un environnement de production, cela serait un appel API
        // Mais pour cette démo, nous simulons le chargement des fichiers de traduction
        let data;
        if (language === 'fr') {
          data = await import('../../translations/fr.json');
        } else {
          data = await import('../../translations/ar.json');
        }
        setTranslations(JSON.parse(JSON.stringify(data)));
        setOriginalTranslations(JSON.parse(JSON.stringify(data)));
      } catch (error) {
        console.error('Erreur lors du chargement des traductions:', error);
        setSnackbar({
          open: true,
          message: 'Erreur lors du chargement des traductions',
          severity: 'error'
        });
      } finally {
        setLoading(false);
      }
    };

    fetchTranslations();
  }, [language]);

  // Gérer le changement de langue
  const handleLanguageChange = (event) => {
    setLanguage(event.target.value);
  };

  // Gérer le changement de valeur d'une traduction
  const handleTranslationChange = (section, key, value) => {
    setTranslations(prev => {
      const newTranslations = { ...prev };
      if (!newTranslations[section]) {
        newTranslations[section] = {};
      }
      newTranslations[section][key] = value;
      return newTranslations;
    });
  };

  // Gérer l'expansion d'une section
  const handleAccordionChange = (section) => (event, isExpanded) => {
    setExpandedSection(isExpanded ? section : null);
  };

  // Sauvegarder les modifications
  const handleSave = async () => {
    setSaving(true);
    try {
      // Dans un environnement de production, cela serait un appel API POST
      // Mais pour cette démo, nous simulons la sauvegarde
      await new Promise(resolve => setTimeout(resolve, 1000));

      // Mise à jour des fichiers de traduction (simulation)
      console.log('Traductions sauvegardées:', translations);

      setSnackbar({
        open: true,
        message: 'Traductions sauvegardées avec succès',
        severity: 'success'
      });

      // Mettre à jour les traductions originales après sauvegarde
      setOriginalTranslations(JSON.parse(JSON.stringify(translations)));
    } catch (error) {
      console.error('Erreur lors de la sauvegarde des traductions:', error);
      setSnackbar({
        open: true,
        message: 'Erreur lors de la sauvegarde des traductions',
        severity: 'error'
      });
    } finally {
      setSaving(false);
    }
  };

  // Ajouter une nouvelle clé de traduction
  const addTranslationKey = (section) => {
    const newKey = `new_key_${Date.now()}`;
    handleTranslationChange(section, newKey, '');
  };

  // Supprimer une clé de traduction
  const deleteTranslationKey = (section, key) => {
    setTranslations(prev => {
      const newTranslations = { ...prev };
      if (newTranslations[section]) {
        const sectionCopy = { ...newTranslations[section] };
        delete sectionCopy[key];
        newTranslations[section] = sectionCopy;
      }
      return newTranslations;
    });
  };

  // Ajouter une nouvelle section
  const addSection = () => {
    const newSection = `new_section_${Date.now()}`;
    setTranslations(prev => ({
      ...prev,
      [newSection]: {}
    }));
    setExpandedSection(newSection);
  };

  // Fermer la notification
  const handleCloseSnackbar = () => {
    setSnackbar(prev => ({ ...prev, open: false }));
  };

  // Filtrer les sections en fonction du terme de recherche
  const filteredSections = Object.keys(translations).filter(section => {
    if (!searchTerm) return true;

    // Vérifier si le nom de la section correspond
    if (section.toLowerCase().includes(searchTerm.toLowerCase())) return true;

    // Vérifier si une clé ou une valeur dans la section correspond
    return Object.entries(translations[section] || {}).some(
      ([key, value]) =>
        key.toLowerCase().includes(searchTerm.toLowerCase()) ||
        (typeof value === 'string' && value.toLowerCase().includes(searchTerm.toLowerCase()))
    );
  });

  // Vérifier s'il y a des modifications non sauvegardées
  const hasChanges = JSON.stringify(translations) !== JSON.stringify(originalTranslations);

  if (loading) {
    return (
      <Box sx={{ display: 'flex', justifyContent: 'center', alignItems: 'center', height: '100%', p: 4 }}>
        <CircularProgress />
      </Box>
    );
  }

  return (
    <Box sx={{ p: 3 }}>
      <Typography variant="h4" gutterBottom>
        {t('admin.translations')}
      </Typography>

      <Paper sx={{ mb: 3, p: 2 }}>
        <Grid container spacing={2} alignItems="center">
          <Grid item xs={12} md={4}>
            <FormControl fullWidth>
              <InputLabel id="language-select-label">Langue</InputLabel>
              <Select
                labelId="language-select-label"
                value={language}
                label="Langue"
                onChange={handleLanguageChange}
              >
                <MenuItem value="fr">Français</MenuItem>
                <MenuItem value="ar">العربية (Arabe)</MenuItem>
              </Select>
            </FormControl>
          </Grid>
          <Grid item xs={12} md={6}>
            <TextField
              fullWidth
              placeholder="Rechercher des traductions..."
              value={searchTerm}
              onChange={(e) => setSearchTerm(e.target.value)}
              InputProps={{
                startAdornment: (
                  <InputAdornment position="start">
                    <SearchIcon />
                  </InputAdornment>
                ),
              }}
            />
          </Grid>
          <Grid item xs={12} md={2}>
            <Button
              fullWidth
              variant="contained"
              color="primary"
              startIcon={<SaveIcon />}
              onClick={handleSave}
              disabled={saving || !hasChanges}
            >
              {saving ? 'Sauvegarde...' : 'Sauvegarder'}
            </Button>
          </Grid>
        </Grid>
      </Paper>

      <Box sx={{ mb: 2 }}>
        <Button
          variant="outlined"
          startIcon={<AddIcon />}
          onClick={addSection}
        >
          Ajouter une section
        </Button>
      </Box>

      {filteredSections.length === 0 ? (
        <Paper sx={{ p: 3, textAlign: 'center' }}>
          <Typography>Aucune traduction trouvée pour "{searchTerm}"</Typography>
        </Paper>
      ) : (
        filteredSections.map(section => (
          <Accordion
            key={section}
            expanded={expandedSection === section}
            onChange={handleAccordionChange(section)}
            sx={{ mb: 2 }}
          >
            <AccordionSummary expandIcon={<ExpandMoreIcon />}>
              <Typography variant="h6">{section}</Typography>
            </AccordionSummary>
            <AccordionDetails>
              <Box sx={{ mb: 2 }}>
                <Button
                  size="small"
                  startIcon={<AddIcon />}
                  onClick={() => addTranslationKey(section)}
                >
                  Ajouter une clé
                </Button>
              </Box>
              <Divider sx={{ mb: 2 }} />

              {Object.keys(translations[section] || {}).length === 0 ? (
                <Typography variant="body2" color="text.secondary" sx={{ fontStyle: 'italic' }}>
                  Aucune clé dans cette section. Ajoutez-en une avec le bouton ci-dessus.
                </Typography>
              ) : (
                Object.entries(translations[section] || {}).map(([key, value]) => (
                  <Grid container spacing={2} key={key} sx={{ mb: 2 }}>
                    <Grid item xs={12} md={3}>
                      <TextField
                        fullWidth
                        label="Clé"
                        value={key}
                        disabled
                        size="small"
                      />
                    </Grid>
                    <Grid item xs={12} md={8}>
                      <TextField
                        fullWidth
                        label="Valeur"
                        value={typeof value === 'string' ? value : JSON.stringify(value)}
                        onChange={(e) => handleTranslationChange(section, key, e.target.value)}
                        multiline={typeof value === 'string' && value.length > 50}
                        rows={typeof value === 'string' && value.length > 50 ? 3 : 1}
                        size="small"
                      />
                    </Grid>
                    <Grid item xs={12} md={1} sx={{ display: 'flex', alignItems: 'center' }}>
                      <IconButton
                        color="error"
                        onClick={() => deleteTranslationKey(section, key)}
                        size="small"
                      >
                        <DeleteIcon />
                      </IconButton>
                    </Grid>
                  </Grid>
                ))
              )}
            </AccordionDetails>
          </Accordion>
        ))
      )}

      <Snackbar
        open={snackbar.open}
        autoHideDuration={6000}
        onClose={handleCloseSnackbar}
        anchorOrigin={{ vertical: 'bottom', horizontal: 'right' }}
      >
        <Alert onClose={handleCloseSnackbar} severity={snackbar.severity} sx={{ width: '100%' }}>
          {snackbar.message}
        </Alert>
      </Snackbar>
    </Box>
  );
};

export default TranslationsManager;
