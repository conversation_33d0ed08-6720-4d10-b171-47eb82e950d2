class ChatThread {
  final int id;
  final List<int> participants;
  final DateTime lastMessageAt;
  final String lastMessageContent;

  ChatThread({
    required this.id,
    required this.participants,
    required this.lastMessageAt,
    required this.lastMessageContent,
  });

  factory ChatThread.fromJson(Map<String, dynamic> json) {
    return ChatThread(
      id: json["id"],
      participants: List<int>.from(json["participants"]),
      lastMessageAt: DateTime.parse(json["last_message_at"]),
      lastMessageContent: json["last_message_content"],
    );
  }

  Map<String, dynamic> toJson() {
    return {
      "id": id,
      "participants": participants,
      "last_message_at": lastMessageAt.toIso8601String(),
      "last_message_content": lastMessageContent,
    };
  }
}


