<!DOCTYPE html>
<html lang="fr">
  <head>
    <meta charset="UTF-8" />
    <link rel="icon" type="image/x-icon" href="/assets/favicon.ico" />
    <link rel="icon" type="image/png" sizes="32x32" href="/assets/favicon-32x32.png" />
    <link rel="icon" type="image/png" sizes="16x16" href="/assets/favicon-16x16.png" />
    <link rel="apple-touch-icon" href="/assets/favicon-32x32.png" />
    <link rel="manifest" href="/manifest.json" />
    <meta name="viewport" content="width=device-width, initial-scale=1.0" />
    <meta name="theme-color" content="#4CAF50" />
    <meta name="description" content="Application de gestion d'élevage de volailles" />
    <title>Poultray-dz - Gestion d'élevage de volailles</title>
    <link href="https://fonts.googleapis.com/css2?family=Inter:wght@300;400;500;600;700&display=swap" rel="stylesheet" />
  </head>
  <body>
    <noscript>You need to enable JavaScript to run this app.</noscript>
    <div id="root">
      <div style="padding: 20px; font-family: Arial, sans-serif;">
        <h2 style="color: orange;">🔄 Loading React App...</h2>
        <p>If you see this message for more than a few seconds, there might be a JavaScript loading issue.</p>
        <div id="debug-info" style="margin-top: 10px; padding: 10px; background: #f0f0f0; border-radius: 5px;">
          <strong>Debug Info:</strong><br>
          <span id="timestamp"></span>
        </div>
      </div>
    </div>

    <script>
      console.log('🔧 index.html: Inline script running');
      document.getElementById('timestamp').textContent = 'Page loaded at: ' + new Date().toLocaleString();

      // Check if the main script loads
      window.addEventListener('error', function(e) {
        console.error('❌ index.html: Script error:', e.error);
        document.getElementById('root').innerHTML += '<div style="color: red; margin-top: 10px;"><strong>Error:</strong> ' + e.message + '</div>';
      });

      // Set a timeout to check if React loaded
      setTimeout(function() {
        if (document.getElementById('root').innerHTML.includes('Loading React App')) {
          console.error('❌ index.html: React app did not load within 5 seconds');
          document.getElementById('root').innerHTML += '<div style="color: red; margin-top: 10px;"><strong>Timeout:</strong> React app did not load within 5 seconds</div>';
        }
      }, 5000);
    </script>

    <script type="module" src="/src/main.jsx"></script>
  </body>
</html>
