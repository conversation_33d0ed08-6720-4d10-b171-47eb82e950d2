import{r as o,j as e}from"./vendor.CTQIA7G6.js";import{s as D}from"./settingsService.DVLXOYtr.js";import{u as I}from"./index.WVN8qCy5.js";import{b as d,t as Y,q as B,a as c,W as E,al as g,G as n,K as i,an as F,e as x,bo as P,br as W,B as H,aT as N,V as q}from"./mui.D_tNY0b-.js";import"./firebase.BaqyMmVp.js";function X(){const{t,language:O,changeLanguage:Z}=I(),[a,j]=o.useState({siteName:"Poultray DZ",siteDescription:"",contactEmail:"",contactPhone:"",address:"",logo:"",favicon:"",primaryColor:"#2c5530",secondaryColor:"#e7eae2",defaultLanguage:"fr",availableLanguages:["fr","ar","en"],dateFormat:"DD/MM/YYYY",timeFormat:"HH:mm",timezone:"Africa/Algiers",maintenanceMode:!1,maintenanceMessage:"",allowUserRegistration:!0,defaultUserRole:"user",footerText:"© Poultray DZ",maxUploadSize:5,socialLinks:{}}),[w,v]=o.useState(!0),[f,b]=o.useState(!1),[y,u]=o.useState(""),[S,C]=o.useState(""),[m,h]=o.useState({open:!1,message:"",severity:"info"});o.useEffect(()=>{(async()=>{try{v(!0);const l=await D.fetchGeneralSettings();l&&j(p=>({...p,...l})),u("")}catch(l){u(l.response?.data?.message||t("errors.fetchingGeneralSettings","Error fetching general settings")),console.error("Error fetchGeneralSettings:",l)}finally{v(!1)}})()},[t]);const r=s=>{const{name:l,value:p,type:M,checked:U}=s.target;j(R=>({...R,[l]:M==="checkbox"||M==="switch"?U:p}))},A=async s=>{s.preventDefault(),b(!0),u(""),C("");try{const l=await D.updateGeneralSettings(a);C(t("success.generalSettingsSaved","General settings saved successfully!")),h({open:!0,message:t("success.generalSettingsSaved","General settings saved successfully!"),severity:"success"})}catch(l){u(l.response?.data?.message||t("errors.savingGeneralSettings","Error saving general settings")),h({open:!0,message:l.response?.data?.message||t("errors.savingGeneralSettings","Error saving general settings"),severity:"error"}),console.error("Error handleSubmit:",l)}finally{b(!1)}},L=()=>{h({...m,open:!1})},T=["Africa/Algiers","Europe/Paris","Europe/London","America/New_York","Asia/Dubai","Asia/Tokyo","Australia/Sydney"],k=["DD/MM/YYYY","MM/DD/YYYY","YYYY-MM-DD","DD-MM-YYYY","DD.MM.YYYY"],z=["HH:mm","hh:mm A","HH:mm:ss"],G=[{code:"fr",name:"Français"},{code:"ar",name:"العربية"},{code:"en",name:"English"}];return w?e.jsx(d,{display:"flex",justifyContent:"center",alignItems:"center",minHeight:"200px",children:e.jsx(Y,{})}):e.jsxs(B,{elevation:3,sx:{p:4,m:2},children:[e.jsx(c,{variant:"h5",gutterBottom:!0,sx:{mb:3},children:t("settings.general.title","General Settings")}),y&&e.jsx(E,{severity:"error",sx:{mb:2},children:y}),S&&e.jsx(E,{severity:"success",sx:{mb:2},children:S}),e.jsxs("form",{onSubmit:A,children:[e.jsx(c,{variant:"h6",gutterBottom:!0,sx:{mt:2},children:t("settings.general.siteIdentity","Site Identity")}),e.jsx(g,{sx:{mb:3}}),e.jsxs(n,{container:!0,spacing:3,children:[e.jsx(n,{item:!0,xs:12,sm:6,children:e.jsx(i,{fullWidth:!0,label:t("settings.general.siteName","Site Name"),name:"siteName",value:a.siteName,onChange:r,variant:"outlined",required:!0})}),e.jsx(n,{item:!0,xs:12,sm:6,children:e.jsx(i,{fullWidth:!0,label:t("settings.general.siteDescription","Site Description"),name:"siteDescription",value:a.siteDescription,onChange:r,variant:"outlined",multiline:!0,rows:2})}),e.jsx(n,{item:!0,xs:12,sm:6,children:e.jsx(i,{fullWidth:!0,label:t("settings.general.contactEmail","Contact Email"),name:"contactEmail",value:a.contactEmail,onChange:r,variant:"outlined",type:"email"})}),e.jsx(n,{item:!0,xs:12,sm:6,children:e.jsx(i,{fullWidth:!0,label:t("settings.general.contactPhone","Contact Phone"),name:"contactPhone",value:a.contactPhone,onChange:r,variant:"outlined"})}),e.jsx(n,{item:!0,xs:12,children:e.jsx(i,{fullWidth:!0,label:t("settings.general.address","Address"),name:"address",value:a.address,onChange:r,variant:"outlined",multiline:!0,rows:2})})]}),e.jsx(c,{variant:"h6",gutterBottom:!0,sx:{mt:4},children:t("settings.general.appearance","Appearance")}),e.jsx(g,{sx:{mb:3}}),e.jsxs(n,{container:!0,spacing:3,children:[e.jsx(n,{item:!0,xs:12,sm:6,children:e.jsx(i,{fullWidth:!0,label:t("settings.general.logo","Logo URL"),name:"logo",value:a.logo,onChange:r,variant:"outlined",placeholder:"https://example.com/logo.png"})}),e.jsx(n,{item:!0,xs:12,sm:6,children:e.jsx(i,{fullWidth:!0,label:t("settings.general.favicon","Favicon URL"),name:"favicon",value:a.favicon,onChange:r,variant:"outlined",placeholder:"https://example.com/favicon.ico"})}),e.jsx(n,{item:!0,xs:12,sm:6,children:e.jsx(i,{fullWidth:!0,label:t("settings.general.primaryColor","Primary Color"),name:"primaryColor",value:a.primaryColor,onChange:r,variant:"outlined",InputProps:{startAdornment:e.jsx(F,{position:"start",children:e.jsx(d,{sx:{width:20,height:20,backgroundColor:a.primaryColor,borderRadius:"4px",border:"1px solid #ccc"}})})}})}),e.jsx(n,{item:!0,xs:12,sm:6,children:e.jsx(i,{fullWidth:!0,label:t("settings.general.secondaryColor","Secondary Color"),name:"secondaryColor",value:a.secondaryColor,onChange:r,variant:"outlined",InputProps:{startAdornment:e.jsx(F,{position:"start",children:e.jsx(d,{sx:{width:20,height:20,backgroundColor:a.secondaryColor,borderRadius:"4px",border:"1px solid #ccc"}})})}})}),e.jsx(n,{item:!0,xs:12,sm:6,children:e.jsx(i,{fullWidth:!0,label:t("settings.general.footerText","Footer Text"),name:"footerText",value:a.footerText,onChange:r,variant:"outlined"})}),e.jsx(n,{item:!0,xs:12,sm:6,children:e.jsx(i,{fullWidth:!0,label:t("settings.general.maxUploadSize","Max Upload Size (MB)"),name:"maxUploadSize",value:a.maxUploadSize,onChange:r,variant:"outlined",type:"number",InputProps:{inputProps:{min:1,max:100}}})})]}),e.jsx(c,{variant:"h6",gutterBottom:!0,sx:{mt:4},children:t("settings.general.localization","Localization")}),e.jsx(g,{sx:{mb:3}}),e.jsxs(n,{container:!0,spacing:3,children:[e.jsx(n,{item:!0,xs:12,sm:6,children:e.jsx(i,{select:!0,fullWidth:!0,label:t("settings.general.defaultLanguage","Default Language"),name:"defaultLanguage",value:a.defaultLanguage,onChange:r,variant:"outlined",children:G.map(s=>e.jsx(x,{value:s.code,children:s.name},s.code))})}),e.jsx(n,{item:!0,xs:12,sm:6,children:e.jsx(i,{select:!0,fullWidth:!0,label:t("settings.general.timezone","Timezone"),name:"timezone",value:a.timezone,onChange:r,variant:"outlined",children:T.map(s=>e.jsx(x,{value:s,children:s},s))})}),e.jsx(n,{item:!0,xs:12,sm:6,children:e.jsx(i,{select:!0,fullWidth:!0,label:t("settings.general.dateFormat","Date Format"),name:"dateFormat",value:a.dateFormat,onChange:r,variant:"outlined",children:k.map(s=>e.jsx(x,{value:s,children:s},s))})}),e.jsx(n,{item:!0,xs:12,sm:6,children:e.jsx(i,{select:!0,fullWidth:!0,label:t("settings.general.timeFormat","Time Format"),name:"timeFormat",value:a.timeFormat,onChange:r,variant:"outlined",children:z.map(s=>e.jsx(x,{value:s,children:s},s))})})]}),e.jsx(c,{variant:"h6",gutterBottom:!0,sx:{mt:4},children:t("settings.general.siteOperation","Site Operation")}),e.jsx(g,{sx:{mb:3}}),e.jsxs(n,{container:!0,spacing:3,children:[e.jsx(n,{item:!0,xs:12,sm:6,children:e.jsx(P,{control:e.jsx(W,{checked:a.maintenanceMode,onChange:r,name:"maintenanceMode",color:"warning"}),label:t("settings.general.maintenanceMode","Maintenance Mode")})}),e.jsx(n,{item:!0,xs:12,sm:6,children:e.jsx(P,{control:e.jsx(W,{checked:a.allowUserRegistration,onChange:r,name:"allowUserRegistration",color:"primary"}),label:t("settings.general.allowRegistration","Allow User Registration")})}),a.maintenanceMode&&e.jsx(n,{item:!0,xs:12,children:e.jsx(i,{fullWidth:!0,label:t("settings.general.maintenanceMessage","Maintenance Message"),name:"maintenanceMessage",value:a.maintenanceMessage,onChange:r,variant:"outlined",multiline:!0,rows:2,placeholder:t("settings.general.maintenanceMessagePlaceholder","Site under maintenance. Please check back later.")})})]}),e.jsx(d,{sx:{mt:4,display:"flex",justifyContent:"flex-end"},children:e.jsx(H,{type:"submit",variant:"contained",color:"primary",startIcon:e.jsx(N,{}),disabled:f,children:f?e.jsx(Y,{size:24}):t("actions.saveSettings","Save Settings")})})]}),e.jsx(q,{open:m.open,autoHideDuration:6e3,onClose:L,message:m.message,severity:m.severity})]})}export{X as default};
//# sourceMappingURL=GeneralSettings.C4Na4802.js.map
