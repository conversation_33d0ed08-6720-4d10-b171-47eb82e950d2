Write-Host "[1/4] Cleaning project..."

# Remove node_modules and lock files
if (Test-Path "node_modules") {
    Remove-Item -Recurse -Force "node_modules"
}
if (Test-Path "package-lock.json") {
    Remove-Item -Force "package-lock.json"
}

# Clear Vite cache
if (Test-Path "node_modules/.vite") {
    Remove-Item -Recurse -Force "node_modules/.vite"
}

# Clear dist folder
if (Test-Path "dist") {
    Remove-Item -Recurse -Force "dist"
}

Write-Host "[2/4] Installing dependencies..."
npm install

Write-Host "[3/4] Building project..."
npm run build

Write-Host "[4/4] Starting dev server..."
npm run dev
