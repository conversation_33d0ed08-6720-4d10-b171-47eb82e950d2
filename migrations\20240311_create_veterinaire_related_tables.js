'use strict';

module.exports = {
  up: async (queryInterface, Sequelize) => {
    // Create consultations table
    await queryInterface.createTable('consultations', {
      id: {
        type: Sequelize.INTEGER,
        primaryKey: true,
        autoIncrement: true
      },
      veterinaire_id: {
        type: Sequelize.INTEGER,
        allowNull: false,
        references: {
          model: 'veterinaires',
          key: 'id'
        },
        onUpdate: 'CASCADE',
        onDelete: 'CASCADE'
      },
      eleveur_id: {
        type: Sequelize.INTEGER,
        allowNull: false,
        references: {
          model: 'users',
          key: 'id'
        }
      },
      date: {
        type: Sequelize.DATE,
        allowNull: false
      },
      symptomes: {
        type: Sequelize.TEXT,
        allowNull: true
      },
      diagnostic: {
        type: Sequelize.TEXT,
        allowNull: true
      },
      statut: {
        type: Sequelize.ENUM('programmee', 'en_cours', 'terminee', 'annulee'),
        defaultValue: 'programmee'
      },
      notes: {
        type: Sequelize.TEXT,
        allowNull: true
      },
      created_at: {
        type: Sequelize.DATE,
        allowNull: false,
        defaultValue: Sequelize.literal('CURRENT_TIMESTAMP')
      },
      updated_at: {
        type: Sequelize.DATE,
        allowNull: false,
        defaultValue: Sequelize.literal('CURRENT_TIMESTAMP')
      }
    });

    // Create prescriptions table
    await queryInterface.createTable('prescriptions', {
      id: {
        type: Sequelize.INTEGER,
        primaryKey: true,
        autoIncrement: true
      },
      veterinaire_id: {
        type: Sequelize.INTEGER,
        allowNull: false,
        references: {
          model: 'veterinaires',
          key: 'id'
        }
      },
      eleveur_id: {
        type: Sequelize.INTEGER,
        allowNull: false,
        references: {
          model: 'users',
          key: 'id'
        }
      },
      consultation_id: {
        type: Sequelize.INTEGER,
        allowNull: true,
        references: {
          model: 'consultations',
          key: 'id'
        }
      },
      medicament: {
        type: Sequelize.STRING,
        allowNull: false
      },
      dosage: {
        type: Sequelize.STRING,
        allowNull: false
      },
      instructions: {
        type: Sequelize.TEXT,
        allowNull: false
      },
      duree_traitement: {
        type: Sequelize.INTEGER,
        allowNull: true
      },
      created_at: {
        type: Sequelize.DATE,
        allowNull: false,
        defaultValue: Sequelize.literal('CURRENT_TIMESTAMP')
      },
      updated_at: {
        type: Sequelize.DATE,
        allowNull: false,
        defaultValue: Sequelize.literal('CURRENT_TIMESTAMP')
      }
    });

    // Create volailles table
    await queryInterface.createTable('volailles', {
      id: {
        type: Sequelize.INTEGER,
        primaryKey: true,
        autoIncrement: true
      },
      eleveur_id: {
        type: Sequelize.INTEGER,
        allowNull: false,
        references: {
          model: 'users',
          key: 'id'
        }
      },
      type: {
        type: Sequelize.STRING,
        allowNull: false
      },
      nombre: {
        type: Sequelize.INTEGER,
        allowNull: false
      },
      age: {
        type: Sequelize.INTEGER,
        allowNull: true
      },
      description: {
        type: Sequelize.TEXT,
        allowNull: true
      },
      created_at: {
        type: Sequelize.DATE,
        allowNull: false,
        defaultValue: Sequelize.literal('CURRENT_TIMESTAMP')
      },
      updated_at: {
        type: Sequelize.DATE,
        allowNull: false,
        defaultValue: Sequelize.literal('CURRENT_TIMESTAMP')
      }
    });

    // Create suivi_veterinaire table
    await queryInterface.createTable('suivi_veterinaire', {
      id: {
        type: Sequelize.INTEGER,
        primaryKey: true,
        autoIncrement: true
      },
      veterinaire_id: {
        type: Sequelize.INTEGER,
        allowNull: false,
        references: {
          model: 'veterinaires',
          key: 'id'
        }
      },
      volaille_id: {
        type: Sequelize.INTEGER,
        allowNull: false,
        references: {
          model: 'volailles',
          key: 'id'
        }
      },
      date_visite: {
        type: Sequelize.DATE,
        allowNull: false
      },
      statut: {
        type: Sequelize.ENUM('planifie', 'en_cours', 'termine', 'annule'),
        defaultValue: 'planifie'
      },
      observations: {
        type: Sequelize.TEXT,
        allowNull: true
      },
      recommandations: {
        type: Sequelize.TEXT,
        allowNull: true
      },
      created_at: {
        type: Sequelize.DATE,
        allowNull: false,
        defaultValue: Sequelize.literal('CURRENT_TIMESTAMP')
      },
      updated_at: {
        type: Sequelize.DATE,
        allowNull: false,
        defaultValue: Sequelize.literal('CURRENT_TIMESTAMP')
      }
    });

    // Create indexes
    await queryInterface.addIndex('consultations', ['veterinaire_id']);
    await queryInterface.addIndex('consultations', ['eleveur_id']);
    await queryInterface.addIndex('consultations', ['date']);
    await queryInterface.addIndex('consultations', ['statut']);

    await queryInterface.addIndex('prescriptions', ['veterinaire_id']);
    await queryInterface.addIndex('prescriptions', ['eleveur_id']);
    await queryInterface.addIndex('prescriptions', ['consultation_id']);

    await queryInterface.addIndex('volailles', ['eleveur_id']);
    await queryInterface.addIndex('volailles', ['type']);

    await queryInterface.addIndex('suivi_veterinaire', ['veterinaire_id']);
    await queryInterface.addIndex('suivi_veterinaire', ['volaille_id']);
    await queryInterface.addIndex('suivi_veterinaire', ['date_visite']);
    await queryInterface.addIndex('suivi_veterinaire', ['statut']);
  },

  down: async (queryInterface, Sequelize) => {
    await queryInterface.dropTable('suivi_veterinaire');
    await queryInterface.dropTable('prescriptions');
    await queryInterface.dropTable('volailles');
    await queryInterface.dropTable('consultations');
  }
};
