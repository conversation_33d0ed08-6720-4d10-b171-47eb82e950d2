import 'package:flutter/material.dart';
import 'package:provider/provider.dart';
import 'package:flutter_localizations/flutter_localizations.dart';
import 'package:mobile_manus/l10n/app_localizations.dart';
import 'package:mobile_manus/providers/auth_provider.dart';
import 'package:mobile_manus/screens/auth/login_screen.dart';
import 'package:mobile_manus/screens/home/<USER>';
import 'package:mobile_manus/services/sync/sync_service.dart';

void main() async {
  WidgetsFlutterBinding.ensureInitialized();
  final syncService = SyncService();
  await syncService.initializeWorkmanager();
  await syncService.registerFeedSyncTask();

  runApp(
    ChangeNotifierProvider(
      create: (context) => AuthProvider(),
      child: const MyApp(),
    ),
  );
}

class MyApp extends StatelessWidget {
  const MyApp({super.key});

  @override
  Widget build(BuildContext context) {
    return MaterialApp(
      title: 'Poultry DZ',
      localizationsDelegates: const [
        AppLocalizations.delegate,
        GlobalMaterialLocalizations.delegate,
        GlobalWidgetsLocalizations.delegate,
        GlobalCupertinoLocalizations.delegate,
      ],
      supportedLocales: const [
        Locale('en'), // English
        Locale('fr'), // French
        Locale('ar'), // Arabic
      ],
      home: Consumer<AuthProvider>(
        builder: (context, auth, child) {
          if (auth.isAuthenticated) {
            return const HomeScreen();
          } else {
            return const LoginScreen();
          }
        },
      ),
    );
  }
}


