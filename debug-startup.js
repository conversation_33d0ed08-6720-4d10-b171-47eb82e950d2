console.log('1. Starting debug script...');

try {
  console.log('2. Loading dotenv...');
  require('dotenv').config();
  console.log('3. Dotenv loaded successfully');
  
  console.log('4. Loading express...');
  const express = require('express');
  console.log('5. Express loaded successfully');
  
  console.log('6. Creating express app...');
  const app = express();
  console.log('7. Express app created successfully');
  
  console.log('8. Setting up basic middleware...');
  app.use(express.json());
  console.log('9. Basic middleware set up successfully');
  
  console.log('10. Setting up test route...');
  app.get('/test', (req, res) => {
    res.json({ message: 'Test route working' });
  });
  console.log('11. Test route set up successfully');
  
  console.log('12. Getting port and host...');
  const port = process.env.PORT || 3005;
  const host = process.env.HOST || 'localhost';
  console.log(`13. Port: ${port}, Host: ${host}`);
  
  console.log('14. Starting server...');
  const server = app.listen(port, host, () => {
    console.log(`✅ Debug server running on http://${host}:${port}`);
  });
  
  server.on('error', (err) => {
    console.error('❌ Server error:', err);
  });
  
} catch (error) {
  console.error('❌ Error during startup:', error);
  process.exit(1);
}
