class UserRating {
  final int id;
  final int raterId;
  final int ratedId;
  final int transactionId;
  final double rating;
  final String? feedback;
  final DateTime createdAt;

  UserRating({
    required this.id,
    required this.raterId,
    required this.ratedId,
    required this.transactionId,
    required this.rating,
    this.feedback,
    required this.createdAt,
  });

  factory UserRating.fromJson(Map<String, dynamic> json) {
    return UserRating(
      id: json["id"],
      raterId: json["rater_id"],
      ratedId: json["rated_id"],
      transactionId: json["transaction_id"],
      rating: json["rating"].toDouble(),
      feedback: json["feedback"],
      createdAt: DateTime.parse(json["created_at"]),
    );
  }

  Map<String, dynamic> toJson() {
    return {
      "id": id,
      "rater_id": raterId,
      "rated_id": ratedId,
      "transaction_id": transactionId,
      "rating": rating,
      "feedback": feedback,
      "created_at": createdAt.toIso8601String(),
    };
  }
}


