import { createContext, useState, useContext, useEffect } from 'react';
import frTranslations from '../translations/fr.json';
import arTranslations from '../translations/ar.json';

const LanguageContext = createContext();

export const useLanguage = () => useContext(LanguageContext);

export const LanguageProvider = ({ children }) => {
  const [language, setLanguage] = useState(localStorage.getItem('language') || 'fr');
  const [translations, setTranslations] = useState(language === 'ar' ? arTranslations : frTranslations);
  const [direction, setDirection] = useState(language === 'ar' ? 'rtl' : 'ltr');

  useEffect(() => {
    // Update translations when language changes
    setTranslations(language === 'ar' ? arTranslations : frTranslations);

    // Update text direction
    setDirection(language === 'ar' ? 'rtl' : 'ltr');

    // Save language preference to localStorage
    localStorage.setItem('language', language);

    // Update document direction
    document.documentElement.dir = language === 'ar' ? 'rtl' : 'ltr';

    // Update lang attribute
    document.documentElement.lang = language;
  }, [language]);

  // Function to change language
  const changeLanguage = (lang) => {
    if (lang === 'fr' || lang === 'ar') {
      setLanguage(lang);
    }
  };

  // Function to translate a key
  const t = (key) => {
    const keys = key.split('.');
    let result = translations;

    for (const k of keys) {
      if (result && result[k]) {
        result = result[k];
      } else {
        // Return the key if translation not found
        return key;
      }
    }

    return result;
  };

  const value = {
    language,
    changeLanguage,
    t,
    direction,
    isRTL: language === 'ar',
  };

  return <LanguageContext.Provider value={value}>{children}</LanguageContext.Provider>;
};

export default LanguageContext;
