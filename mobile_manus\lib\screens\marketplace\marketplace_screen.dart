import 'package:flutter/material.dart';
import 'package:provider/provider.dart';
import 'package:mobile_manus/providers/marketplace_provider.dart';
import 'package:mobile_manus/models/market_listing.dart';

class MarketplaceScreen extends StatefulWidget {
  const MarketplaceScreen({super.key});

  @override
  State<MarketplaceScreen> createState() => _MarketplaceScreenState();
}

class _MarketplaceScreenState extends State<MarketplaceScreen>
    with SingleTickerProviderStateMixin {
  late TabController _tabController;
  String _selectedCategory = 'Tous';
  String _sortBy = 'recent';

  final List<String> _categories = [
    'Tous',
    'Poulets',
    'Dindes',
    'C<PERSON>les',
    'Œufs',
    'Aliments',
    'Équipements',
  ];

  @override
  void initState() {
    super.initState();
    _tabController = TabController(length: 3, vsync: this);
    
    // Load marketplace data when screen initializes
    WidgetsBinding.instance.addPostFrameCallback((_) {
      Provider.of<MarketplaceProvider>(context, listen: false).loadListings();
    });
  }

  @override
  void dispose() {
    _tabController.dispose();
    super.dispose();
  }

  @override
  Widget build(BuildContext context) {
    return Scaffold(
      appBar: AppBar(
        title: const Text('Marketplace'),
        backgroundColor: Colors.purple[700],
        foregroundColor: Colors.white,
        actions: [
          IconButton(
            icon: const Icon(Icons.search),
            onPressed: () {
              _showSearchDialog();
            },
          ),
          IconButton(
            icon: const Icon(Icons.filter_list),
            onPressed: () {
              _showFilterDialog();
            },
          ),
        ],
        bottom: TabBar(
          controller: _tabController,
          labelColor: Colors.white,
          unselectedLabelColor: Colors.white70,
          indicatorColor: Colors.white,
          tabs: const [
            Tab(text: 'Parcourir', icon: Icon(Icons.store)),
            Tab(text: 'Mes annonces', icon: Icon(Icons.list_alt)),
            Tab(text: 'Favoris', icon: Icon(Icons.favorite)),
          ],
        ),
      ),
      body: TabBarView(
        controller: _tabController,
        children: [
          _buildBrowseTab(),
          _buildMyListingsTab(),
          _buildFavoritesTab(),
        ],
      ),
      floatingActionButton: FloatingActionButton(
        onPressed: () {
          _showCreateListingDialog();
        },
        backgroundColor: Colors.purple[700],
        child: const Icon(Icons.add, color: Colors.white),
      ),
    );
  }

  Widget _buildBrowseTab() {
    return Consumer<MarketplaceProvider>(
      builder: (context, marketplaceProvider, child) {
        return Column(
          children: [
            // Category Filter
            Container(
              height: 50,
              padding: const EdgeInsets.symmetric(vertical: 8),
              child: ListView.builder(
                scrollDirection: Axis.horizontal,
                itemCount: _categories.length,
                itemBuilder: (context, index) {
                  final category = _categories[index];
                  final isSelected = category == _selectedCategory;
                  
                  return Padding(
                    padding: const EdgeInsets.symmetric(horizontal: 4),
                    child: FilterChip(
                      label: Text(category),
                      selected: isSelected,
                      onSelected: (selected) {
                        setState(() {
                          _selectedCategory = category;
                        });
                      },
                      selectedColor: Colors.purple[100],
                      checkmarkColor: Colors.purple[700],
                    ),
                  );
                },
              ),
            ),
            
            // Listings Grid
            Expanded(
              child: RefreshIndicator(
                onRefresh: () async {
                  await marketplaceProvider.loadListings();
                },
                child: GridView.builder(
                  padding: const EdgeInsets.all(16),
                  gridDelegate: const SliverGridDelegateWithFixedCrossAxisCount(
                    crossAxisCount: 2,
                    crossAxisSpacing: 8,
                    mainAxisSpacing: 8,
                    childAspectRatio: 0.75,
                  ),
                  itemCount: _getFilteredListings().length,
                  itemBuilder: (context, index) {
                    final listing = _getFilteredListings()[index];
                    return _buildListingCard(listing);
                  },
                ),
              ),
            ),
          ],
        );
      },
    );
  }

  Widget _buildMyListingsTab() {
    return Consumer<MarketplaceProvider>(
      builder: (context, marketplaceProvider, child) {
        return RefreshIndicator(
          onRefresh: () async {
            await marketplaceProvider.loadListings();
          },
          child: ListView(
            padding: const EdgeInsets.all(16),
            children: [
              // Stats Cards
              Row(
                children: [
                  Expanded(
                    child: _buildStatCard(
                      'Annonces actives',
                      '8',
                      Icons.store,
                      Colors.green,
                    ),
                  ),
                  const SizedBox(width: 8),
                  Expanded(
                    child: _buildStatCard(
                      'Vues totales',
                      '245',
                      Icons.visibility,
                      Colors.blue,
                    ),
                  ),
                ],
              ),
              
              const SizedBox(height: 16),
              
              // My Listings
              Text(
                'Mes annonces',
                style: Theme.of(context).textTheme.titleLarge,
              ),
              const SizedBox(height: 8),
              
              ..._getMyListings().map((listing) => _buildMyListingCard(listing)),
            ],
          ),
        );
      },
    );
  }

  Widget _buildFavoritesTab() {
    return Consumer<MarketplaceProvider>(
      builder: (context, marketplaceProvider, child) {
        return RefreshIndicator(
          onRefresh: () async {
            await marketplaceProvider.loadListings();
          },
          child: GridView.builder(
            padding: const EdgeInsets.all(16),
            gridDelegate: const SliverGridDelegateWithFixedCrossAxisCount(
              crossAxisCount: 2,
              crossAxisSpacing: 8,
              mainAxisSpacing: 8,
              childAspectRatio: 0.75,
            ),
            itemCount: _getFavoriteListings().length,
            itemBuilder: (context, index) {
              final listing = _getFavoriteListings()[index];
              return _buildListingCard(listing);
            },
          ),
        );
      },
    );
  }

  Widget _buildListingCard(MarketListing listing) {
    return Card(
      clipBehavior: Clip.antiAlias,
      child: InkWell(
        onTap: () {
          _showListingDetails(listing);
        },
        child: Column(
          crossAxisAlignment: CrossAxisAlignment.start,
          children: [
            // Image placeholder
            Container(
              height: 120,
              width: double.infinity,
              decoration: BoxDecoration(
                color: Colors.grey[300],
                image: listing.imageUrl != null
                    ? DecorationImage(
                        image: NetworkImage(listing.imageUrl!),
                        fit: BoxFit.cover,
                      )
                    : null,
              ),
              child: listing.imageUrl == null
                  ? Icon(
                      _getCategoryIcon(listing.category),
                      size: 40,
                      color: Colors.grey[600],
                    )
                  : null,
            ),
            
            Expanded(
              child: Padding(
                padding: const EdgeInsets.all(8),
                child: Column(
                  crossAxisAlignment: CrossAxisAlignment.start,
                  children: [
                    Text(
                      listing.title,
                      style: Theme.of(context).textTheme.titleSmall,
                      maxLines: 2,
                      overflow: TextOverflow.ellipsis,
                    ),
                    const SizedBox(height: 4),
                    Text(
                      '${listing.price.toStringAsFixed(0)} DA',
                      style: Theme.of(context).textTheme.titleMedium?.copyWith(
                        color: Colors.purple[700],
                        fontWeight: FontWeight.bold,
                      ),
                    ),
                    const Spacer(),
                    Row(
                      children: [
                        Icon(
                          Icons.location_on,
                          size: 12,
                          color: Colors.grey[600],
                        ),
                        const SizedBox(width: 2),
                        Expanded(
                          child: Text(
                            listing.location ?? 'Non spécifié',
                            style: Theme.of(context).textTheme.bodySmall,
                            overflow: TextOverflow.ellipsis,
                          ),
                        ),
                      ],
                    ),
                  ],
                ),
              ),
            ),
          ],
        ),
      ),
    );
  }

  Widget _buildMyListingCard(MarketListing listing) {
    return Card(
      child: ListTile(
        leading: Container(
          width: 60,
          height: 60,
          decoration: BoxDecoration(
            color: Colors.grey[300],
            borderRadius: BorderRadius.circular(8),
            image: listing.imageUrl != null
                ? DecorationImage(
                    image: NetworkImage(listing.imageUrl!),
                    fit: BoxFit.cover,
                  )
                : null,
          ),
          child: listing.imageUrl == null
              ? Icon(
                  _getCategoryIcon(listing.category),
                  color: Colors.grey[600],
                )
              : null,
        ),
        title: Text(listing.title),
        subtitle: Column(
          crossAxisAlignment: CrossAxisAlignment.start,
          children: [
            Text('${listing.price.toStringAsFixed(0)} DA'),
            Text(
              '${listing.views} vues',
              style: TextStyle(
                color: Colors.grey[600],
                fontSize: 12,
              ),
            ),
          ],
        ),
        trailing: PopupMenuButton(
          itemBuilder: (context) => [
            const PopupMenuItem(
              value: 'edit',
              child: Row(
                children: [
                  Icon(Icons.edit),
                  SizedBox(width: 8),
                  Text('Modifier'),
                ],
              ),
            ),
            const PopupMenuItem(
              value: 'promote',
              child: Row(
                children: [
                  Icon(Icons.trending_up),
                  SizedBox(width: 8),
                  Text('Promouvoir'),
                ],
              ),
            ),
            const PopupMenuItem(
              value: 'delete',
              child: Row(
                children: [
                  Icon(Icons.delete, color: Colors.red),
                  SizedBox(width: 8),
                  Text('Supprimer', style: TextStyle(color: Colors.red)),
                ],
              ),
            ),
          ],
          onSelected: (value) {
            _handleListingAction(value.toString(), listing);
          },
        ),
      ),
    );
  }

  Widget _buildStatCard(String title, String value, IconData icon, Color color) {
    return Card(
      child: Padding(
        padding: const EdgeInsets.all(16),
        child: Column(
          children: [
            Icon(icon, color: color, size: 32),
            const SizedBox(height: 8),
            Text(
              value,
              style: Theme.of(context).textTheme.titleLarge?.copyWith(
                fontWeight: FontWeight.bold,
                color: color,
              ),
            ),
            Text(
              title,
              style: Theme.of(context).textTheme.bodySmall,
              textAlign: TextAlign.center,
            ),
          ],
        ),
      ),
    );
  }

  IconData _getCategoryIcon(String category) {
    switch (category.toLowerCase()) {
      case 'poulets':
      case 'dindes':
      case 'cailles':
        return Icons.pets;
      case 'œufs':
        return Icons.egg;
      case 'aliments':
        return Icons.grass;
      case 'équipements':
        return Icons.build;
      default:
        return Icons.store;
    }
  }

  List<MarketListing> _getFilteredListings() {
    // Mock data for demonstration
    return [
      MarketListing(
        id: 1,
        title: 'Lot de 100 poulets de chair',
        description: 'Poulets de chair de 45 jours, race Cobb500',
        category: 'Poulets',
        price: 45000,
        location: 'Alger',
        views: 25,
        imageUrl: null,
      ),
      MarketListing(
        id: 2,
        title: 'Aliment pondeuse premium 50kg',
        description: 'Aliment complet pour poules pondeuses',
        category: 'Aliments',
        price: 2500,
        location: 'Blida',
        views: 18,
        imageUrl: null,
      ),
      MarketListing(
        id: 3,
        title: 'Abreuvoir automatique',
        description: 'Système d\'abreuvement automatique pour 500 volailles',
        category: 'Équipements',
        price: 15000,
        location: 'Oran',
        views: 12,
        imageUrl: null,
      ),
      MarketListing(
        id: 4,
        title: 'Œufs frais bio',
        description: 'Œufs de poules élevées au sol, alimentation bio',
        category: 'Œufs',
        price: 350,
        location: 'Sétif',
        views: 31,
        imageUrl: null,
      ),
    ].where((listing) {
      if (_selectedCategory == 'Tous') return true;
      return listing.category == _selectedCategory;
    }).toList();
  }

  List<MarketListing> _getMyListings() {
    // Mock data for user's listings
    return [
      MarketListing(
        id: 5,
        title: 'Mes poulets fermiers',
        description: 'Poulets élevés en plein air',
        category: 'Poulets',
        price: 800,
        location: 'Ma ferme',
        views: 45,
        imageUrl: null,
      ),
      MarketListing(
        id: 6,
        title: 'Œufs de mes poules',
        description: 'Œufs frais de poules heureuses',
        category: 'Œufs',
        price: 300,
        location: 'Ma ferme',
        views: 23,
        imageUrl: null,
      ),
    ];
  }

  List<MarketListing> _getFavoriteListings() {
    // Mock data for favorite listings
    return _getFilteredListings().take(2).toList();
  }

  void _showSearchDialog() {
    showDialog(
      context: context,
      builder: (BuildContext context) {
        return AlertDialog(
          title: const Text('Rechercher'),
          content: const TextField(
            decoration: InputDecoration(
              hintText: 'Que recherchez-vous ?',
              prefixIcon: Icon(Icons.search),
            ),
          ),
          actions: [
            TextButton(
              onPressed: () => Navigator.pop(context),
              child: const Text('Annuler'),
            ),
            ElevatedButton(
              onPressed: () {
                Navigator.pop(context);
                // TODO: Implement search
              },
              child: const Text('Rechercher'),
            ),
          ],
        );
      },
    );
  }

  void _showFilterDialog() {
    showDialog(
      context: context,
      builder: (BuildContext context) {
        return AlertDialog(
          title: const Text('Filtres'),
          content: Column(
            mainAxisSize: MainAxisSize.min,
            children: [
              ListTile(
                title: const Text('Prix croissant'),
                leading: Radio<String>(
                  value: 'price_asc',
                  groupValue: _sortBy,
                  onChanged: (value) {
                    setState(() {
                      _sortBy = value!;
                    });
                  },
                ),
              ),
              ListTile(
                title: const Text('Prix décroissant'),
                leading: Radio<String>(
                  value: 'price_desc',
                  groupValue: _sortBy,
                  onChanged: (value) {
                    setState(() {
                      _sortBy = value!;
                    });
                  },
                ),
              ),
              ListTile(
                title: const Text('Plus récent'),
                leading: Radio<String>(
                  value: 'recent',
                  groupValue: _sortBy,
                  onChanged: (value) {
                    setState(() {
                      _sortBy = value!;
                    });
                  },
                ),
              ),
            ],
          ),
          actions: [
            TextButton(
              onPressed: () => Navigator.pop(context),
              child: const Text('Fermer'),
            ),
          ],
        );
      },
    );
  }

  void _showCreateListingDialog() {
    showDialog(
      context: context,
      builder: (BuildContext context) {
        return AlertDialog(
          title: const Text('Créer une annonce'),
          content: const Text('Fonctionnalité de création d\'annonce à implémenter'),
          actions: [
            TextButton(
              onPressed: () => Navigator.pop(context),
              child: const Text('Fermer'),
            ),
          ],
        );
      },
    );
  }

  void _showListingDetails(MarketListing listing) {
    showDialog(
      context: context,
      builder: (BuildContext context) {
        return AlertDialog(
          title: Text(listing.title),
          content: Column(
            mainAxisSize: MainAxisSize.min,
            crossAxisAlignment: CrossAxisAlignment.start,
            children: [
              Text(listing.description),
              const SizedBox(height: 8),
              Text(
                '${listing.price.toStringAsFixed(0)} DA',
                style: Theme.of(context).textTheme.titleLarge?.copyWith(
                  color: Colors.purple[700],
                  fontWeight: FontWeight.bold,
                ),
              ),
              const SizedBox(height: 8),
              Text('Localisation: ${listing.location ?? "Non spécifié"}'),
              Text('Vues: ${listing.views}'),
            ],
          ),
          actions: [
            TextButton(
              onPressed: () => Navigator.pop(context),
              child: const Text('Fermer'),
            ),
            ElevatedButton(
              onPressed: () {
                Navigator.pop(context);
                // TODO: Implement contact seller
                ScaffoldMessenger.of(context).showSnackBar(
                  const SnackBar(content: Text('Contacter le vendeur - À implémenter')),
                );
              },
              child: const Text('Contacter'),
            ),
          ],
        );
      },
    );
  }

  void _handleListingAction(String action, MarketListing listing) {
    switch (action) {
      case 'edit':
        ScaffoldMessenger.of(context).showSnackBar(
          const SnackBar(content: Text('Modifier l\'annonce - À implémenter')),
        );
        break;
      case 'promote':
        ScaffoldMessenger.of(context).showSnackBar(
          const SnackBar(content: Text('Promouvoir l\'annonce - À implémenter')),
        );
        break;
      case 'delete':
        _showDeleteConfirmation(listing);
        break;
    }
  }

  void _showDeleteConfirmation(MarketListing listing) {
    showDialog(
      context: context,
      builder: (BuildContext context) {
        return AlertDialog(
          title: const Text('Supprimer l\'annonce'),
          content: Text('Êtes-vous sûr de vouloir supprimer "${listing.title}" ?'),
          actions: [
            TextButton(
              onPressed: () => Navigator.pop(context),
              child: const Text('Annuler'),
            ),
            ElevatedButton(
              onPressed: () {
                Navigator.pop(context);
                // TODO: Implement delete listing
                ScaffoldMessenger.of(context).showSnackBar(
                  const SnackBar(content: Text('Annonce supprimée')),
                );
              },
              style: ElevatedButton.styleFrom(backgroundColor: Colors.red),
              child: const Text('Supprimer', style: TextStyle(color: Colors.white)),
            ),
          ],
        );
      },
    );
  }
}

