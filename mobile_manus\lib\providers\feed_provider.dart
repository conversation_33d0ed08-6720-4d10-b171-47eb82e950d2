import 'package:flutter/material.dart';
import 'package:mobile_manus/models/feed_consumption_log.dart';
import 'package:mobile_manus/models/feed_plan.dart';
import 'package:mobile_manus/services/api/feed_api_service.dart';

class FeedProvider with ChangeNotifier {
  final FeedApiService _feedApiService = FeedApiService();

  List<FeedConsumptionLog> _consumptionLogs = [];
  List<FeedPlan> _feedPlans = [];

  List<FeedConsumptionLog> get consumptionLogs => _consumptionLogs;
  List<FeedPlan> get feedPlans => _feedPlans;

  Future<void> fetchFeedConsumptionLogs() async {
    try {
      _consumptionLogs = await _feedApiService.getFeedConsumptionLogs();
      notifyListeners();
    } catch (e) {
      // Handle error
      print('Error fetching feed consumption logs: $e');
    }
  }

  Future<void> addFeedConsumptionLog(FeedConsumptionLog log) async {
    try {
      final newLog = await _feedApiService.createFeedConsumptionLog(log);
      _consumptionLogs.add(newLog);
      notifyListeners();
    } catch (e) {
      // Handle error
      print('Error adding feed consumption log: $e');
    }
  }

  Future<void> fetchFeedPlans() async {
    try {
      _feedPlans = await _feedApiService.getFeedPlans();
      notifyListeners();
    } catch (e) {
      // Handle error
      print('Error fetching feed plans: $e');
    }
  }

  Future<void> addFeedPlan(FeedPlan plan) async {
    try {
      final newPlan = await _feedApiService.createFeedPlan(plan);
      _feedPlans.add(newPlan);
      notifyListeners();
    } catch (e) {
      // Handle error
      print('Error adding feed plan: $e');
    }
  }

  Future<void> loadFeedPlans() async {
    await fetchFeedPlans();
  }

  Future<void> loadConsumptionLogs() async {
    await fetchFeedConsumptionLogs();
  }
}
