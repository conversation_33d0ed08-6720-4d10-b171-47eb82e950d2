<!DOCTYPE html>
<html lang="fr">
<head>
  <meta charset="UTF-8">
  <meta name="viewport" content="width=device-width, initial-scale=1.0">
  <title>Test des SVG Poultray DZ</title>
  <style>
    body {
      font-family: Arial, sans-serif;
      margin: 0;
      padding: 20px;
      background-color: #f5f5f5;
    }
    .container {
      max-width: 800px;
      margin: 0 auto;
      background-color: white;
      padding: 20px;
      border-radius: 8px;
      box-shadow: 0 2px 10px rgba(0,0,0,0.1);
    }
    h1 {
      color: #4CAF50;
      text-align: center;
    }
    .asset-container {
      margin: 20px 0;
      padding: 15px;
      border: 1px solid #ddd;
      border-radius: 4px;
    }
    .asset-title {
      font-weight: bold;
      margin-bottom: 10px;
    }
    img {
      max-width: 100%;
      height: auto;
      display: block;
      margin: 0 auto;
    }
    .svg-inline {
      width: 100%;
      max-width: 400px;
      margin: 0 auto;
      display: block;
    }
  </style>
</head>
<body>
  <div class="container">
    <h1>Test des SVG Poultray DZ</h1>
    
    <div class="asset-container">
      <div class="asset-title">Logo (SVG Inline)</div>
      <svg xmlns="http://www.w3.org/2000/svg" width="200" height="200" viewBox="0 0 200 200" class="svg-inline">
        <defs>
          <linearGradient id="gradient" x1="0%" y1="0%" x2="100%" y2="100%">
            <stop offset="0%" stop-color="#4caf50" />
            <stop offset="100%" stop-color="#357a38" />
          </linearGradient>
        </defs>
        <circle cx="100" cy="100" r="90" fill="white" stroke="url(#gradient)" stroke-width="5" />
        <g transform="translate(50, 50)">
          <!-- Stylized chicken silhouette -->
          <path d="M25,0 C40,0 50,10 60,30 C70,50 70,70 60,90 C50,110 30,100 20,80 C10,60 10,40 20,20 C25,10 35,0 50,0 Z" fill="url(#gradient)" />
          <!-- Egg shape -->
          <ellipse cx="70" cy="50" rx="20" ry="30" fill="white" stroke="#4caf50" stroke-width="3" />
        </g>
        <text x="100" y="160" font-family="Arial" font-size="18" font-weight="bold" text-anchor="middle" fill="#4caf50">Poultray DZ</text>
      </svg>
    </div>
    
    <div class="asset-container">
      <div class="asset-title">Hero Image (SVG Inline)</div>
      <svg xmlns="http://www.w3.org/2000/svg" width="800" height="600" viewBox="0 0 800 600" class="svg-inline">
        <rect width="800" height="600" fill="#f8f9fa"/>
        <g transform="translate(400, 300)">
          <!-- Stylized chicken silhouette -->
          <path d="M-100,-50 C-80,-70 -60,-80 -40,-80 C-20,-80 0,-70 20,-50 C40,-30 50,-10 50,10 C50,30 40,50 20,70 C0,90 -20,100 -40,100 C-60,100 -80,90 -100,70 C-120,50 -130,30 -130,10 C-130,-10 -120,-30 -100,-50 Z" fill="#4caf50" opacity="0.8"/>
          <!-- Egg shape -->
          <ellipse cx="80" cy="20" rx="60" ry="80" fill="#fff" stroke="#4caf50" stroke-width="5"/>
          <!-- Farm building silhouette -->
          <path d="M-200,100 L-200,0 L-150,-50 L-100,0 L-100,100 Z" fill="#8d6e63"/>
          <rect x="-180" y="50" width="30" height="50" fill="#5d4037"/>
          <!-- Sun -->
          <circle cx="150" cy="-100" r="40" fill="#ffc107"/>
          <line x1="150" y1="-150" x2="150" y2="-170" stroke="#ffc107" stroke-width="5"/>
          <line x1="150" y1="-30" x2="150" y2="-10" stroke="#ffc107" stroke-width="5"/>
          <line x1="100" y1="-100" x2="80" y2="-100" stroke="#ffc107" stroke-width="5"/>
          <line x1="200" y1="-100" x2="220" y2="-100" stroke="#ffc107" stroke-width="5"/>
          <line x1="115" y1="-135" x2="100" y2="-150" stroke="#ffc107" stroke-width="5"/>
          <line x1="185" y1="-65" x2="200" y2="-50" stroke="#ffc107" stroke-width="5"/>
          <line x1="115" y1="-65" x2="100" y2="-50" stroke="#ffc107" stroke-width="5"/>
          <line x1="185" y1="-135" x2="200" y2="-150" stroke="#ffc107" stroke-width="5"/>
        </g>
        <text x="400" y="500" font-family="Arial" font-size="24" text-anchor="middle" fill="#4caf50">Poultray DZ</text>
      </svg>
    </div>
  </div>
</body>
</html>