
import { Box, Grid, Container, Paper, Typography } from '@mui/material';
import { useTranslation } from 'react-i18next';

// Import des composants
import PoussinManagement from './PoussinManagement';
import EggProductionManagement from './EggProductionManagement';
import VetConsultations from './VetConsultations';
import AlerteStock from './AlerteStock';
import DailyCollection from './DailyCollection';
import EggSalesManagement from './EggSalesManagement';
import ActiveTreatments from './ActiveTreatments';
import VaccinationSchedule from './VaccinationSchedule';
import EmergencyVet from './EmergencyVet';
import StockDashboard from './StockDashboard';
import AlertHistory from './AlertHistory';
import AlertSettings from './AlertSettings';

// Import des nouveaux composants
import Overview from './Overview';
import ProductionTracking from './ProductionTracking';
import AlertsActions from './AlertsActions';
import RecentActivity from './RecentActivity';

const DashboardEleveur = () => {
  const { t } = useTranslation();

  return (
    <Container maxWidth="xl">
      <Box sx={{ py: 4 }}>
        <Typography variant="h4" component="h1" gutterBottom>
          {t('dashboard.eleveur.title')}
        </Typography>

        {/* Section Vue d'Ensemble */}
        <Overview />

        {/* Section Suivi de Production et Activité Récente */}
        <Grid container spacing={3} sx={{ mb: 4 }}>
          <Grid item xs={12} lg={8}>
            <ProductionTracking />
          </Grid>
          <Grid item xs={12} lg={4}>
            <RecentActivity />
          </Grid>
        </Grid>

        {/* Section Alertes et Actions */}
        <Box sx={{ mb: 4 }}>
          <AlertsActions />
        </Box>

        {/* Section Gestion des Poussins */}
        <Paper sx={{ p: 3, mb: 4 }}>
          <Typography variant="h5" gutterBottom>
            {t('dashboard.sections.poussins')}
          </Typography>
          <PoussinManagement />
        </Paper>

        {/* Section Production d'Œufs */}
        <Paper sx={{ p: 3, mb: 4 }}>
          <Typography variant="h5" gutterBottom>
            {t('dashboard.sections.eggs')}
          </Typography>
          <Grid container spacing={3}>
            <Grid item xs={12} md={6}>
              <DailyCollection />
            </Grid>
            <Grid item xs={12} md={6}>
              <EggProductionManagement />
            </Grid>
            <Grid item xs={12}>
              <EggSalesManagement />
            </Grid>
          </Grid>
        </Paper>

        {/* Section Suivi Vétérinaire */}
        <Paper sx={{ p: 3, mb: 4 }}>
          <Typography variant="h5" gutterBottom>
            {t('dashboard.sections.veterinary')}
          </Typography>
          <Grid container spacing={3}>
            <Grid item xs={12} lg={6}>
              <VetConsultations />
            </Grid>
            <Grid item xs={12} md={6} lg={3}>
              <ActiveTreatments />
            </Grid>
            <Grid item xs={12} md={6} lg={3}>
              <VaccinationSchedule />
            </Grid>
            <Grid item xs={12}>
              <EmergencyVet />
            </Grid>
          </Grid>
        </Paper>

        {/* Section Alertes et Stocks */}
        <Paper sx={{ p: 3 }}>
          <Typography variant="h5" gutterBottom>
            {t('dashboard.sections.alerts')}
          </Typography>
          <Grid container spacing={3}>
            <Grid item xs={12} md={6}>
              <AlerteStock />
            </Grid>
            <Grid item xs={12} md={6}>
              <StockDashboard />
            </Grid>
            <Grid item xs={12} md={8}>
              <AlertHistory />
            </Grid>
            <Grid item xs={12} md={4}>
              <AlertSettings />
            </Grid>
          </Grid>
        </Paper>
      </Box>
    </Container>
  );
};

export default DashboardEleveur;
