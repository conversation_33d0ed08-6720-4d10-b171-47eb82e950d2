const express = require('express');
const cors = require('cors');

const app = express();
const PORT = process.env.PORT || 3003;

// Basic middleware
app.use(cors());
app.use(express.json());

// Simple health endpoint
app.get('/api/health', (req, res) => {
  res.json({ 
    status: 'OK', 
    timestamp: new Date().toISOString(),
    message: 'Server is running'
  });
});

// Start server
app.listen(PORT, () => {
  console.log(`Minimal server running on http://localhost:${PORT}`);
  console.log('Health endpoint: http://localhost:' + PORT + '/api/health');
});
