# CMAKE generated file: DO NOT EDIT!
# Generated by "Ninja" Generator, CMake Version 3.22

# This file contains all the build statements describing the
# compilation DAG.

# =============================================================================
# Write statements declared in CMakeLists.txt:
# 
# Which is the root file.
# =============================================================================

# =============================================================================
# Project: Project
# Configurations: Debug
# =============================================================================

#############################################
# Minimal version of Ninja required by this file

ninja_required_version = 1.5


#############################################
# Set configuration variable for custom commands.

CONFIGURATION = Debug
# =============================================================================
# Include auxiliary files.


#############################################
# Include rules file.

include CMakeFiles/rules.ninja

# =============================================================================

#############################################
# Logical path to working directory; prefix for absolute paths.

cmake_ninja_workdir = K$:/Projets_Sites_Web/Poultray-dz-TraeDev/Web_App/Poultraydz-Trae/mobile_manus/android/app/.cxx/Debug/z4ls113k/x86_64/

#############################################
# Utility command for edit_cache

build CMakeFiles/edit_cache.util: CUSTOM_COMMAND
  COMMAND = cmd.exe /C "cd /D K:\Projets_Sites_Web\Poultray-dz-TraeDev\Web_App\Poultraydz-Trae\mobile_manus\android\app\.cxx\Debug\z4ls113k\x86_64 && K:\AndroidSDK\cmake\3.22.1\bin\cmake.exe -E echo "No interactive CMake dialog available.""
  DESC = No interactive CMake dialog available...
  restat = 1

build edit_cache: phony CMakeFiles/edit_cache.util


#############################################
# Utility command for rebuild_cache

build CMakeFiles/rebuild_cache.util: CUSTOM_COMMAND
  COMMAND = cmd.exe /C "cd /D K:\Projets_Sites_Web\Poultray-dz-TraeDev\Web_App\Poultraydz-Trae\mobile_manus\android\app\.cxx\Debug\z4ls113k\x86_64 && K:\AndroidSDK\cmake\3.22.1\bin\cmake.exe --regenerate-during-build -SC:\Users\<USER>\fvm\versions\3.10.4\packages\flutter_tools\gradle\src\main\groovy -BK:\Projets_Sites_Web\Poultray-dz-TraeDev\Web_App\Poultraydz-Trae\mobile_manus\android\app\.cxx\Debug\z4ls113k\x86_64"
  DESC = Running CMake to regenerate build system...
  pool = console
  restat = 1

build rebuild_cache: phony CMakeFiles/rebuild_cache.util

# =============================================================================
# Target aliases.

# =============================================================================
# Folder targets.

# =============================================================================

#############################################
# Folder: K:/Projets_Sites_Web/Poultray-dz-TraeDev/Web_App/Poultraydz-Trae/mobile_manus/android/app/.cxx/Debug/z4ls113k/x86_64

build all: phony

# =============================================================================
# Built-in targets


#############################################
# Re-run CMake if any of its inputs changed.

build build.ninja: RERUN_CMAKE | C$:/Users/<USER>/fvm/versions/3.10.4/packages/flutter_tools/gradle/src/main/groovy/CMakeLists.txt CMakeCache.txt CMakeFiles/3.22.1-g37088a8-dirty/CMakeCCompiler.cmake CMakeFiles/3.22.1-g37088a8-dirty/CMakeCXXCompiler.cmake CMakeFiles/3.22.1-g37088a8-dirty/CMakeSystem.cmake K$:/AndroidSDK/cmake/3.22.1/share/cmake-3.22/Modules/CMakeCCompiler.cmake.in K$:/AndroidSDK/cmake/3.22.1/share/cmake-3.22/Modules/CMakeCCompilerABI.c K$:/AndroidSDK/cmake/3.22.1/share/cmake-3.22/Modules/CMakeCInformation.cmake K$:/AndroidSDK/cmake/3.22.1/share/cmake-3.22/Modules/CMakeCXXCompiler.cmake.in K$:/AndroidSDK/cmake/3.22.1/share/cmake-3.22/Modules/CMakeCXXCompilerABI.cpp K$:/AndroidSDK/cmake/3.22.1/share/cmake-3.22/Modules/CMakeCXXInformation.cmake K$:/AndroidSDK/cmake/3.22.1/share/cmake-3.22/Modules/CMakeCommonLanguageInclude.cmake K$:/AndroidSDK/cmake/3.22.1/share/cmake-3.22/Modules/CMakeCompilerIdDetection.cmake K$:/AndroidSDK/cmake/3.22.1/share/cmake-3.22/Modules/CMakeDetermineCCompiler.cmake K$:/AndroidSDK/cmake/3.22.1/share/cmake-3.22/Modules/CMakeDetermineCXXCompiler.cmake K$:/AndroidSDK/cmake/3.22.1/share/cmake-3.22/Modules/CMakeDetermineCompileFeatures.cmake K$:/AndroidSDK/cmake/3.22.1/share/cmake-3.22/Modules/CMakeDetermineCompiler.cmake K$:/AndroidSDK/cmake/3.22.1/share/cmake-3.22/Modules/CMakeDetermineCompilerABI.cmake K$:/AndroidSDK/cmake/3.22.1/share/cmake-3.22/Modules/CMakeDetermineCompilerId.cmake K$:/AndroidSDK/cmake/3.22.1/share/cmake-3.22/Modules/CMakeDetermineSystem.cmake K$:/AndroidSDK/cmake/3.22.1/share/cmake-3.22/Modules/CMakeFindBinUtils.cmake K$:/AndroidSDK/cmake/3.22.1/share/cmake-3.22/Modules/CMakeGenericSystem.cmake K$:/AndroidSDK/cmake/3.22.1/share/cmake-3.22/Modules/CMakeInitializeConfigs.cmake K$:/AndroidSDK/cmake/3.22.1/share/cmake-3.22/Modules/CMakeLanguageInformation.cmake K$:/AndroidSDK/cmake/3.22.1/share/cmake-3.22/Modules/CMakeParseImplicitIncludeInfo.cmake K$:/AndroidSDK/cmake/3.22.1/share/cmake-3.22/Modules/CMakeParseImplicitLinkInfo.cmake K$:/AndroidSDK/cmake/3.22.1/share/cmake-3.22/Modules/CMakeParseLibraryArchitecture.cmake K$:/AndroidSDK/cmake/3.22.1/share/cmake-3.22/Modules/CMakeSystem.cmake.in K$:/AndroidSDK/cmake/3.22.1/share/cmake-3.22/Modules/CMakeSystemSpecificInformation.cmake K$:/AndroidSDK/cmake/3.22.1/share/cmake-3.22/Modules/CMakeSystemSpecificInitialize.cmake K$:/AndroidSDK/cmake/3.22.1/share/cmake-3.22/Modules/CMakeTestCCompiler.cmake K$:/AndroidSDK/cmake/3.22.1/share/cmake-3.22/Modules/CMakeTestCXXCompiler.cmake K$:/AndroidSDK/cmake/3.22.1/share/cmake-3.22/Modules/CMakeTestCompilerCommon.cmake K$:/AndroidSDK/cmake/3.22.1/share/cmake-3.22/Modules/Compiler/ADSP-DetermineCompiler.cmake K$:/AndroidSDK/cmake/3.22.1/share/cmake-3.22/Modules/Compiler/ARMCC-DetermineCompiler.cmake K$:/AndroidSDK/cmake/3.22.1/share/cmake-3.22/Modules/Compiler/ARMClang-DetermineCompiler.cmake K$:/AndroidSDK/cmake/3.22.1/share/cmake-3.22/Modules/Compiler/AppleClang-DetermineCompiler.cmake K$:/AndroidSDK/cmake/3.22.1/share/cmake-3.22/Modules/Compiler/Borland-DetermineCompiler.cmake K$:/AndroidSDK/cmake/3.22.1/share/cmake-3.22/Modules/Compiler/Bruce-C-DetermineCompiler.cmake K$:/AndroidSDK/cmake/3.22.1/share/cmake-3.22/Modules/Compiler/CMakeCommonCompilerMacros.cmake K$:/AndroidSDK/cmake/3.22.1/share/cmake-3.22/Modules/Compiler/Clang-C.cmake K$:/AndroidSDK/cmake/3.22.1/share/cmake-3.22/Modules/Compiler/Clang-CXX.cmake K$:/AndroidSDK/cmake/3.22.1/share/cmake-3.22/Modules/Compiler/Clang-DetermineCompiler.cmake K$:/AndroidSDK/cmake/3.22.1/share/cmake-3.22/Modules/Compiler/Clang-DetermineCompilerInternal.cmake K$:/AndroidSDK/cmake/3.22.1/share/cmake-3.22/Modules/Compiler/Clang-FindBinUtils.cmake K$:/AndroidSDK/cmake/3.22.1/share/cmake-3.22/Modules/Compiler/Clang.cmake K$:/AndroidSDK/cmake/3.22.1/share/cmake-3.22/Modules/Compiler/Comeau-CXX-DetermineCompiler.cmake K$:/AndroidSDK/cmake/3.22.1/share/cmake-3.22/Modules/Compiler/Compaq-C-DetermineCompiler.cmake K$:/AndroidSDK/cmake/3.22.1/share/cmake-3.22/Modules/Compiler/Compaq-CXX-DetermineCompiler.cmake K$:/AndroidSDK/cmake/3.22.1/share/cmake-3.22/Modules/Compiler/Cray-DetermineCompiler.cmake K$:/AndroidSDK/cmake/3.22.1/share/cmake-3.22/Modules/Compiler/Embarcadero-DetermineCompiler.cmake K$:/AndroidSDK/cmake/3.22.1/share/cmake-3.22/Modules/Compiler/Fujitsu-DetermineCompiler.cmake K$:/AndroidSDK/cmake/3.22.1/share/cmake-3.22/Modules/Compiler/FujitsuClang-DetermineCompiler.cmake K$:/AndroidSDK/cmake/3.22.1/share/cmake-3.22/Modules/Compiler/GHS-DetermineCompiler.cmake K$:/AndroidSDK/cmake/3.22.1/share/cmake-3.22/Modules/Compiler/GNU-C-DetermineCompiler.cmake K$:/AndroidSDK/cmake/3.22.1/share/cmake-3.22/Modules/Compiler/GNU-CXX-DetermineCompiler.cmake K$:/AndroidSDK/cmake/3.22.1/share/cmake-3.22/Modules/Compiler/GNU.cmake K$:/AndroidSDK/cmake/3.22.1/share/cmake-3.22/Modules/Compiler/HP-C-DetermineCompiler.cmake K$:/AndroidSDK/cmake/3.22.1/share/cmake-3.22/Modules/Compiler/HP-CXX-DetermineCompiler.cmake K$:/AndroidSDK/cmake/3.22.1/share/cmake-3.22/Modules/Compiler/IAR-DetermineCompiler.cmake K$:/AndroidSDK/cmake/3.22.1/share/cmake-3.22/Modules/Compiler/IBMCPP-C-DetermineVersionInternal.cmake K$:/AndroidSDK/cmake/3.22.1/share/cmake-3.22/Modules/Compiler/IBMCPP-CXX-DetermineVersionInternal.cmake K$:/AndroidSDK/cmake/3.22.1/share/cmake-3.22/Modules/Compiler/Intel-DetermineCompiler.cmake K$:/AndroidSDK/cmake/3.22.1/share/cmake-3.22/Modules/Compiler/IntelLLVM-DetermineCompiler.cmake K$:/AndroidSDK/cmake/3.22.1/share/cmake-3.22/Modules/Compiler/MSVC-DetermineCompiler.cmake K$:/AndroidSDK/cmake/3.22.1/share/cmake-3.22/Modules/Compiler/NVHPC-DetermineCompiler.cmake K$:/AndroidSDK/cmake/3.22.1/share/cmake-3.22/Modules/Compiler/NVIDIA-DetermineCompiler.cmake K$:/AndroidSDK/cmake/3.22.1/share/cmake-3.22/Modules/Compiler/OpenWatcom-DetermineCompiler.cmake K$:/AndroidSDK/cmake/3.22.1/share/cmake-3.22/Modules/Compiler/PGI-DetermineCompiler.cmake K$:/AndroidSDK/cmake/3.22.1/share/cmake-3.22/Modules/Compiler/PathScale-DetermineCompiler.cmake K$:/AndroidSDK/cmake/3.22.1/share/cmake-3.22/Modules/Compiler/SCO-DetermineCompiler.cmake K$:/AndroidSDK/cmake/3.22.1/share/cmake-3.22/Modules/Compiler/SDCC-C-DetermineCompiler.cmake K$:/AndroidSDK/cmake/3.22.1/share/cmake-3.22/Modules/Compiler/SunPro-C-DetermineCompiler.cmake K$:/AndroidSDK/cmake/3.22.1/share/cmake-3.22/Modules/Compiler/SunPro-CXX-DetermineCompiler.cmake K$:/AndroidSDK/cmake/3.22.1/share/cmake-3.22/Modules/Compiler/TI-DetermineCompiler.cmake K$:/AndroidSDK/cmake/3.22.1/share/cmake-3.22/Modules/Compiler/TinyCC-C-DetermineCompiler.cmake K$:/AndroidSDK/cmake/3.22.1/share/cmake-3.22/Modules/Compiler/VisualAge-C-DetermineCompiler.cmake K$:/AndroidSDK/cmake/3.22.1/share/cmake-3.22/Modules/Compiler/VisualAge-CXX-DetermineCompiler.cmake K$:/AndroidSDK/cmake/3.22.1/share/cmake-3.22/Modules/Compiler/Watcom-DetermineCompiler.cmake K$:/AndroidSDK/cmake/3.22.1/share/cmake-3.22/Modules/Compiler/XL-C-DetermineCompiler.cmake K$:/AndroidSDK/cmake/3.22.1/share/cmake-3.22/Modules/Compiler/XL-CXX-DetermineCompiler.cmake K$:/AndroidSDK/cmake/3.22.1/share/cmake-3.22/Modules/Compiler/XLClang-C-DetermineCompiler.cmake K$:/AndroidSDK/cmake/3.22.1/share/cmake-3.22/Modules/Compiler/XLClang-CXX-DetermineCompiler.cmake K$:/AndroidSDK/cmake/3.22.1/share/cmake-3.22/Modules/Compiler/zOS-C-DetermineCompiler.cmake K$:/AndroidSDK/cmake/3.22.1/share/cmake-3.22/Modules/Compiler/zOS-CXX-DetermineCompiler.cmake K$:/AndroidSDK/cmake/3.22.1/share/cmake-3.22/Modules/Internal/FeatureTesting.cmake K$:/AndroidSDK/cmake/3.22.1/share/cmake-3.22/Modules/Platform/Android-Clang-C.cmake K$:/AndroidSDK/cmake/3.22.1/share/cmake-3.22/Modules/Platform/Android-Clang-CXX.cmake K$:/AndroidSDK/cmake/3.22.1/share/cmake-3.22/Modules/Platform/Android-Clang.cmake K$:/AndroidSDK/cmake/3.22.1/share/cmake-3.22/Modules/Platform/Android-Determine-C.cmake K$:/AndroidSDK/cmake/3.22.1/share/cmake-3.22/Modules/Platform/Android-Determine-CXX.cmake K$:/AndroidSDK/cmake/3.22.1/share/cmake-3.22/Modules/Platform/Android-Determine.cmake K$:/AndroidSDK/cmake/3.22.1/share/cmake-3.22/Modules/Platform/Android-Initialize.cmake K$:/AndroidSDK/cmake/3.22.1/share/cmake-3.22/Modules/Platform/Android.cmake K$:/AndroidSDK/cmake/3.22.1/share/cmake-3.22/Modules/Platform/Android/Determine-Compiler.cmake K$:/AndroidSDK/cmake/3.22.1/share/cmake-3.22/Modules/Platform/Linux.cmake K$:/AndroidSDK/cmake/3.22.1/share/cmake-3.22/Modules/Platform/UnixPaths.cmake K$:/AndroidSDK/ndk/26.3.11579264/build/cmake/android-legacy.toolchain.cmake K$:/AndroidSDK/ndk/26.3.11579264/build/cmake/android.toolchain.cmake K$:/AndroidSDK/ndk/26.3.11579264/build/cmake/flags.cmake K$:/AndroidSDK/ndk/26.3.11579264/build/cmake/hooks/pre/Android-Clang.cmake K$:/AndroidSDK/ndk/26.3.11579264/build/cmake/hooks/pre/Android-Determine.cmake K$:/AndroidSDK/ndk/26.3.11579264/build/cmake/hooks/pre/Android-Initialize.cmake K$:/AndroidSDK/ndk/26.3.11579264/build/cmake/hooks/pre/Android.cmake K$:/AndroidSDK/ndk/26.3.11579264/build/cmake/hooks/pre/Determine-Compiler.cmake K$:/AndroidSDK/ndk/26.3.11579264/build/cmake/platforms.cmake
  pool = console


#############################################
# A missing CMake input file is not an error.

build C$:/Users/<USER>/fvm/versions/3.10.4/packages/flutter_tools/gradle/src/main/groovy/CMakeLists.txt CMakeCache.txt CMakeFiles/3.22.1-g37088a8-dirty/CMakeCCompiler.cmake CMakeFiles/3.22.1-g37088a8-dirty/CMakeCXXCompiler.cmake CMakeFiles/3.22.1-g37088a8-dirty/CMakeSystem.cmake K$:/AndroidSDK/cmake/3.22.1/share/cmake-3.22/Modules/CMakeCCompiler.cmake.in K$:/AndroidSDK/cmake/3.22.1/share/cmake-3.22/Modules/CMakeCCompilerABI.c K$:/AndroidSDK/cmake/3.22.1/share/cmake-3.22/Modules/CMakeCInformation.cmake K$:/AndroidSDK/cmake/3.22.1/share/cmake-3.22/Modules/CMakeCXXCompiler.cmake.in K$:/AndroidSDK/cmake/3.22.1/share/cmake-3.22/Modules/CMakeCXXCompilerABI.cpp K$:/AndroidSDK/cmake/3.22.1/share/cmake-3.22/Modules/CMakeCXXInformation.cmake K$:/AndroidSDK/cmake/3.22.1/share/cmake-3.22/Modules/CMakeCommonLanguageInclude.cmake K$:/AndroidSDK/cmake/3.22.1/share/cmake-3.22/Modules/CMakeCompilerIdDetection.cmake K$:/AndroidSDK/cmake/3.22.1/share/cmake-3.22/Modules/CMakeDetermineCCompiler.cmake K$:/AndroidSDK/cmake/3.22.1/share/cmake-3.22/Modules/CMakeDetermineCXXCompiler.cmake K$:/AndroidSDK/cmake/3.22.1/share/cmake-3.22/Modules/CMakeDetermineCompileFeatures.cmake K$:/AndroidSDK/cmake/3.22.1/share/cmake-3.22/Modules/CMakeDetermineCompiler.cmake K$:/AndroidSDK/cmake/3.22.1/share/cmake-3.22/Modules/CMakeDetermineCompilerABI.cmake K$:/AndroidSDK/cmake/3.22.1/share/cmake-3.22/Modules/CMakeDetermineCompilerId.cmake K$:/AndroidSDK/cmake/3.22.1/share/cmake-3.22/Modules/CMakeDetermineSystem.cmake K$:/AndroidSDK/cmake/3.22.1/share/cmake-3.22/Modules/CMakeFindBinUtils.cmake K$:/AndroidSDK/cmake/3.22.1/share/cmake-3.22/Modules/CMakeGenericSystem.cmake K$:/AndroidSDK/cmake/3.22.1/share/cmake-3.22/Modules/CMakeInitializeConfigs.cmake K$:/AndroidSDK/cmake/3.22.1/share/cmake-3.22/Modules/CMakeLanguageInformation.cmake K$:/AndroidSDK/cmake/3.22.1/share/cmake-3.22/Modules/CMakeParseImplicitIncludeInfo.cmake K$:/AndroidSDK/cmake/3.22.1/share/cmake-3.22/Modules/CMakeParseImplicitLinkInfo.cmake K$:/AndroidSDK/cmake/3.22.1/share/cmake-3.22/Modules/CMakeParseLibraryArchitecture.cmake K$:/AndroidSDK/cmake/3.22.1/share/cmake-3.22/Modules/CMakeSystem.cmake.in K$:/AndroidSDK/cmake/3.22.1/share/cmake-3.22/Modules/CMakeSystemSpecificInformation.cmake K$:/AndroidSDK/cmake/3.22.1/share/cmake-3.22/Modules/CMakeSystemSpecificInitialize.cmake K$:/AndroidSDK/cmake/3.22.1/share/cmake-3.22/Modules/CMakeTestCCompiler.cmake K$:/AndroidSDK/cmake/3.22.1/share/cmake-3.22/Modules/CMakeTestCXXCompiler.cmake K$:/AndroidSDK/cmake/3.22.1/share/cmake-3.22/Modules/CMakeTestCompilerCommon.cmake K$:/AndroidSDK/cmake/3.22.1/share/cmake-3.22/Modules/Compiler/ADSP-DetermineCompiler.cmake K$:/AndroidSDK/cmake/3.22.1/share/cmake-3.22/Modules/Compiler/ARMCC-DetermineCompiler.cmake K$:/AndroidSDK/cmake/3.22.1/share/cmake-3.22/Modules/Compiler/ARMClang-DetermineCompiler.cmake K$:/AndroidSDK/cmake/3.22.1/share/cmake-3.22/Modules/Compiler/AppleClang-DetermineCompiler.cmake K$:/AndroidSDK/cmake/3.22.1/share/cmake-3.22/Modules/Compiler/Borland-DetermineCompiler.cmake K$:/AndroidSDK/cmake/3.22.1/share/cmake-3.22/Modules/Compiler/Bruce-C-DetermineCompiler.cmake K$:/AndroidSDK/cmake/3.22.1/share/cmake-3.22/Modules/Compiler/CMakeCommonCompilerMacros.cmake K$:/AndroidSDK/cmake/3.22.1/share/cmake-3.22/Modules/Compiler/Clang-C.cmake K$:/AndroidSDK/cmake/3.22.1/share/cmake-3.22/Modules/Compiler/Clang-CXX.cmake K$:/AndroidSDK/cmake/3.22.1/share/cmake-3.22/Modules/Compiler/Clang-DetermineCompiler.cmake K$:/AndroidSDK/cmake/3.22.1/share/cmake-3.22/Modules/Compiler/Clang-DetermineCompilerInternal.cmake K$:/AndroidSDK/cmake/3.22.1/share/cmake-3.22/Modules/Compiler/Clang-FindBinUtils.cmake K$:/AndroidSDK/cmake/3.22.1/share/cmake-3.22/Modules/Compiler/Clang.cmake K$:/AndroidSDK/cmake/3.22.1/share/cmake-3.22/Modules/Compiler/Comeau-CXX-DetermineCompiler.cmake K$:/AndroidSDK/cmake/3.22.1/share/cmake-3.22/Modules/Compiler/Compaq-C-DetermineCompiler.cmake K$:/AndroidSDK/cmake/3.22.1/share/cmake-3.22/Modules/Compiler/Compaq-CXX-DetermineCompiler.cmake K$:/AndroidSDK/cmake/3.22.1/share/cmake-3.22/Modules/Compiler/Cray-DetermineCompiler.cmake K$:/AndroidSDK/cmake/3.22.1/share/cmake-3.22/Modules/Compiler/Embarcadero-DetermineCompiler.cmake K$:/AndroidSDK/cmake/3.22.1/share/cmake-3.22/Modules/Compiler/Fujitsu-DetermineCompiler.cmake K$:/AndroidSDK/cmake/3.22.1/share/cmake-3.22/Modules/Compiler/FujitsuClang-DetermineCompiler.cmake K$:/AndroidSDK/cmake/3.22.1/share/cmake-3.22/Modules/Compiler/GHS-DetermineCompiler.cmake K$:/AndroidSDK/cmake/3.22.1/share/cmake-3.22/Modules/Compiler/GNU-C-DetermineCompiler.cmake K$:/AndroidSDK/cmake/3.22.1/share/cmake-3.22/Modules/Compiler/GNU-CXX-DetermineCompiler.cmake K$:/AndroidSDK/cmake/3.22.1/share/cmake-3.22/Modules/Compiler/GNU.cmake K$:/AndroidSDK/cmake/3.22.1/share/cmake-3.22/Modules/Compiler/HP-C-DetermineCompiler.cmake K$:/AndroidSDK/cmake/3.22.1/share/cmake-3.22/Modules/Compiler/HP-CXX-DetermineCompiler.cmake K$:/AndroidSDK/cmake/3.22.1/share/cmake-3.22/Modules/Compiler/IAR-DetermineCompiler.cmake K$:/AndroidSDK/cmake/3.22.1/share/cmake-3.22/Modules/Compiler/IBMCPP-C-DetermineVersionInternal.cmake K$:/AndroidSDK/cmake/3.22.1/share/cmake-3.22/Modules/Compiler/IBMCPP-CXX-DetermineVersionInternal.cmake K$:/AndroidSDK/cmake/3.22.1/share/cmake-3.22/Modules/Compiler/Intel-DetermineCompiler.cmake K$:/AndroidSDK/cmake/3.22.1/share/cmake-3.22/Modules/Compiler/IntelLLVM-DetermineCompiler.cmake K$:/AndroidSDK/cmake/3.22.1/share/cmake-3.22/Modules/Compiler/MSVC-DetermineCompiler.cmake K$:/AndroidSDK/cmake/3.22.1/share/cmake-3.22/Modules/Compiler/NVHPC-DetermineCompiler.cmake K$:/AndroidSDK/cmake/3.22.1/share/cmake-3.22/Modules/Compiler/NVIDIA-DetermineCompiler.cmake K$:/AndroidSDK/cmake/3.22.1/share/cmake-3.22/Modules/Compiler/OpenWatcom-DetermineCompiler.cmake K$:/AndroidSDK/cmake/3.22.1/share/cmake-3.22/Modules/Compiler/PGI-DetermineCompiler.cmake K$:/AndroidSDK/cmake/3.22.1/share/cmake-3.22/Modules/Compiler/PathScale-DetermineCompiler.cmake K$:/AndroidSDK/cmake/3.22.1/share/cmake-3.22/Modules/Compiler/SCO-DetermineCompiler.cmake K$:/AndroidSDK/cmake/3.22.1/share/cmake-3.22/Modules/Compiler/SDCC-C-DetermineCompiler.cmake K$:/AndroidSDK/cmake/3.22.1/share/cmake-3.22/Modules/Compiler/SunPro-C-DetermineCompiler.cmake K$:/AndroidSDK/cmake/3.22.1/share/cmake-3.22/Modules/Compiler/SunPro-CXX-DetermineCompiler.cmake K$:/AndroidSDK/cmake/3.22.1/share/cmake-3.22/Modules/Compiler/TI-DetermineCompiler.cmake K$:/AndroidSDK/cmake/3.22.1/share/cmake-3.22/Modules/Compiler/TinyCC-C-DetermineCompiler.cmake K$:/AndroidSDK/cmake/3.22.1/share/cmake-3.22/Modules/Compiler/VisualAge-C-DetermineCompiler.cmake K$:/AndroidSDK/cmake/3.22.1/share/cmake-3.22/Modules/Compiler/VisualAge-CXX-DetermineCompiler.cmake K$:/AndroidSDK/cmake/3.22.1/share/cmake-3.22/Modules/Compiler/Watcom-DetermineCompiler.cmake K$:/AndroidSDK/cmake/3.22.1/share/cmake-3.22/Modules/Compiler/XL-C-DetermineCompiler.cmake K$:/AndroidSDK/cmake/3.22.1/share/cmake-3.22/Modules/Compiler/XL-CXX-DetermineCompiler.cmake K$:/AndroidSDK/cmake/3.22.1/share/cmake-3.22/Modules/Compiler/XLClang-C-DetermineCompiler.cmake K$:/AndroidSDK/cmake/3.22.1/share/cmake-3.22/Modules/Compiler/XLClang-CXX-DetermineCompiler.cmake K$:/AndroidSDK/cmake/3.22.1/share/cmake-3.22/Modules/Compiler/zOS-C-DetermineCompiler.cmake K$:/AndroidSDK/cmake/3.22.1/share/cmake-3.22/Modules/Compiler/zOS-CXX-DetermineCompiler.cmake K$:/AndroidSDK/cmake/3.22.1/share/cmake-3.22/Modules/Internal/FeatureTesting.cmake K$:/AndroidSDK/cmake/3.22.1/share/cmake-3.22/Modules/Platform/Android-Clang-C.cmake K$:/AndroidSDK/cmake/3.22.1/share/cmake-3.22/Modules/Platform/Android-Clang-CXX.cmake K$:/AndroidSDK/cmake/3.22.1/share/cmake-3.22/Modules/Platform/Android-Clang.cmake K$:/AndroidSDK/cmake/3.22.1/share/cmake-3.22/Modules/Platform/Android-Determine-C.cmake K$:/AndroidSDK/cmake/3.22.1/share/cmake-3.22/Modules/Platform/Android-Determine-CXX.cmake K$:/AndroidSDK/cmake/3.22.1/share/cmake-3.22/Modules/Platform/Android-Determine.cmake K$:/AndroidSDK/cmake/3.22.1/share/cmake-3.22/Modules/Platform/Android-Initialize.cmake K$:/AndroidSDK/cmake/3.22.1/share/cmake-3.22/Modules/Platform/Android.cmake K$:/AndroidSDK/cmake/3.22.1/share/cmake-3.22/Modules/Platform/Android/Determine-Compiler.cmake K$:/AndroidSDK/cmake/3.22.1/share/cmake-3.22/Modules/Platform/Linux.cmake K$:/AndroidSDK/cmake/3.22.1/share/cmake-3.22/Modules/Platform/UnixPaths.cmake K$:/AndroidSDK/ndk/26.3.11579264/build/cmake/android-legacy.toolchain.cmake K$:/AndroidSDK/ndk/26.3.11579264/build/cmake/android.toolchain.cmake K$:/AndroidSDK/ndk/26.3.11579264/build/cmake/flags.cmake K$:/AndroidSDK/ndk/26.3.11579264/build/cmake/hooks/pre/Android-Clang.cmake K$:/AndroidSDK/ndk/26.3.11579264/build/cmake/hooks/pre/Android-Determine.cmake K$:/AndroidSDK/ndk/26.3.11579264/build/cmake/hooks/pre/Android-Initialize.cmake K$:/AndroidSDK/ndk/26.3.11579264/build/cmake/hooks/pre/Android.cmake K$:/AndroidSDK/ndk/26.3.11579264/build/cmake/hooks/pre/Determine-Compiler.cmake K$:/AndroidSDK/ndk/26.3.11579264/build/cmake/platforms.cmake: phony


#############################################
# Clean all the built files.

build clean: CLEAN


#############################################
# Print all primary targets available.

build help: HELP


#############################################
# Make the all target the default.

default all
