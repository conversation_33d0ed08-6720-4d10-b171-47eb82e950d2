{"cmake": {"generator": {"multiConfig": false, "name": "Ninja"}, "paths": {"cmake": "K:/AndroidSDK/cmake/3.22.1/bin/cmake.exe", "cpack": "K:/AndroidSDK/cmake/3.22.1/bin/cpack.exe", "ctest": "K:/AndroidSDK/cmake/3.22.1/bin/ctest.exe", "root": "K:/AndroidSDK/cmake/3.22.1/share/cmake-3.22"}, "version": {"isDirty": true, "major": 3, "minor": 22, "patch": 1, "string": "3.22.1-g37088a8-dirty", "suffix": "g37088a8"}}, "objects": [{"jsonFile": "codemodel-v2-34fb16004ce7c21aa74b.json", "kind": "codemodel", "version": {"major": 2, "minor": 3}}, {"jsonFile": "cache-v2-5fd8ccc0ec1f6c55f0e6.json", "kind": "cache", "version": {"major": 2, "minor": 0}}, {"jsonFile": "cmakeFiles-v1-83a8368102b0f18ab71f.json", "kind": "cmakeFiles", "version": {"major": 1, "minor": 0}}], "reply": {"client-agp": {"cache-v2": {"jsonFile": "cache-v2-5fd8ccc0ec1f6c55f0e6.json", "kind": "cache", "version": {"major": 2, "minor": 0}}, "cmakeFiles-v1": {"jsonFile": "cmakeFiles-v1-83a8368102b0f18ab71f.json", "kind": "cmakeFiles", "version": {"major": 1, "minor": 0}}, "codemodel-v2": {"jsonFile": "codemodel-v2-34fb16004ce7c21aa74b.json", "kind": "codemodel", "version": {"major": 2, "minor": 3}}}}}