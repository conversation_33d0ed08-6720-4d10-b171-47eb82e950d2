Write-Host "Testing API endpoints..."

$urls = @(
    "http://localhost:3003/",
    "http://localhost:3003/api",
    "http://localhost:3003/api/health"
)

foreach ($url in $urls) {
    Write-Host "`nTesting: $url"
    try {
        $response = Invoke-WebRequest -Uri $url -Method Get -UseBasicParsing
        Write-Host "Status: $($response.StatusCode)"
        Write-Host "Response: $($response.Content)"
    } catch {
        Write-Host "Error: $($_.Exception.Message)" -ForegroundColor Red
    }
    Write-Host "-----------------"
}
