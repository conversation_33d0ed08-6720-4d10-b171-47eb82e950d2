import 'package:flutter_test/flutter_test.dart';
import 'package:mockito/mockito.dart';
import 'package:mockito/annotations.dart';
import 'package:poultry_dz_mobile/models/feed/feed_consumption_log.dart';
import 'package:poultry_dz_mobile/models/feed/feed_plan.dart';
import 'package:poultry_dz_mobile/providers/feed_provider.dart';
import 'package:poultry_dz_mobile/services/api/feed_api_service.dart';
import 'package:poultry_dz_mobile/services/database/database_helper.dart';
import 'package:poultry_dz_mobile/services/sync/sync_service.dart';

import 'feed_provider_test.mocks.dart';

@GenerateMocks([
  FeedApiService,
  DatabaseHelper,
  SyncService,
])
void main() {
  group('FeedProvider Tests', () {
    late FeedProvider feedProvider;
    late MockFeedApiService mockApiService;
    late MockDatabaseHelper mockDatabaseHelper;
    late MockSyncService mockSyncService;

    setUp(() {
      mockApiService = MockFeedApiService();
      mockDatabaseHelper = MockDatabaseHelper();
      mockSyncService = MockSyncService();
      feedProvider = FeedProvider(
        apiService: mockApiService,
        databaseHelper: mockDatabaseHelper,
        syncService: mockSyncService,
      );
    });

    group('Feed Consumption Logs', () {
      final testLog = FeedConsumptionLog(
        id: '1',
        farmId: 'farm1',
        feedType: 'Starter',
        quantity: 50.0,
        unit: 'kg',
        date: DateTime.now(),
        createdAt: DateTime.now(),
        updatedAt: DateTime.now(),
      );

      test('should load feed consumption logs successfully', () async {
        // Arrange
        final logs = [testLog];
        when(mockDatabaseHelper.getFeedConsumptionLogs('farm1'))
            .thenAnswer((_) async => logs);
        when(mockApiService.getFeedConsumptionLogs('farm1'))
            .thenAnswer((_) async => logs);

        // Act
        await feedProvider.loadFeedConsumptionLogs('farm1');

        // Assert
        expect(feedProvider.feedConsumptionLogs, equals(logs));
        expect(feedProvider.isLoading, false);
        verify(mockDatabaseHelper.getFeedConsumptionLogs('farm1')).called(1);
      });

      test('should add feed consumption log successfully', () async {
        // Arrange
        when(mockDatabaseHelper.insertFeedConsumptionLog(testLog))
            .thenAnswer((_) async => 'generated_id');
        when(mockApiService.createFeedConsumptionLog(any))
            .thenAnswer((_) async => testLog.copyWith(id: 'generated_id'));

        // Act
        await feedProvider.addFeedConsumptionLog(testLog);

        // Assert
        expect(feedProvider.feedConsumptionLogs.length, 1);
        expect(feedProvider.feedConsumptionLogs.first.id, 'generated_id');
        verify(mockDatabaseHelper.insertFeedConsumptionLog(any)).called(1);
        verify(mockSyncService.scheduleSync()).called(1);
      });

      test('should update feed consumption log successfully', () async {
        // Arrange
        feedProvider.feedConsumptionLogs.add(testLog);
        final updatedLog = testLog.copyWith(quantity: 75.0);
        when(mockDatabaseHelper.updateFeedConsumptionLog(updatedLog))
            .thenAnswer((_) async => {});
        when(mockApiService.updateFeedConsumptionLog(updatedLog.id!, updatedLog))
            .thenAnswer((_) async => updatedLog);

        // Act
        await feedProvider.updateFeedConsumptionLog(updatedLog);

        // Assert
        expect(feedProvider.feedConsumptionLogs.first.quantity, 75.0);
        verify(mockDatabaseHelper.updateFeedConsumptionLog(updatedLog)).called(1);
        verify(mockSyncService.scheduleSync()).called(1);
      });

      test('should delete feed consumption log successfully', () async {
        // Arrange
        feedProvider.feedConsumptionLogs.add(testLog);
        when(mockDatabaseHelper.deleteFeedConsumptionLog('1'))
            .thenAnswer((_) async => {});
        when(mockApiService.deleteFeedConsumptionLog('1'))
            .thenAnswer((_) async => {});

        // Act
        await feedProvider.deleteFeedConsumptionLog('1');

        // Assert
        expect(feedProvider.feedConsumptionLogs.isEmpty, true);
        verify(mockDatabaseHelper.deleteFeedConsumptionLog('1')).called(1);
        verify(mockSyncService.scheduleSync()).called(1);
      });

      test('should handle offline mode for adding logs', () async {
        // Arrange
        when(mockDatabaseHelper.insertFeedConsumptionLog(testLog))
            .thenAnswer((_) async => 'local_id');
        when(mockApiService.createFeedConsumptionLog(any))
            .thenThrow(Exception('Network error'));

        // Act
        await feedProvider.addFeedConsumptionLog(testLog);

        // Assert
        expect(feedProvider.feedConsumptionLogs.length, 1);
        expect(feedProvider.feedConsumptionLogs.first.id, 'local_id');
        verify(mockDatabaseHelper.insertFeedConsumptionLog(any)).called(1);
        verify(mockSyncService.scheduleSync()).called(1);
      });
    });

    group('Feed Plans', () {
      final testPlan = FeedPlan(
        id: '1',
        name: 'Weekly Feed Plan',
        description: 'Test feed plan',
        startDate: DateTime.now(),
        endDate: DateTime.now().add(const Duration(days: 30)),
        plannedConsumption: 100.0,
        poultryType: 'Broiler',
        feedType: 'Starter',
        dailyAmount: 3.33,
        status: 'active',
        createdAt: DateTime.now(),
        updatedAt: DateTime.now(),
      );

      test('should load feed plans successfully', () async {
        // Arrange
        final plans = [testPlan];
        when(mockDatabaseHelper.getFeedPlans('farm1'))
            .thenAnswer((_) async => plans);
        when(mockApiService.getFeedPlans('farm1'))
            .thenAnswer((_) async => plans);

        // Act
        await feedProvider.loadFeedPlans('farm1');

        // Assert
        expect(feedProvider.feedPlans, equals(plans));
        expect(feedProvider.isLoading, false);
        verify(mockDatabaseHelper.getFeedPlans('farm1')).called(1);
      });

      test('should add feed plan successfully', () async {
        // Arrange
        when(mockDatabaseHelper.insertFeedPlan(testPlan))
            .thenAnswer((_) async => 'generated_id');
        when(mockApiService.createFeedPlan(any))
            .thenAnswer((_) async => testPlan.copyWith(id: 'generated_id'));

        // Act
        await feedProvider.addFeedPlan(testPlan);

        // Assert
        expect(feedProvider.feedPlans.length, 1);
        expect(feedProvider.feedPlans.first.id, 'generated_id');
        verify(mockDatabaseHelper.insertFeedPlan(any)).called(1);
        verify(mockSyncService.scheduleSync()).called(1);
      });

      test('should update feed plan successfully', () async {
        // Arrange
        feedProvider.feedPlans.add(testPlan);
        final updatedPlan = testPlan.copyWith(targetQuantity: 150.0);
        when(mockDatabaseHelper.updateFeedPlan(updatedPlan))
            .thenAnswer((_) async => {});
        when(mockApiService.updateFeedPlan(updatedPlan.id!, updatedPlan))
            .thenAnswer((_) async => updatedPlan);

        // Act
        await feedProvider.updateFeedPlan(updatedPlan);

        // Assert
        expect(feedProvider.feedPlans.first.targetQuantity, 150.0);
        verify(mockDatabaseHelper.updateFeedPlan(updatedPlan)).called(1);
        verify(mockSyncService.scheduleSync()).called(1);
      });

      test('should delete feed plan successfully', () async {
        // Arrange
        feedProvider.feedPlans.add(testPlan);
        when(mockDatabaseHelper.deleteFeedPlan('1'))
            .thenAnswer((_) async => {});
        when(mockApiService.deleteFeedPlan('1'))
            .thenAnswer((_) async => {});

        // Act
        await feedProvider.deleteFeedPlan('1');

        // Assert
        expect(feedProvider.feedPlans.isEmpty, true);
        verify(mockDatabaseHelper.deleteFeedPlan('1')).called(1);
        verify(mockSyncService.scheduleSync()).called(1);
      });
    });

    group('Analytics and Statistics', () {
      test('should get feed statistics successfully', () async {
        // Arrange
        final stats = {
          'totalConsumption': 500.0,
          'averageDaily': 25.0,
          'mostUsedFeedType': 'Starter',
        };
        when(mockApiService.getFeedStatistics('farm1', any, any))
            .thenAnswer((_) async => stats);

        // Act
        final result = await feedProvider.getFeedStatistics(
          'farm1',
          DateTime.now().subtract(const Duration(days: 30)),
          DateTime.now(),
        );

        // Assert
        expect(result, equals(stats));
        verify(mockApiService.getFeedStatistics('farm1', any, any)).called(1);
      });

      test('should get consumption forecast successfully', () async {
        // Arrange
        final forecast = {
          'nextWeek': 175.0,
          'nextMonth': 700.0,
          'trend': 'increasing',
        };
        when(mockApiService.getConsumptionForecast('farm1', 30))
            .thenAnswer((_) async => forecast);

        // Act
        final result = await feedProvider.getConsumptionForecast('farm1', 30);

        // Assert
        expect(result, equals(forecast));
        verify(mockApiService.getConsumptionForecast('farm1', 30)).called(1);
      });
    });

    group('Error Handling', () {
      test('should handle API errors gracefully', () async {
        // Arrange
        when(mockDatabaseHelper.getFeedConsumptionLogs('farm1'))
            .thenAnswer((_) async => []);
        when(mockApiService.getFeedConsumptionLogs('farm1'))
            .thenThrow(Exception('API Error'));

        // Act
        await feedProvider.loadFeedConsumptionLogs('farm1');

        // Assert
        expect(feedProvider.isLoading, false);
        expect(feedProvider.error, isNotNull);
        expect(feedProvider.error, contains('API Error'));
      });

      test('should handle database errors gracefully', () async {
        // Arrange
        when(mockDatabaseHelper.getFeedConsumptionLogs('farm1'))
            .thenThrow(Exception('Database Error'));

        // Act
        await feedProvider.loadFeedConsumptionLogs('farm1');

        // Assert
        expect(feedProvider.isLoading, false);
        expect(feedProvider.error, isNotNull);
        expect(feedProvider.error, contains('Database Error'));
      });
    });

    group('Sync Operations', () {
      test('should trigger sync after data modifications', () async {
        // Arrange
        final testLog = FeedConsumptionLog(
          farmId: 'farm1',
          feedType: 'Starter',
          quantity: 50.0,
          unit: 'kg',
          date: DateTime.now(),
          createdAt: DateTime.now(),
          updatedAt: DateTime.now(),
        );
        when(mockDatabaseHelper.insertFeedConsumptionLog(testLog))
            .thenAnswer((_) async => 'local_id');

        // Act
        await feedProvider.addFeedConsumptionLog(testLog);

        // Assert
        verify(mockSyncService.scheduleSync()).called(1);
      });

      test('should handle sync completion', () async {
        // Arrange
        when(mockSyncService.syncFeedData())
            .thenAnswer((_) async => {});

        // Act
        await feedProvider.syncData();

        // Assert
        verify(mockSyncService.syncFeedData()).called(1);
      });
    });

    group('Data Filtering and Sorting', () {
      test('should filter feed consumption logs by date range', () {
        // Arrange
        final now = DateTime.now();
        final logs = [
          FeedConsumptionLog(
            id: '1',
            farmId: 'farm1',
            feedType: 'Starter',
            quantity: 50.0,
            unit: 'kg',
            date: now.subtract(const Duration(days: 5)),
            createdAt: now,
            updatedAt: now,
          ),
          FeedConsumptionLog(
            id: '2',
            farmId: 'farm1',
            feedType: 'Grower',
            quantity: 75.0,
            unit: 'kg',
            date: now.subtract(const Duration(days: 15)),
            createdAt: now,
            updatedAt: now,
          ),
        ];
        feedProvider.feedConsumptionLogs.addAll(logs);

        // Act
        final filtered = feedProvider.getFilteredLogs(
          startDate: now.subtract(const Duration(days: 10)),
          endDate: now,
        );

        // Assert
        expect(filtered.length, 1);
        expect(filtered.first.id, '1');
      });

      test('should filter feed consumption logs by feed type', () {
        // Arrange
        final now = DateTime.now();
        final logs = [
          FeedConsumptionLog(
            id: '1',
            farmId: 'farm1',
            feedType: 'Starter',
            quantity: 50.0,
            unit: 'kg',
            date: now,
            createdAt: now,
            updatedAt: now,
          ),
          FeedConsumptionLog(
            id: '2',
            farmId: 'farm1',
            feedType: 'Grower',
            quantity: 75.0,
            unit: 'kg',
            date: now,
            createdAt: now,
            updatedAt: now,
          ),
        ];
        feedProvider.feedConsumptionLogs.addAll(logs);

        // Act
        final filtered = feedProvider.getFilteredLogs(feedType: 'Starter');

        // Assert
        expect(filtered.length, 1);
        expect(filtered.first.feedType, 'Starter');
      });
    });
  });
}