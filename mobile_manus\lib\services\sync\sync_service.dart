import 'dart:async';
import 'dart:convert';
import 'package:connectivity_plus/connectivity_plus.dart';
import 'package:mobile_manus/services/database/database_helper.dart';
import 'package:mobile_manus/services/api/auth_service.dart';
import 'package:mobile_manus/services/api/eleveur_service.dart';
import 'package:mobile_manus/services/api/veterinaire_service.dart';
import 'package:mobile_manus/services/api/marchand_service.dart';
import 'package:mobile_manus/services/api/marketplace_service.dart';
import 'package:workmanager/workmanager.dart';

class SyncService {
  static final SyncService _instance = SyncService._internal();
  factory SyncService() => _instance;
  SyncService._internal();

  final DatabaseHelper _databaseHelper = DatabaseHelper();
  final Connectivity _connectivity = Connectivity();

  // API Services
  final AuthService _authService = AuthService();
  final EleveurService _eleveurService = EleveurService();
  final VeterinaireService _veterinaireService = VeterinaireService();
  final MarchandService _marchandService = MarchandService();
  final MarketplaceService _marketplaceService = MarketplaceService();

  bool _isSyncing = false;
  Timer? _syncTimer;
  StreamSubscription<List<ConnectivityResult>>? _connectivitySubscription;

  // Sync status callbacks
  Function(bool isOnline)? onConnectivityChanged;
  Function(String status)? onSyncStatusChanged;
  Function(String error)? onSyncError;

  void initialize() {
    _startConnectivityMonitoring();
    _startPeriodicSync();
  }

  void dispose() {
    _connectivitySubscription?.cancel();
    _syncTimer?.cancel();
  }
  Future<void> _startConnectivityMonitoring() async {
    _connectivitySubscription = _connectivity.onConnectivityChanged.listen(
      (List<ConnectivityResult> results) {
        final isOnline = results.isNotEmpty && results.first != ConnectivityResult.none;
        onConnectivityChanged?.call(isOnline);

        if (isOnline && !_isSyncing) {
          _performSync();
        }
      },
    );
  }

  void _startPeriodicSync() {
    _syncTimer = Timer.periodic(const Duration(minutes: 5), (timer) {
      if (!_isSyncing) {
        _performSync();
      }
    });
  }

  Future<bool> isOnline() async {
    final connectivityResult = await _connectivity.checkConnectivity();
    return connectivityResult != ConnectivityResult.none;
  }

  Future<void> syncNow() async {
    if (_isSyncing) return;
    await _performSync();
  }

  Future<void> _performSync() async {
    if (_isSyncing) return;

    final online = await isOnline();
    if (!online) return;

    _isSyncing = true;
    onSyncStatusChanged?.call('Synchronisation en cours...');

    try {
      // Sync pending operations from queue
      await _syncPendingOperations();

      // Download latest data from server
      await _downloadLatestData();

      onSyncStatusChanged?.call('Synchronisation terminée');
    } catch (e) {
      onSyncError?.call('Erreur de synchronisation: $e');
    } finally {
      _isSyncing = false;
    }
  }

  Future<void> _syncPendingOperations() async {
    final pendingItems = await _databaseHelper.getPendingSyncItems();

    for (final item in pendingItems) {
      try {
        await _processSyncItem(item);
        await _databaseHelper.removeSyncItem(item['id']);
      } catch (e) {
        await _databaseHelper.updateSyncItemAttempt(item['id'], e.toString());

        // Remove items that have failed too many times
        if (item['attempts'] >= 3) {
          await _databaseHelper.removeSyncItem(item['id']);
        }
      }
    }
  }

  Future<void> _processSyncItem(Map<String, dynamic> item) async {
    final tableName = item['tableName'] as String;
    final operation = item['operation'] as String;
    final recordId = item['recordId'] as int?;
    final data = item['data'] as String?;

    Map<String, dynamic>? parsedData;
    if (data != null) {
      try {
        parsedData = jsonDecode(data);
      } catch (e) {
        throw Exception('Invalid data format in sync queue');
      }
    }

    switch (tableName) {
      case 'eleveurs':
        await _syncEleveurOperation(operation, recordId, parsedData);
        break;
      case 'veterinaires':
        await _syncVeterinaireOperation(operation, recordId, parsedData);
        break;
      case 'feed_plans':
        await _syncFeedPlanOperation(operation, recordId, parsedData);
        break;
      case 'feed_consumption_logs':
        await _syncFeedConsumptionOperation(operation, recordId, parsedData);
        break;
      case 'annonces':
        await _syncAnnonceOperation(operation, recordId, parsedData);
        break;
      default:
        throw Exception('Unknown table: $tableName');
    }
  }

  Future<void> _syncEleveurOperation(String operation, int? recordId, Map<String, dynamic>? data) async {
    switch (operation) {
      case 'CREATE':
        if (data != null) {
          await _eleveurService.createEleveur(data);
        }
        break;
      case 'UPDATE':
        if (recordId != null && data != null) {
          await _eleveurService.updateEleveur(recordId, data);
        }
        break;
      case 'DELETE':
        if (recordId != null) {
          await _eleveurService.deleteEleveur(recordId);
        }
        break;
    }
  }

  Future<void> _syncVeterinaireOperation(String operation, int? recordId, Map<String, dynamic>? data) async {
    switch (operation) {
      case 'CREATE':
        if (data != null) {
          await _veterinaireService.createVeterinaire(data);
        }
        break;
      case 'UPDATE':
        if (recordId != null && data != null) {
          await _veterinaireService.updateVeterinaire(recordId, data);
        }
        break;
      case 'DELETE':
        if (recordId != null) {
          await _veterinaireService.deleteVeterinaire(recordId);
        }
        break;
    }
  }

  Future<void> _syncFeedPlanOperation(String operation, int? recordId, Map<String, dynamic>? data) async {
    // TODO: Implement feed plan sync operations
    // This would require corresponding API endpoints
  }

  Future<void> _syncFeedConsumptionOperation(String operation, int? recordId, Map<String, dynamic>? data) async {
    // TODO: Implement feed consumption sync operations
    // This would require corresponding API endpoints
  }

  Future<void> _syncAnnonceOperation(String operation, int? recordId, Map<String, dynamic>? data) async {
    switch (operation) {
      case 'CREATE':
        if (data != null) {
          await _marketplaceService.createAnnonce(data);
        }
        break;
      case 'UPDATE':
        if (recordId != null && data != null) {
          await _marketplaceService.updateAnnonce(recordId, data);
        }
        break;
      case 'DELETE':
        if (recordId != null) {
          await _marketplaceService.deleteAnnonce(recordId);
        }
        break;
    }
  }

  Future<void> _downloadLatestData() async {
    try {
      // Download latest annonces
      final annoncesData = await _marketplaceService.getAnnonces(page: 1, limit: 50);
      // TODO: Update local database with latest annonces

      // Download other data based on user role
      // TODO: Implement role-specific data downloads

    } catch (e) {
      throw Exception('Failed to download latest data: $e');
    }
  }

  // Offline operation methods
  Future<void> createOfflineEleveur(Map<String, dynamic> eleveurData) async {
    // Save to local database
    // Add to sync queue
    await _databaseHelper.addToSyncQueue(
      tableName: 'eleveurs',
      operation: 'CREATE',
      data: eleveurData,
    );
  }

  Future<void> updateOfflineEleveur(int id, Map<String, dynamic> eleveurData) async {
    // Update local database
    // Add to sync queue
    await _databaseHelper.addToSyncQueue(
      tableName: 'eleveurs',
      recordId: id,
      operation: 'UPDATE',
      data: eleveurData,
    );
  }

  Future<void> deleteOfflineEleveur(int id) async {
    // Mark as deleted in local database
    // Add to sync queue
    await _databaseHelper.addToSyncQueue(
      tableName: 'eleveurs',
      recordId: id,
      operation: 'DELETE',
    );
  }

  Future<void> createOfflineAnnonce(Map<String, dynamic> annonceData) async {
    // Save to local database
    // Add to sync queue
    await _databaseHelper.addToSyncQueue(
      tableName: 'annonces',
      operation: 'CREATE',
      data: annonceData,
    );
  }

  Future<void> updateOfflineAnnonce(int id, Map<String, dynamic> annonceData) async {
    // Update local database
    // Add to sync queue
    await _databaseHelper.addToSyncQueue(
      tableName: 'annonces',
      recordId: id,
      operation: 'UPDATE',
      data: annonceData,
    );
  }

  Future<void> deleteOfflineAnnonce(int id) async {
    // Mark as deleted in local database
    // Add to sync queue
    await _databaseHelper.addToSyncQueue(
      tableName: 'annonces',
      recordId: id,
      operation: 'DELETE',
    );
  }

  Future<void> createOfflineFeedPlan(Map<String, dynamic> feedPlanData) async {
    // Save to local database
    // Add to sync queue
    await _databaseHelper.addToSyncQueue(
      tableName: 'feed_plans',
      operation: 'CREATE',
      data: feedPlanData,
    );
  }

  Future<void> createOfflineFeedConsumption(Map<String, dynamic> consumptionData) async {
    // Save to local database
    // Add to sync queue
    await _databaseHelper.addToSyncQueue(
      tableName: 'feed_consumption_logs',
      operation: 'CREATE',
      data: consumptionData,
    );
  }

  // Utility methods
  Future<int> getPendingSyncCount() async {
    final pendingItems = await _databaseHelper.getPendingSyncItems();
    return pendingItems.length;
  }

  Future<DateTime?> getLastSyncTime() async {
    final lastSyncString = await _databaseHelper.getSetting('last_sync_time');
    if (lastSyncString != null) {
      return DateTime.parse(lastSyncString);
    }
    return null;
  }

  Future<void> setLastSyncTime(DateTime time) async {
    await _databaseHelper.setSetting('last_sync_time', time.toIso8601String());
  }

  Future<void> clearSyncQueue() async {
    final pendingItems = await _databaseHelper.getPendingSyncItems();
    for (final item in pendingItems) {
      await _databaseHelper.removeSyncItem(item['id']);
    }
  }

  Future<void> initializeWorkmanager() async {
    await Workmanager().initialize(
      callbackDispatcher,
      isInDebugMode: true,
    );
  }

  Future<void> registerFeedSyncTask() async {
    await Workmanager().registerPeriodicTask(
      'feedSync',
      'syncFeedData',
      frequency: const Duration(hours: 1),
      constraints: Constraints(
        networkType: NetworkType.connected,
        requiresBatteryNotLow: true,
      ),
    );
  }

  @pragma('vm:entry-point')
  static void callbackDispatcher() {
    Workmanager().executeTask((task, inputData) async {
      switch (task) {
        case 'syncFeedData':
          await SyncService().syncNow();
          break;
      }
      return true;
    });
  }
}
