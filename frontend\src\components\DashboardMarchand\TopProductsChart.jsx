
import { useTheme } from '@mui/material/styles';
import { Box, Typography } from '@mui/material';
import { ResponsiveContainer, BarChart, Bar, XAxis, YAxis, CartesianGrid, Tooltip, Legend } from 'recharts';

/**
 * Composant pour afficher les produits les plus vendus sous forme de graphique à barres
 * @param {Object} props - Propriétés du composant
 * @param {Array} props.products - Liste des produits les plus vendus
 */
const TopProductsChart = ({ products = [] }) => {
  const theme = useTheme();

  // Formater les données pour le graphique
  const formattedData = products.map(product => ({
    name: truncateText(product.name, 15),
    ventes: parseInt(product.total_sold) || 0,
    fullName: product.name // Pour l'affichage complet dans le tooltip
  }));

  // Si aucune donnée, afficher un message
  if (formattedData.length === 0) {
    return (
      <Box sx={{ display: 'flex', justifyContent: 'center', alignItems: 'center', height: 300 }}>
        <Typography variant="body1" color="textSecondary">
          Aucune donnée de vente disponible
        </Typography>
      </Box>
    );
  }

  return (
    <Box sx={{ width: '100%', height: 300 }}>
      <ResponsiveContainer>
        <BarChart
          data={formattedData}
          margin={{ top: 5, right: 30, left: 20, bottom: 5 }}
          layout="vertical"
        >
          <CartesianGrid strokeDasharray="3 3" stroke={theme.palette.divider} />
          <XAxis
            type="number"
            stroke={theme.palette.text.secondary}
            style={{ fontSize: '0.75rem' }}
          />
          <YAxis
            dataKey="name"
            type="category"
            stroke={theme.palette.text.secondary}
            style={{ fontSize: '0.75rem' }}
            width={100}
          />
          <Tooltip
            formatter={(value) => [`${value} unités`, 'Quantité vendue']}
            labelFormatter={(label) => `Produit: ${formattedData.find(item => item.name === label)?.fullName || label}`}
            contentStyle={{
              backgroundColor: theme.palette.background.paper,
              border: `1px solid ${theme.palette.divider}`,
              borderRadius: 4,
              boxShadow: theme.shadows[3]
            }}
          />
          <Legend />
          <Bar
            dataKey="ventes"
            name="Quantité vendue"
            fill={theme.palette.success.main}
            radius={[0, 4, 4, 0]}
          />
        </BarChart>
      </ResponsiveContainer>
    </Box>
  );
};

/**
 * Tronque un texte s'il dépasse une certaine longueur
 * @param {string} text - Texte à tronquer
 * @param {number} maxLength - Longueur maximale
 * @returns {string} - Texte tronqué
 */
const truncateText = (text, maxLength) => {
  if (!text) return '';
  return text.length > maxLength ? `${text.substring(0, maxLength)}...` : text;
};

export default TopProductsChart;
