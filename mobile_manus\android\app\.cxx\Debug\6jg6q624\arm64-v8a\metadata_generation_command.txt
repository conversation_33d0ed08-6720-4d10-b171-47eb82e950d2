                        -HC:\Users\<USER>\fvm\versions\3.10.4\packages\flutter_tools\gradle\src\main\groovy
-DCMAKE_SYSTEM_NAME=Android
-DCMAKE_EXPORT_COMPILE_COMMANDS=ON
-DCMAKE_SYSTEM_VERSION=21
-DANDROID_PLATFORM=android-21
-DANDROID_ABI=arm64-v8a
-DCMAKE_ANDROID_ARCH_ABI=arm64-v8a
-DANDROID_NDK=K:\AndroidSDK\ndk\27.0.12077973
-DCMAKE_ANDROID_NDK=K:\AndroidSDK\ndk\27.0.12077973
-DCMAKE_TOOLCHAIN_FILE=K:\AndroidSDK\ndk\27.0.12077973\build\cmake\android.toolchain.cmake
-DCMAKE_MAKE_PROGRAM=K:\AndroidSDK\cmake\3.22.1\bin\ninja.exe
-DCMAKE_LIBRARY_OUTPUT_DIRECTORY=K:\Projets_Sites_Web\Poultray-dz-TraeDev\Web_App\Poultraydz-Trae\mobile_manus\build\app\intermediates\cxx\Debug\6jg6q624\obj\arm64-v8a
-DCMAKE_RUNTIME_OUTPUT_DIRECTORY=K:\Projets_Sites_Web\Poultray-dz-TraeDev\Web_App\Poultraydz-Trae\mobile_manus\build\app\intermediates\cxx\Debug\6jg6q624\obj\arm64-v8a
-DCMAKE_BUILD_TYPE=Debug
-BK:\Projets_Sites_Web\Poultray-dz-TraeDev\Web_App\Poultraydz-Trae\mobile_manus\android\app\.cxx\Debug\6jg6q624\arm64-v8a
-GNinja
-Wno-dev
--no-warn-unused-cli
                        Build command args: []
                        Version: 2