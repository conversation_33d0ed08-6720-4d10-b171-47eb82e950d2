/**
 * Service d'authentification simple pour les tests
 * Bypasse Firebase et communique directement avec le backend
 */

import axiosInstance from '../utils/axiosConfig';

// Constantes pour la gestion des timeouts
const AUTH_TIMEOUT = 20000; // 20 secondes pour les opérations d'authentification

// Fonction utilitaire pour gérer les timeouts
const withTimeout = (promise, ms, errorMessage) => {
  let timeoutId;
  const timeoutPromise = new Promise((_, reject) => {
    timeoutId = setTimeout(() => {
      reject(new Error(errorMessage || `L'opération a dépassé le délai de ${ms/1000} secondes`));
    }, ms);
  });

  return Promise.race([promise, timeoutPromise])
    .finally(() => clearTimeout(timeoutId));
};

// Service d'authentification simple
class SimpleAuthService {
  // Connexion d'un utilisateur (sans Firebase)
  async login(email, password) {
    try {
      console.log('🔐 SimpleAuth: Tentative de connexion directe au backend');

      // Appel direct au backend sans Firebase
      const response = await withTimeout(        axiosInstance.post('/api/auth/login', {
          email,
          password
        }),
        AUTH_TIMEOUT,
        'La connexion au serveur a pris trop de temps'
      );

      console.log('✅ SimpleAuth: Réponse du backend reçue:', response.data);

      // Stocker le token JWT dans localStorage
    const jwtToken = response.data.token;
    localStorage.setItem('token', jwtToken);

    // Ensure user object has role field (fallback to empty string)
    const user = {
      ...response.data.user,
      role: response.data.user?.role || ''
    };

    return {
      token: jwtToken,
      user
    };
    } catch (error) {
      console.error('❌ SimpleAuth: Erreur lors de la connexion:', error);
      throw this.handleError(error);
    }
  }

  // Inscription d'un utilisateur (sans Firebase)
  async register(userData) {
    try {
      console.log('📝 SimpleAuth: Tentative d\'inscription directe au backend');

      const response = await withTimeout(
        axiosInstance.post('/auth/register', userData),
        AUTH_TIMEOUT,
        'L\'inscription a pris trop de temps'
      );

      return response.data;
    } catch (error) {
      console.error('❌ SimpleAuth: Erreur lors de l\'inscription:', error);
      throw this.handleError(error);
    }
  }

  // Déconnexion
  async logout() {
    try {
      console.log('🚪 SimpleAuth: Déconnexion');
      localStorage.removeItem('token');
      localStorage.removeItem('firebase_data');
      localStorage.removeItem('user_credentials');
    } catch (error) {
      console.error('❌ SimpleAuth: Erreur lors de la déconnexion:', error);
      throw this.handleError(error);
    }
  }

  // Gestion des erreurs
  handleError(error) {
    let message = 'Une erreur est survenue';

    if (error.response) {
      // Erreur de réponse du serveur
      message = error.response.data?.message || `Erreur serveur: ${error.response.status}`;
    } else if (error.request) {
      // Erreur de réseau
      message = 'Impossible de contacter le serveur. Vérifiez votre connexion.';
    } else if (error.message) {
      // Autre erreur
      message = error.message;
    }

    return new Error(message);
  }
}

export const simpleAuth = new SimpleAuthService();
