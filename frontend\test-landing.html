<!DOCTYPE html>
<html lang="fr">
<head>
  <meta charset="UTF-8">
  <meta name="viewport" content="width=device-width, initial-scale=1.0">
  <title>Test Landing Page Poultray DZ</title>
  <style>
    /* Reset CSS */
    * {
      margin: 0;
      padding: 0;
      box-sizing: border-box;
    }

    body {
      font-family: Arial, sans-serif;
      line-height: 1.6;
      color: #333;
      background-color: #f5f5f5;
    }

    /* Navbar */
    .navbar {
      background-color: #4CAF50;
      color: white;
      padding: 1rem 2rem;
      display: flex;
      justify-content: space-between;
      align-items: center;
      position: fixed;
      width: 100%;
      top: 0;
      z-index: 1000;
      box-shadow: 0 2px 10px rgba(0,0,0,0.1);
    }

    .logo {
      display: flex;
      align-items: center;
      font-weight: bold;
      font-size: 1.5rem;
    }

    .logo svg {
      height: 40px;
      margin-right: 10px;
    }

    .nav-links {
      display: flex;
      gap: 20px;
    }

    .nav-links a {
      color: white;
      text-decoration: none;
      font-weight: 500;
    }

    /* Hero Section */
    .hero {
      min-height: 80vh;
      background: linear-gradient(135deg, #81C784 0%, #4CAF50 100%);
      color: white;
      display: flex;
      align-items: center;
      padding: 6rem 2rem 4rem;
      margin-top: 60px;
    }

    .hero-content {
      max-width: 1200px;
      margin: 0 auto;
      display: flex;
      align-items: center;
      justify-content: space-between;
      gap: 2rem;
    }

    .hero-text {
      flex: 1;
    }

    .hero-text h1 {
      font-size: 2.5rem;
      margin-bottom: 1rem;
      font-weight: bold;
    }

    .hero-text p {
      font-size: 1.2rem;
      margin-bottom: 2rem;
    }

    .hero-image {
      flex: 1;
      display: flex;
      justify-content: center;
    }

    .hero-image svg {
      max-width: 100%;
      height: auto;
    }

    .btn {
      display: inline-block;
      padding: 0.8rem 2rem;
      border-radius: 30px;
      font-weight: bold;
      text-decoration: none;
      cursor: pointer;
      transition: all 0.3s ease;
    }

    .btn-primary {
      background-color: #FFC107;
      color: #333;
      margin-right: 1rem;
    }

    .btn-secondary {
      background-color: transparent;
      color: white;
      border: 2px solid white;
    }

    .btn:hover {
      transform: translateY(-3px);
      box-shadow: 0 4px 10px rgba(0,0,0,0.2);
    }

    /* Features Section */
    .features {
      padding: 4rem 2rem;
      background-color: white;
    }

    .section-title {
      text-align: center;
      margin-bottom: 3rem;
      position: relative;
    }

    .section-title h2 {
      font-size: 2rem;
      color: #333;
    }

    .section-title::after {
      content: '';
      position: absolute;
      bottom: -10px;
      left: 50%;
      width: 80px;
      height: 4px;
      background-color: #4CAF50;
      transform: translateX(-50%);
    }

    .features-grid {
      display: grid;
      grid-template-columns: repeat(auto-fit, minmax(300px, 1fr));
      gap: 2rem;
      max-width: 1200px;
      margin: 0 auto;
    }

    .feature-card {
      background-color: white;
      border-radius: 8px;
      padding: 2rem;
      box-shadow: 0 5px 15px rgba(0,0,0,0.1);
      transition: transform 0.3s ease, box-shadow 0.3s ease;
    }

    .feature-card:hover {
      transform: translateY(-10px);
      box-shadow: 0 15px 30px rgba(0,0,0,0.15);
    }

    .feature-icon {
      font-size: 2.5rem;
      color: #4CAF50;
      margin-bottom: 1rem;
    }

    .feature-title {
      font-size: 1.2rem;
      font-weight: bold;
      margin-bottom: 1rem;
    }

    /* Footer */
    .footer {
      background-color: #333;
      color: white;
      padding: 3rem 2rem;
    }

    .footer-content {
      max-width: 1200px;
      margin: 0 auto;
      display: grid;
      grid-template-columns: repeat(auto-fit, minmax(250px, 1fr));
      gap: 2rem;
    }

    .footer-logo {
      font-size: 1.5rem;
      font-weight: bold;
      margin-bottom: 1rem;
    }

    .footer-links h3 {
      font-size: 1.2rem;
      margin-bottom: 1rem;
      position: relative;
    }

    .footer-links h3::after {
      content: '';
      position: absolute;
      bottom: -5px;
      left: 0;
      width: 40px;
      height: 3px;
      background-color: #4CAF50;
    }

    .footer-links ul {
      list-style: none;
    }

    .footer-links li {
      margin-bottom: 0.5rem;
    }

    .footer-links a {
      color: #ccc;
      text-decoration: none;
      transition: color 0.3s ease;
    }

    .footer-links a:hover {
      color: #4CAF50;
    }

    .footer-bottom {
      text-align: center;
      margin-top: 2rem;
      padding-top: 2rem;
      border-top: 1px solid #444;
    }

    /* Responsive */
    @media (max-width: 768px) {
      .hero-content {
        flex-direction: column;
      }

      .hero-image {
        order: -1;
      }

      .nav-links {
        display: none;
      }
    }
  </style>
</head>
<body>
  <!-- Navbar -->
  <nav class="navbar">
    <div class="logo">
      <svg xmlns="http://www.w3.org/2000/svg" width="40" height="40" viewBox="0 0 200 200">
        <defs>
          <linearGradient id="gradient" x1="0%" y1="0%" x2="100%" y2="100%">
            <stop offset="0%" stop-color="#ffffff" />
            <stop offset="100%" stop-color="#ffffff" />
          </linearGradient>
        </defs>
        <circle cx="100" cy="100" r="90" fill="transparent" stroke="url(#gradient)" stroke-width="5" />
        <g transform="translate(50, 50)">
          <path d="M25,0 C40,0 50,10 60,30 C70,50 70,70 60,90 C50,110 30,100 20,80 C10,60 10,40 20,20 C25,10 35,0 50,0 Z" fill="white" />
          <ellipse cx="70" cy="50" rx="20" ry="30" fill="white" stroke="white" stroke-width="3" />
        </g>
      </svg>
      Poultray DZ
    </div>
    <div class="nav-links">
      <a href="#">Accueil</a>
      <a href="#">Fonctionnalités</a>
      <a href="#">Tarifs</a>
      <a href="#">FAQ</a>
      <a href="#">Contact</a>
    </div>
  </nav>

  <!-- Hero Section -->
  <section class="hero">
    <div class="hero-content">
      <div class="hero-text">
        <h1>Révolutionnez votre élevage de volailles</h1>
        <p>La première plateforme algérienne dédiée à la gestion et à la commercialisation des volailles.</p>
        <div class="hero-buttons">
          <a href="#" class="btn btn-primary">Créer un compte</a>
          <a href="#" class="btn btn-secondary">Contactez-nous</a>
        </div>
      </div>
      <div class="hero-image">
        <svg xmlns="http://www.w3.org/2000/svg" width="400" height="300" viewBox="0 0 800 600">
          <g transform="translate(400, 300)">
            <path d="M-100,-50 C-80,-70 -60,-80 -40,-80 C-20,-80 0,-70 20,-50 C40,-30 50,-10 50,10 C50,30 40,50 20,70 C0,90 -20,100 -40,100 C-60,100 -80,90 -100,70 C-120,50 -130,30 -130,10 C-130,-10 -120,-30 -100,-50 Z" fill="white" opacity="0.8"/>
            <ellipse cx="80" cy="20" rx="60" ry="80" fill="white" stroke="white" stroke-width="5"/>
            <path d="M-200,100 L-200,0 L-150,-50 L-100,0 L-100,100 Z" fill="white"/>
            <rect x="-180" y="50" width="30" height="50" fill="white"/>
            <circle cx="150" cy="-100" r="40" fill="white"/>
          </g>
        </svg>
      </div>
    </div>
  </section>

  <!-- Features Section -->
  <section class="features">
    <div class="section-title">
      <h2>Nos Fonctionnalités</h2>
    </div>
    <div class="features-grid">
      <div class="feature-card">
        <div class="feature-icon">🧑‍🌾</div>
        <h3 class="feature-title">Gestion des Éleveurs</h3>
        <p>Suivez et gérez facilement tous vos éleveurs partenaires avec des profils détaillés et des statistiques en temps réel.</p>
      </div>
      <div class="feature-card">
        <div class="feature-icon">🩺</div>
        <h3 class="feature-title">Suivi Vétérinaire</h3>
        <p>Accédez à des services vétérinaires à distance et suivez la santé de vos volailles avec des rapports détaillés.</p>
      </div>
      <div class="feature-card">
        <div class="feature-icon">🛒</div>
        <h3 class="feature-title">Marketplace</h3>
        <p>Vendez et achetez des volailles directement sur la plateforme avec un système de paiement sécurisé.</p>
      </div>
    </div>
  </section>

  <!-- Footer -->
  <footer class="footer">
    <div class="footer-content">
      <div class="footer-about">
        <div class="footer-logo">Poultray DZ</div>
        <p>La première plateforme algérienne dédiée à la gestion et à la commercialisation des volailles.</p>
      </div>
      <div class="footer-links">
        <h3>Liens Rapides</h3>
        <ul>
          <li><a href="#">Accueil</a></li>
          <li><a href="#">Fonctionnalités</a></li>
          <li><a href="#">Tarifs</a></li>
          <li><a href="#">FAQ</a></li>
        </ul>
      </div>
      <div class="footer-links">
        <h3>Contact</h3>
        <ul>
          <li>Email: <EMAIL></li>
          <li>Téléphone: +213 XX XX XX XX</li>
          <li>Adresse: Alger, Algérie</li>
        </ul>
      </div>
    </div>
    <div class="footer-bottom">
      <p>&copy; 2023 Poultray DZ. Tous droits réservés.</p>
    </div>
  </footer>
</body>
</html>