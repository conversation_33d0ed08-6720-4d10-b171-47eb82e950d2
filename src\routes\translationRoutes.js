const express = require('express');
const router = express.Router();
const pool = require('../config/database');
const { auth, checkRole } = require('../middleware/auth');

// @route   GET /api/translations
// @desc    Get all translations
// @access  Public
router.get('/', async (req, res) => {
  try {
    const { lang = 'fr' } = req.query;

    const translationsQuery = `
      SELECT key, value, language, category, created_at, updated_at
      FROM translations
      WHERE language = $1
      ORDER BY category, key
    `;

    const result = await pool.query(translationsQuery, [lang]);

    // Organiser les traductions par catégorie
    const translations = {};
    result.rows.forEach(row => {
      if (!translations[row.category]) {
        translations[row.category] = {};
      }
      translations[row.category][row.key] = row.value;
    });

    res.json({
      language: lang,
      translations
    });

  } catch (error) {
    console.error('Erreur lors de la récupération des traductions:', error);

    // Return default translations if database error
    const { lang = 'fr' } = req.query;
    const defaultTranslations = {
      fr: {
        general: {
          'app.title': 'Poultray DZ',
          'welcome': 'Bienvenue'
        },
        navigation: {
          'nav.dashboard': 'Tableau de bord',
          'nav.volailles': 'Volailles',
          'nav.ventes': 'Ventes'
        }
      },
      ar: {
        general: {
          'app.title': 'دواجن الجزائر',
          'welcome': 'مرحبا'
        },
        navigation: {
          'nav.dashboard': 'لوحة التحكم',
          'nav.volailles': 'الدواجن',
          'nav.ventes': 'المبيعات'
        }
      }
    };

    res.json({
      language: lang,
      translations: defaultTranslations[lang] || defaultTranslations.fr
    });
  }
});

// @route   GET /api/translations/languages
// @desc    Get available languages
// @access  Public
router.get('/languages', async (req, res) => {
  try {
    const languagesQuery = `
      SELECT DISTINCT language, COUNT(*) as translation_count
      FROM translations
      GROUP BY language
      ORDER BY language
    `;

    const result = await pool.query(languagesQuery);

    const languages = result.rows.map(row => ({
      code: row.language,
      name: getLanguageName(row.language),
      translationCount: parseInt(row.translation_count)
    }));

    res.json({ languages });

  } catch (error) {
    console.error('Erreur lors de la récupération des langues:', error);
    res.status(500).json({ message: 'Erreur serveur' });
  }
});

// @route   GET /api/translations/categories
// @desc    Get translation categories
// @access  Private/Admin
router.get('/categories', auth, checkRole(['admin']), async (req, res) => {
  try {
    const categoriesQuery = `
      SELECT DISTINCT category, COUNT(*) as key_count
      FROM translations
      GROUP BY category
      ORDER BY category
    `;

    const result = await pool.query(categoriesQuery);

    res.json({
      categories: result.rows.map(row => ({
        name: row.category,
        keyCount: parseInt(row.key_count)
      }))
    });

  } catch (error) {
    console.error('Erreur lors de la récupération des catégories:', error);
    res.status(500).json({ message: 'Erreur serveur' });
  }
});

// @route   POST /api/translations
// @desc    Create or update translation
// @access  Private/Admin
router.post('/', auth, checkRole(['admin']), async (req, res) => {
  try {
    const { key, value, language, category = 'general' } = req.body;

    if (!key || !value || !language) {
      return res.status(400).json({
        message: 'Clé, valeur et langue sont requis'
      });
    }

    // Vérifier si la traduction existe déjà
    const existingQuery = `
      SELECT id FROM translations
      WHERE key = $1 AND language = $2
    `;

    const existingResult = await pool.query(existingQuery, [key, language]);

    let result;
    if (existingResult.rows.length > 0) {
      // Mettre à jour la traduction existante
      const updateQuery = `
        UPDATE translations
        SET value = $1, category = $2, updated_at = NOW()
        WHERE key = $3 AND language = $4
        RETURNING *
      `;

      result = await pool.query(updateQuery, [value, category, key, language]);
    } else {
      // Créer une nouvelle traduction
      const insertQuery = `
        INSERT INTO translations (key, value, language, category, created_at, updated_at)
        VALUES ($1, $2, $3, $4, NOW(), NOW())
        RETURNING *
      `;

      result = await pool.query(insertQuery, [key, value, language, category]);
    }

    res.json({
      message: 'Traduction sauvegardée avec succès',
      translation: result.rows[0]
    });

  } catch (error) {
    console.error('Erreur lors de la sauvegarde de la traduction:', error);
    res.status(500).json({ message: 'Erreur serveur' });
  }
});

// @route   PUT /api/translations/:id
// @desc    Update translation
// @access  Private/Admin
router.put('/:id', auth, checkRole(['admin']), async (req, res) => {
  try {
    const { id } = req.params;
    const { key, value, language, category } = req.body;

    const updateQuery = `
      UPDATE translations
      SET key = $1, value = $2, language = $3, category = $4, updated_at = NOW()
      WHERE id = $5
      RETURNING *
    `;

    const result = await pool.query(updateQuery, [key, value, language, category, id]);

    if (result.rows.length === 0) {
      return res.status(404).json({ message: 'Traduction non trouvée' });
    }

    res.json({
      message: 'Traduction mise à jour avec succès',
      translation: result.rows[0]
    });

  } catch (error) {
    console.error('Erreur lors de la mise à jour de la traduction:', error);
    res.status(500).json({ message: 'Erreur serveur' });
  }
});

// @route   DELETE /api/translations/:id
// @desc    Delete translation
// @access  Private/Admin
router.delete('/:id', auth, checkRole(['admin']), async (req, res) => {
  try {
    const { id } = req.params;

    const deleteQuery = `
      DELETE FROM translations
      WHERE id = $1
      RETURNING *
    `;

    const result = await pool.query(deleteQuery, [id]);

    if (result.rows.length === 0) {
      return res.status(404).json({ message: 'Traduction non trouvée' });
    }

    res.json({ message: 'Traduction supprimée avec succès' });

  } catch (error) {
    console.error('Erreur lors de la suppression de la traduction:', error);
    res.status(500).json({ message: 'Erreur serveur' });
  }
});

// @route   POST /api/translations/bulk
// @desc    Bulk import translations
// @access  Private/Admin
router.post('/bulk', auth, checkRole(['admin']), async (req, res) => {
  try {
    const { translations, language, category = 'general' } = req.body;

    if (!translations || !language) {
      return res.status(400).json({
        message: 'Traductions et langue sont requis'
      });
    }

    const client = await pool.connect();

    try {
      await client.query('BEGIN');

      let insertedCount = 0;
      let updatedCount = 0;

      for (const [key, value] of Object.entries(translations)) {
        // Vérifier si la traduction existe
        const existingResult = await client.query(
          'SELECT id FROM translations WHERE key = $1 AND language = $2',
          [key, language]
        );

        if (existingResult.rows.length > 0) {
          // Mettre à jour
          await client.query(
            'UPDATE translations SET value = $1, category = $2, updated_at = NOW() WHERE key = $3 AND language = $4',
            [value, category, key, language]
          );
          updatedCount++;
        } else {
          // Insérer
          await client.query(
            'INSERT INTO translations (key, value, language, category, created_at, updated_at) VALUES ($1, $2, $3, $4, NOW(), NOW())',
            [key, value, language, category]
          );
          insertedCount++;
        }
      }

      await client.query('COMMIT');

      res.json({
        message: 'Import en lot terminé avec succès',
        inserted: insertedCount,
        updated: updatedCount,
        total: insertedCount + updatedCount
      });

    } catch (error) {
      await client.query('ROLLBACK');
      throw error;
    } finally {
      client.release();
    }

  } catch (error) {
    console.error('Erreur lors de l\'import en lot:', error);
    res.status(500).json({ message: 'Erreur serveur' });
  }
});

// @route   GET /api/translations/export
// @desc    Export translations
// @access  Private/Admin
router.get('/export', auth, checkRole(['admin']), async (req, res) => {
  try {
    const { language, category, format = 'json' } = req.query;

    let whereClause = '';
    let queryParams = [];
    let paramCount = 0;

    if (language) {
      paramCount++;
      whereClause += `WHERE language = $${paramCount}`;
      queryParams.push(language);
    }

    if (category) {
      paramCount++;
      whereClause += whereClause ? ` AND category = $${paramCount}` : `WHERE category = $${paramCount}`;
      queryParams.push(category);
    }

    const exportQuery = `
      SELECT key, value, language, category
      FROM translations
      ${whereClause}
      ORDER BY language, category, key
    `;

    const result = await pool.query(exportQuery, queryParams);

    if (format === 'csv') {
      // Export CSV
      const csv = [
        'key,value,language,category',
        ...result.rows.map(row =>
          `"${row.key}","${row.value}","${row.language}","${row.category}"`
        )
      ].join('\n');

      res.setHeader('Content-Type', 'text/csv');
      res.setHeader('Content-Disposition', 'attachment; filename=translations.csv');
      res.send(csv);
    } else {
      // Export JSON
      const translations = {};
      result.rows.forEach(row => {
        if (!translations[row.language]) {
          translations[row.language] = {};
        }
        if (!translations[row.language][row.category]) {
          translations[row.language][row.category] = {};
        }
        translations[row.language][row.category][row.key] = row.value;
      });

      res.json({ translations });
    }

  } catch (error) {
    console.error('Erreur lors de l\'export des traductions:', error);
    res.status(500).json({ message: 'Erreur serveur' });
  }
});

// Fonction utilitaire pour obtenir le nom de la langue
function getLanguageName(code) {
  const languageNames = {
    'fr': 'Français',
    'ar': 'العربية',
    'en': 'English',
    'es': 'Español',
    'de': 'Deutsch'
  };
  return languageNames[code] || code;
}

module.exports = router;
