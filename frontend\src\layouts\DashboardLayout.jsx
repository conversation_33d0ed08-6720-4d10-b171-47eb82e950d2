import React, { useState } from 'react';
import { Outlet, Navigate, useNavigate, useLocation } from 'react-router-dom';
import { Box, AppBar, Toolbar, Typography, IconButton, Menu, MenuItem, Badge, Tooltip, CircularProgress } from '@mui/material';
import {
  Notifications as NotificationsIcon,
  AccountCircle,
  Translate as TranslateIcon,
  Settings as SettingsIcon,
  ArrowBack as ArrowBackIcon,
} from '@mui/icons-material';
import Sidebar from '../components/Sidebar';
import { useAuth } from '../contexts/AuthContext';

// Drawer width
const drawerWidth = 260;

const DashboardLayout = ({ requiredRole }) => {
  const { user, isAuthenticated, logout, loading } = useAuth();
  const [drawerOpen, setDrawerOpen] = useState(false);
  const [anchorEl, setAnchorEl] = useState(null);
  const [notificationsAnchorEl, setNotificationsAnchorEl] = useState(null);
  const [languageAnchorEl, setLanguageAnchorEl] = useState(null);
  const navigate = useNavigate();
  const location = useLocation();

  // Show loading while checking authentication
  if (loading) {
    return (
      <Box
        sx={{
          display: 'flex',
          justifyContent: 'center',
          alignItems: 'center',
          height: '100vh',
          flexDirection: 'column',
          gap: 2
        }}
      >
        <CircularProgress size={60} />
        <Typography variant="h6" color="text.secondary">
          Chargement...
        </Typography>
      </Box>
    );
  }

  // Check if user is authenticated and has required role
  if (!isAuthenticated()) {
    console.log('🔒 DashboardLayout: Utilisateur non authentifié, redirection vers /login');
    return <Navigate to="/login" />;
  }

  // Debug user information
  console.log('👤 DashboardLayout: Utilisateur authentifié:', {
    id: user?.id,
    email: user?.email,
    role: user?.role,
    requiredRole
  });

  // Handle both string roles (legacy) and object roles (new API format)
  const userRole = typeof user?.role === 'object' && user?.role !== null ? user.role.name : user?.role;

  if (requiredRole && userRole !== requiredRole) {
    console.log(`🚫 DashboardLayout: Rôle incorrect. Requis: ${requiredRole}, Actuel: ${userRole}`);
    // Redirect to appropriate dashboard based on role
    const redirectPath = `/${userRole}/dashboard`;
    console.log(`📍 DashboardLayout: Redirection vers ${redirectPath}`);
    return <Navigate to={redirectPath} replace />;
  }

  console.log('✅ DashboardLayout: Accès autorisé pour le rôle:', userRole);

  const toggleDrawer = (open) => {
    setDrawerOpen(open);
  };

  const handleProfileMenuOpen = (event) => {
    setAnchorEl(event.currentTarget);
  };

  const handleNotificationsMenuOpen = (event) => {
    setNotificationsAnchorEl(event.currentTarget);
  };

  const handleLanguageMenuOpen = (event) => {
    setLanguageAnchorEl(event.currentTarget);
  };

  const handleMenuClose = () => {
    setAnchorEl(null);
    setNotificationsAnchorEl(null);
    setLanguageAnchorEl(null);
  };

  const handleLogout = () => {
    handleMenuClose();
    logout();
  };

  return (
    <Box sx={{ display: 'flex' }}>
      {/* Sidebar */}
      <Sidebar open={drawerOpen} toggleDrawer={toggleDrawer} />

      {/* Main content */}
      <Box
        component="main"
        sx={{
          flexGrow: 1,
          p: 3,
          width: { md: `calc(100% - ${drawerWidth}px)` },
          ml: { md: `${drawerWidth}px` },
        }}
      >
        {/* App Bar */}
        <AppBar
          position="fixed"
          sx={{
            width: { md: `calc(100% - ${drawerWidth}px)` },
            ml: { md: `${drawerWidth}px` },
            bgcolor: 'white',
            color: 'text.primary',
            boxShadow: '0 1px 3px rgba(0,0,0,0.12)',
          }}
        >
          <Toolbar>
            <Tooltip title="Retour">
              <IconButton
                edge="start"
                color="inherit"
                aria-label="retour"
                onClick={() => navigate(-1)}
                sx={{ mr: 2 }}
              >
                <ArrowBackIcon />
              </IconButton>
            </Tooltip>

            <Typography
              variant="h6"
              noWrap
              component="div"
              sx={{ flexGrow: 1, display: { xs: 'none', sm: 'block' } }}
            >
              {user?.role === 'admin' && 'Administration'}
              {user?.role === 'eleveur' && 'Espace Éleveur'}
              {user?.role === 'veterinaire' && 'Espace Vétérinaire'}
              {user?.role === 'marchand' && 'Espace Marchand'}
            </Typography>

            {/* Language Selector */}
            <Tooltip title="Changer de langue">
              <IconButton
                size="large"
                color="inherit"
                onClick={handleLanguageMenuOpen}
              >
                <TranslateIcon />
              </IconButton>
            </Tooltip>
            <Menu
              anchorEl={languageAnchorEl}
              open={Boolean(languageAnchorEl)}
              onClose={handleMenuClose}
            >
              <MenuItem onClick={handleMenuClose}>Français</MenuItem>
              <MenuItem onClick={handleMenuClose}>العربية</MenuItem>
            </Menu>

            {/* Notifications */}
            <Tooltip title="Notifications">
              <IconButton
                size="large"
                color="inherit"
                onClick={handleNotificationsMenuOpen}
              >
                <Badge badgeContent={3} color="error">
                  <NotificationsIcon />
                </Badge>
              </IconButton>
            </Tooltip>
            <Menu
              anchorEl={notificationsAnchorEl}
              open={Boolean(notificationsAnchorEl)}
              onClose={handleMenuClose}
            >
              <MenuItem onClick={handleMenuClose}>Notification 1</MenuItem>
              <MenuItem onClick={handleMenuClose}>Notification 2</MenuItem>
              <MenuItem onClick={handleMenuClose}>Notification 3</MenuItem>
            </Menu>

            {/* User Profile */}
            <Tooltip title="Profil">
              <IconButton
                size="large"
                edge="end"
                onClick={handleProfileMenuOpen}
                color="inherit"
              >
                <AccountCircle />
              </IconButton>
            </Tooltip>
            <Menu
              anchorEl={anchorEl}
              open={Boolean(anchorEl)}
              onClose={handleMenuClose}
            >
              <MenuItem onClick={handleMenuClose}>Profil</MenuItem>
              <MenuItem onClick={handleMenuClose}>Paramètres</MenuItem>
              <MenuItem onClick={handleLogout}>Déconnexion</MenuItem>
            </Menu>
          </Toolbar>
        </AppBar>

        {/* Toolbar spacer */}
        <Toolbar />

        {/* Page content */}
        <Box sx={{
          mt: 2,
          minHeight: 'calc(100vh - 64px)',
          overflow: 'auto',
          position: 'relative',
          backgroundColor: 'background.default',
          p: 3
        }}>
          <Outlet />
        </Box>
      </Box>
    </Box>
  );
};

export default DashboardLayout;
