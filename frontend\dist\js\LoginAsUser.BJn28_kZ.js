import{r,j as e,w as C}from"./vendor.CTQIA7G6.js";import{b as k,a as h}from"./index.WVN8qCy5.js";import{q as j,bw as f,a as i,aI as z,B as v,P as T,W as y,bx as _,K as w,t as A,j as B,am as V,d as O,o as R,f as F}from"./mui.D_tNY0b-.js";import"./firebase.BaqyMmVp.js";const G=()=>{const[b,I]=r.useState([]),[a,$]=r.useState(null),[l,u]=r.useState(!0),[d,m]=r.useState(!1),[x,o]=r.useState(""),[p,g]=r.useState(""),{user:n,loginAs:S,logoutAs:U}=k();r.useEffect(()=>{const s=async()=>{try{u(!0);const t=await h.get("/api/users?exclude_self=true");I(t.data||[]),o("")}catch(t){o(t.response?.data?.message||"Erreur lors de la récupération des utilisateurs."),console.error("Erreur fetchUsers:",t)}finally{u(!1)}};n?.role==="Administrateur"&&s()},[n]);const E=(s,t)=>{$(t)},L=async()=>{if(!a){o("Veuillez sélectionner un utilisateur.");return}m(!0),o(""),g("");try{const s=await h.post(`/admin/login-as-user/${a._id}`),{token:t,user:c,originalAdminToken:q,impersonationExpiresAt:P}=s.data;S(c,t,q,P),g(`Vous êtes maintenant connecté en tant que ${c.nom} ${c.prenom}.`)}catch(s){o(s.response?.data?.message||"Erreur lors de la tentative de connexion en tant qu'utilisateur."),console.error("Erreur handleLoginAs:",s)}finally{m(!1)}};return n&&n.isImpersonating?e.jsxs(j,{elevation:3,sx:{p:4,m:2,textAlign:"center"},children:[e.jsx(f,{sx:{fontSize:60,color:"primary.main",mb:2}}),e.jsx(i,{variant:"h5",gutterBottom:!0,children:'Mode "Connexion en tant que" Actif'}),e.jsxs(i,{variant:"body1",sx:{mb:2},children:["Vous naviguez actuellement en tant que ",e.jsx(z,{label:`${n.nom} ${n.prenom} (${n.role})`,color:"secondary"}),"."]}),e.jsx(i,{variant:"caption",display:"block",sx:{mb:3},children:"La session expirera automatiquement ou vous pouvez revenir à votre compte administrateur."}),e.jsx(v,{variant:"contained",color:"error",onClick:()=>U(),startIcon:e.jsx(T,{}),children:"Revenir à mon compte Administrateur"})]}):e.jsxs(j,{elevation:3,sx:{p:4,m:2},children:[e.jsxs(i,{variant:"h5",gutterBottom:!0,sx:{mb:3,display:"flex",alignItems:"center"},children:[e.jsx(f,{sx:{mr:1}})," Se Connecter en tant qu'Utilisateur"]}),x&&e.jsx(y,{severity:"error",sx:{mb:2},children:x}),p&&e.jsx(y,{severity:"success",sx:{mb:2},children:p}),e.jsx(i,{variant:"body2",sx:{mb:2},color:"text.secondary",children:"Cette fonctionnalité permet à un administrateur de se connecter temporairement au compte d'un autre utilisateur pour des raisons de support ou de vérification. Toutes les actions effectuées seront journalisées."}),e.jsx(_,{options:b,getOptionLabel:s=>`${s.nom} ${s.prenom} (${s.email}) - ${s.role}`,value:a,onChange:E,loading:l,isOptionEqualToValue:(s,t)=>s._id===t._id,renderOption:(s,t)=>r.createElement(B,{...s,key:t._id},e.jsx(V,{children:e.jsxs(O,{src:t.photoDeProfil||void 0,children:[t.nom?.[0],t.prenom?.[0]]})}),e.jsx(R,{primary:`${t.nom} ${t.prenom}`,secondary:`${t.email} - ${t.role}`})),renderInput:s=>e.jsx(w,{...s,label:"Sélectionner un utilisateur",variant:"outlined",InputProps:{...s.InputProps,endAdornment:e.jsxs(C.Fragment,{children:[l?e.jsx(A,{color:"inherit",size:20}):null,s.InputProps.endAdornment]})}}),sx:{mb:3}}),e.jsx(v,{variant:"contained",color:"primary",startIcon:e.jsx(F,{}),onClick:L,disabled:!a||d||l,fullWidth:!0,children:d?e.jsx(A,{size:24,color:"inherit"}):`Se connecter en tant que ${a?a.nom:"..."}`})]})};export{G as default};
//# sourceMappingURL=LoginAsUser.BJn28_kZ.js.map
