import{r,j as t}from"./vendor.CTQIA7G6.js";import{a as g}from"./index.WVN8qCy5.js";import{b as h,t as m,q as C,a as f,W as A,G as j,B as v,aT as E,K as k,c as w,I as B,ao as G,ap as O}from"./mui.D_tNY0b-.js";import"./firebase.BaqyMmVp.js";const V=()=>{const[a,d]=r.useState({}),[i,n]=r.useState(!0),[u,o]=r.useState(""),[p,y]=r.useState(""),[l,I]=r.useState({});r.useEffect(()=>{(async()=>{try{n(!0);const s=await g.get("/admin/settings/api-keys");d(s.data||{}),o("")}catch(s){o(s.response?.data?.message||"Erreur lors de la récupération des clés API."),console.error("Erreur fetchApiKeys:",s)}finally{n(!1)}})()},[]);const b=(e,s)=>{d(c=>({...c,[e]:s}))},K=e=>{I(s=>({...s,[e]:!s[e]}))},P=async e=>{e.preventDefault(),n(!0),o(""),y("");try{console.log("Saving API keys:",{apiKeys:a});const s=await g.post("/admin/settings/api-keys",{apiKeys:a});console.log("Server response:",s.data),y("Clés API sauvegardées avec succès !")}catch(s){console.error("Erreur handleSubmit détaillée:",{message:s.message,response:s.response?.data,status:s.response?.status}),o(s.response?.data?.message||s.response?.data?.error||"Erreur lors de la sauvegarde des clés API."),console.error("Erreur handleSubmit:",s)}finally{n(!1)}},x={openai:"OpenAI API Key",google:"Google AI API Key",azure_openai:"Azure OpenAI API Key",claude:"Anthropic Claude API Key",gemini:"Google Gemini API Key"},S=(e,s)=>t.jsx(j,{item:!0,xs:12,children:t.jsx(k,{fullWidth:!0,label:x[e]||`${e} API Key`,variant:"outlined",type:l[e]?"text":"password",value:a[e]||"",onChange:c=>b(e,c.target.value),InputProps:{endAdornment:t.jsx(w,{title:l[e]?"Cacher la clé":"Afficher la clé",children:t.jsx(B,{onClick:()=>K(e),edge:"end",children:l[e]?t.jsx(G,{}):t.jsx(O,{})})})},sx:{mb:2}})},e);return i&&!Object.keys(a).length?t.jsx(h,{display:"flex",justifyContent:"center",alignItems:"center",minHeight:"200px",children:t.jsx(m,{})}):t.jsxs(C,{elevation:3,sx:{p:4,m:2},children:[t.jsx(f,{variant:"h5",gutterBottom:!0,sx:{mb:3},children:"Configuration des Clés API pour l'IA"}),u&&t.jsx(A,{severity:"error",sx:{mb:2},children:u}),p&&t.jsx(A,{severity:"success",sx:{mb:2},children:p}),t.jsx(f,{variant:"body1",sx:{mb:3},children:"Configurez les clés API pour les services d'intelligence artificielle utilisés par l'application. Ces clés sont stockées de manière sécurisée et chiffrées dans la base de données."}),t.jsxs("form",{onSubmit:P,children:[t.jsx(j,{container:!0,spacing:2,children:Object.keys(x).map(e=>S(e))}),t.jsx(h,{sx:{mt:3,display:"flex",justifyContent:"flex-end"},children:t.jsx(v,{type:"submit",variant:"contained",color:"primary",startIcon:t.jsx(E,{}),disabled:i,children:i?t.jsx(m,{size:24,color:"inherit"}):"Sauvegarder les Clés API"})})]})]})};export{V as default};
//# sourceMappingURL=ApiConfig.CQDJF2D9.js.map
