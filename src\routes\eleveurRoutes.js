// Enhanced Éleveur Dashboard Routes
const express = require('express');
const router = express.Router();
const { body, param, query, validationResult } = require('express-validator');
const { auth, checkRole } = require('../middleware/auth');
const eleveurController = require('../controllers/eleveurController');
const validatePagination = require('../middleware/pagination');
const { QueryTypes } = require('sequelize');
const sequelize = require('../config/database');
const bcrypt = require('bcryptjs');

// Logger pour les requêtes SQL
const logQuery = (query, params) => {
  console.log('SQL Query:', {
    text: query,
    params,
    timestamp: new Date().toISOString()
  });
};

// Middleware pour vérifier si l'utilisateur est un éleveur et propriétaire de la ressource
const isEleveur = async (req, res, next) => {
  try {
    // Check role
    if (req.user.role !== 'eleveur' && req.user.role !== 'admin') {
      return res.status(403).json({
        status: 'error',
        code: 'FORBIDDEN',
        message: 'Accès refusé. Rôle éleveur requis.'
      });
    }

    // If admin, allow access to any resource
    if (req.user.role === 'admin') {
      return next();
    }

    // For eleveur role, check resource ownership
    const requestedEleveurId = parseInt(req.params.id);
    if (requestedEleveurId !== req.user.id) {
      console.log('Access denied - ID mismatch:', {
        requestedId: requestedEleveurId,
        userId: req.user.id
      });
      return res.status(403).json({
        status: 'error',
        code: 'FORBIDDEN',
        message: 'Accès refusé. Vous ne pouvez accéder qu\'à vos propres ressources.'
      });
    }

    next();
  } catch (error) {
    console.error('Erreur dans le middleware isEleveur:', error);
    res.status(500).json({
      status: 'error',
      code: 'SERVER_ERROR',
      message: 'Erreur serveur'
    });
  }
};

// Middleware pour vérifier si l'utilisateur est un ouvrier/fonctionnaire
const isOuvrier = async (req, res, next) => {
  try {
    if (req.user.role !== 'ouvrier' && req.user.role !== 'eleveur' && req.user.role !== 'admin') {
      return res.status(403).json({
        status: 'error',
        code: 'FORBIDDEN',
        message: 'Accès refusé. Rôle ouvrier ou éleveur requis.'
      });
    }
    next();
  } catch (error) {
    console.error('Erreur dans le middleware isOuvrier:', error);
    res.status(500).json({
      status: 'error',
      code: 'SERVER_ERROR',
      message: 'Erreur serveur'
    });
  }
};

// @route   GET /api/eleveurs
// @desc    Get all éleveurs (admin only) or current éleveur info
// @access  Private
router.get('/', [auth, validatePagination], eleveurController.getAllEleveurs);

// @route   GET /api/eleveurs/:id
// @desc    Get éleveur by ID
// @access  Private
router.get('/:id', [auth, isEleveur], eleveurController.getEleveurById);

// @route   PUT /api/eleveurs/:id
// @desc    Update éleveur
// @access  Private
router.put('/:id', [auth, isEleveur], eleveurController.updateEleveur);

// @route   GET /api/eleveurs/legacy
// @desc    Legacy route for backwards compatibility
// @access  Private
router.get('/legacy', auth, async (req, res) => {
  try {
    // Si l'utilisateur est admin, retourner tous les éleveurs
    if (req.user.role === 'admin') {
      const eleveurs = await sequelize.query(
        `SELECT
          e.id,
          e.nom,
          e.prenom,
          e.telephone,
          e.email,
          e.adresse,
          e.date_creation,
          COUNT(v.id) as nombre_volailles,
          COALESCE(SUM(v.quantite), 0) as total_animaux
        FROM eleveurs e
        LEFT JOIN volailles v ON e.id = v.eleveur_id
        GROUP BY e.id, e.nom, e.prenom, e.telephone, e.email, e.adresse, e.date_creation
        ORDER BY e.date_creation DESC`,
        {
          type: QueryTypes.SELECT
        }
      );

      return res.json({
        status: 'success',
        data: eleveurs,
        total: eleveurs.length
      });
    }

    // Si l'utilisateur est éleveur, retourner ses propres informations
    if (req.user.role === 'eleveur') {
      const eleveur = await sequelize.query(
        `SELECT
          e.id,
          e.nom,
          e.prenom,
          e.telephone,
          e.email,
          e.adresse,
          e.date_creation,
          COUNT(v.id) as nombre_volailles,
          COALESCE(SUM(v.quantite), 0) as total_animaux
        FROM eleveurs e
        LEFT JOIN volailles v ON e.id = v.eleveur_id
        WHERE e.id = $1
        GROUP BY e.id, e.nom, e.prenom, e.telephone, e.email, e.adresse, e.date_creation`,
        {
          bind: [req.user.profile_id],
          type: QueryTypes.SELECT
        }
      );

      return res.json({
        status: 'success',
        data: eleveur[0] || null
      });
    }

    // Autres rôles n'ont pas accès
    return res.status(403).json({
      status: 'error',
      message: 'Accès refusé'
    });

  } catch (error) {
    console.error('Erreur lors de la récupération des éleveurs:', error);
    res.status(500).json({
      status: 'error',
      message: 'Erreur serveur lors de la récupération des éleveurs'
    });
  }
});

// @route   GET /api/eleveurs/:id/dashboard
// @desc    Get enhanced éleveur dashboard data
// @access  Private/Éleveur
router.get('/:id/dashboard', auth, isEleveur, async (req, res) => {
  try {
    const eleveurId = req.params.id;

    // Verify access rights
    if (req.user.role !== 'admin' && req.user.id !== parseInt(eleveurId)) {
      return res.status(403).json({
        status: 'error',
        code: 'FORBIDDEN',
        message: 'Accès non autorisé'
      });
    }

    // Get dashboard statistics
    const statsQuery = `
      SELECT
        (SELECT COUNT(*) FROM volailles WHERE eleveur_id = :eleveurId) as total_volailles,
        (SELECT COALESCE(SUM(nombre), 0) FROM volailles WHERE eleveur_id = :eleveurId AND statut = 'actif') as volailles_actives,
        (SELECT COUNT(*) FROM consultations c
         JOIN veterinaires v ON c.veterinaire_id = v.id
         WHERE c.eleveur_id = :eleveurId AND c.date >= NOW() - INTERVAL '30 days') as consultations_recentes,
        (SELECT COUNT(DISTINCT sv.id) FROM suivi_veterinaire sv
         JOIN volailles v ON sv.volaille_id = v.id
         WHERE v.eleveur_id = :eleveurId AND sv.date_visite >= NOW()) as suivis_planifies
    `;

    const [stats] = await sequelize.query(statsQuery, {
      replacements: { eleveurId },
      type: QueryTypes.SELECT
    });

    // Get recent activity
    const recentActivityQuery = `
      SELECT
        'consultation' as type,
        c.id,
        c.date,
        c.symptomes as details,
        u.first_name || ' ' || u.last_name as veterinaire_nom,
        c.statut
      FROM consultations c
      JOIN users u ON c.veterinaire_id = u.id
      WHERE c.eleveur_id = :eleveurId

      UNION ALL

      SELECT
        'suivi' as type,
        sv.id,
        sv.date_visite as date,
        sv.observations as details,
        u.first_name || ' ' || u.last_name as veterinaire_nom,
        sv.statut
      FROM suivi_veterinaire sv
      JOIN volailles v ON sv.volaille_id = v.id
      JOIN users u ON sv.veterinaire_id = u.id
      WHERE v.eleveur_id = :eleveurId

      ORDER BY date DESC
      LIMIT 10
    `;

    const recentActivity = await sequelize.query(recentActivityQuery, {
      replacements: { eleveurId },
      type: QueryTypes.SELECT
    });

    // Get volailles summary
    const volaillesSummaryQuery = `
      SELECT
        type,
        COUNT(*) as count,
        COALESCE(SUM(nombre), 0) as total_nombre,
        MIN(age) as age_min,
        MAX(age) as age_max,
        AVG(poids) as poids_moyen
      FROM volailles
      WHERE eleveur_id = :eleveurId AND statut = 'actif'
      GROUP BY type
    `;

    const volaillesSummary = await sequelize.query(volaillesSummaryQuery, {
      replacements: { eleveurId },
      type: QueryTypes.SELECT
    });

    res.json({
      status: 'success',
      data: {
        stats,
        recentActivity,
        volaillesSummary
      }
    });

  } catch (error) {
    console.error('Erreur lors de la récupération des données du tableau de bord:', error);
    res.status(500).json({
      status: 'error',
      code: 'SERVER_ERROR',
      message: 'Erreur lors de la récupération des données du tableau de bord'
    });
  }
});

// @route   GET /api/eleveurs/:id/ouvriers
// @desc    Get farm workers (ouvriers) for an éleveur
// @access  Private/Éleveur
router.get('/:id/ouvriers', auth, isEleveur, async (req, res) => {
  try {
    const eleveurId = req.params.id;

    // Verify access rights
    if (req.user.role !== 'admin' && req.user.id !== parseInt(eleveurId)) {
      return res.status(403).json({
        status: 'error',
        code: 'FORBIDDEN',
        message: 'Accès non autorisé'
      });
    }

    const ouvriersQuery = `
      SELECT
        u.id,
        u.username,
        u.email,
        u.first_name,
        u.last_name,
        u.phone,
        u.address,
        u.status,
        u.created_at,
        u.updated_at,
        COUNT(DISTINCT s.id) as total_saisies,
        MAX(s.date) as derniere_saisie
      FROM users u
      LEFT JOIN saisies_quotidiennes s ON s.ouvrier_id = u.id
      WHERE u.role = 'ouvrier'
      AND u.created_by = :eleveurId
      GROUP BY u.id
      ORDER BY u.created_at DESC
    `;

    const ouvriers = await sequelize.query(ouvriersQuery, {
      replacements: { eleveurId },
      type: QueryTypes.SELECT
    });

    res.json({
      status: 'success',
      data: ouvriers
    });

  } catch (error) {
    console.error('Erreur lors de la récupération des ouvriers:', error);
    res.status(500).json({
      status: 'error',
      code: 'SERVER_ERROR',
      message: 'Erreur lors de la récupération des ouvriers'
    });
  }
});

// @route   POST /api/eleveurs/:id/ouvriers
// @desc    Create new farm worker (ouvrier)
// @access  Private/Éleveur
router.post('/:id/ouvriers', auth, isEleveur, [
  body('username').notEmpty().withMessage('Le nom d\'utilisateur est requis'),
  body('email').isEmail().withMessage('Email invalide'),
  body('password').isLength({ min: 6 }).withMessage('Le mot de passe doit contenir au moins 6 caractères'),
  body('first_name').optional().isString(),
  body('last_name').optional().isString(),
  body('phone').optional().isString(),
  body('address').optional().isString()
], async (req, res) => {
  try {
    const eleveurId = req.params.id;

    // Verify access rights
    if (req.user.role !== 'admin' && req.user.id !== parseInt(eleveurId)) {
      return res.status(403).json({
        status: 'error',
        code: 'FORBIDDEN',
        message: 'Accès non autorisé'
      });
    }

    // Validate request
    const errors = validationResult(req);
    if (!errors.isEmpty()) {
      return res.status(400).json({
        status: 'error',
        code: 'VALIDATION_ERROR',
        errors: errors.array()
      });
    }

    const { username, email, password, first_name, last_name, phone, address } = req.body;

    // Hash password
    const salt = await bcrypt.genSalt(10);
    const hashedPassword = await bcrypt.hash(password, salt);

    // Create ouvrier
    const createUserQuery = `
      INSERT INTO users (
        username,
        email,
        password,
        role,
        first_name,
        last_name,
        phone,
        address,
        status,
        created_by,
        created_at,
        updated_at
      ) VALUES (
        :username,
        :email,
        :password,
        'ouvrier',
        :first_name,
        :last_name,
        :phone,
        :address,
        'active',
        :eleveurId,
        NOW(),
        NOW()
      ) RETURNING id, username, email, first_name, last_name, phone, address, created_at
    `;

    const [ouvrier] = await sequelize.query(createUserQuery, {
      replacements: {
        username,
        email,
        password: hashedPassword,
        first_name: first_name || null,
        last_name: last_name || null,
        phone: phone || null,
        address: address || null,
        eleveurId
      },
      type: QueryTypes.INSERT
    });

    res.status(201).json({
      status: 'success',
      message: 'Ouvrier créé avec succès',
      data: ouvrier
    });

  } catch (error) {
    console.error('Erreur lors de la création de l\'ouvrier:', error);
    if (error.code === '23505') { // Unique constraint violation
      return res.status(400).json({
        status: 'error',
        code: 'DUPLICATE_ENTRY',
        message: 'Un utilisateur avec cet email ou ce nom d\'utilisateur existe déjà'
      });
    }
    res.status(500).json({
      status: 'error',
      code: 'SERVER_ERROR',
      message: 'Erreur lors de la création de l\'ouvrier'
    });
  }
});

// @route   POST /api/eleveurs/saisies-quotidiennes
// @desc    Create daily data entry (for farm workers)
// @access  Private/Ouvrier
router.post('/saisies-quotidiennes', auth, isOuvrier, [
  body('eleveur_id').isInt().withMessage('ID éleveur requis'),
  body('ferme_id').optional().isInt(),
  body('volaille_id').optional().isInt(),
  body('date_saisie').isISO8601().withMessage('Date de saisie invalide'),
  body('nombre_morts').isInt({ min: 0 }).withMessage('Nombre de morts invalide'),
  body('nombre_malades').optional().isInt({ min: 0 }),
  body('temperature_moyenne').optional().isFloat(),
  body('humidite_moyenne').optional().isFloat(),
  body('consommation_eau').optional().isFloat({ min: 0 }),
  body('consommation_aliment').optional().isFloat({ min: 0 }),
  body('incidents').optional().isString(),
  body('besoins_materiels').optional().isString(),
  body('observations').optional().isString()
], async (req, res) => {
  try {
    const errors = validationResult(req);
    if (!errors.isEmpty()) {
      return res.status(400).json({
        status: 'error',
        message: 'Données invalides',
        errors: errors.array()
      });
    }

    const {
      eleveur_id,
      ferme_id,
      volaille_id,
      date_saisie,
      nombre_morts,
      nombre_malades = 0,
      temperature_moyenne,
      humidite_moyenne,
      consommation_eau,
      consommation_aliment,
      incidents,
      besoins_materiels,
      observations
    } = req.body;

    const ouvrierId = req.user.id;

    // Vérifier que l'ouvrier peut saisir pour cet éleveur
    if (req.user.role === 'ouvrier' && req.user.profile_id != eleveur_id) {
      return res.status(403).json({
        status: 'error',
        message: 'Accès refusé pour cet éleveur'
      });
    }

    const insertQuery = `
      INSERT INTO saisies_quotidiennes (
        eleveur_id, ouvrier_id, ferme_id, volaille_id, date_saisie,
        nombre_morts, nombre_malades, temperature_moyenne, humidite_moyenne,
        consommation_eau, consommation_aliment, incidents, besoins_materiels,
        observations, created_at, updated_at
      ) VALUES ($1, $2, $3, $4, $5, $6, $7, $8, $9, $10, $11, $12, $13, $14, NOW(), NOW())
      RETURNING *
    `;

    const result = await sequelize.query(insertQuery, {
      replacements: [
        eleveur_id, ouvrierId, ferme_id, volaille_id, date_saisie,
        nombre_morts, nombre_malades, temperature_moyenne, humidite_moyenne,
        consommation_eau, consommation_aliment, incidents, besoins_materiels,
        observations
      ],
      type: QueryTypes.INSERT
    });

    // Créer des alertes automatiques si nécessaire
    if (nombre_morts > 10) {
      const alerteQuery = `
        INSERT INTO alertes_stock (
          eleveur_id, type_alerte, priorite, titre, message,
          donnees_contexte, date_declenchement, created_at, updated_at
        ) VALUES ($1, 'mortalite_elevee', 'haute', 'Mortalité élevée détectée',
                 'Mortalité de $2 animaux signalée le $3',
                 $4, NOW(), NOW(), NOW())
      `;

      await sequelize.query(alerteQuery, {
        replacements: [
          eleveur_id,
          nombre_morts,
          date_saisie,
          JSON.stringify({
            ferme_id,
            volaille_id,
            ouvrier_id: ouvrierId,
            nombre_morts,
            nombre_malades
          })
        ],
        type: QueryTypes.INSERT
      });
    }

    res.status(201).json({
      status: 'success',
      message: 'Saisie quotidienne enregistrée avec succès',
      data: {
        saisie: result[0][0]
      }
    });

  } catch (error) {
    console.error('Erreur lors de la saisie quotidienne:', error);
    res.status(500).json({
      status: 'error',
      message: 'Erreur serveur lors de la saisie'
    });
  }
});

// @route   POST /api/eleveurs/:id/ventes/quick
// @desc    Quick action - Add new sale
// @access  Private/Éleveur
router.post('/:id/ventes/quick', auth, isEleveur, [
  body('volaille_id').isInt().withMessage('ID volaille requis'),
  body('quantite').isInt({ min: 1 }).withMessage('Quantité invalide'),
  body('prix_unitaire').isFloat({ min: 0 }).withMessage('Prix unitaire invalide'),
  body('acheteur').notEmpty().withMessage('Nom de l\'acheteur requis'),
  body('date_vente').optional().isISO8601()
], async (req, res) => {
  try {
    const errors = validationResult(req);
    if (!errors.isEmpty()) {
      return res.status(400).json({
        status: 'error',
        message: 'Données invalides',
        errors: errors.array()
      });
    }

    const eleveurId = req.params.id;
    const { volaille_id, quantite, prix_unitaire, acheteur, date_vente = new Date() } = req.body;

    if (req.user.role !== 'admin' && req.user.profile_id != eleveurId) {
      return res.status(403).json({
        status: 'error',
        message: 'Accès refusé'
      });
    }

    const total_amount = quantite * prix_unitaire;

    const insertQuery = `
      INSERT INTO ventes (
        eleveur_id, volaille_id, quantite, prix_unitaire, total_amount,
        acheteur, date_vente, status, created_at, updated_at
      ) VALUES ($1, $2, $3, $4, $5, $6, $7, 'confirmee', NOW(), NOW())
      RETURNING *
    `;

    const result = await sequelize.query(insertQuery, {
      replacements: [eleveurId, volaille_id, quantite, prix_unitaire, total_amount, acheteur, date_vente],
      type: QueryTypes.INSERT
    });

    // Mettre à jour le stock de volailles
    const updateStockQuery = `
      UPDATE volailles
      SET quantite = GREATEST(0, quantite - $1), updated_at = NOW()
      WHERE id = $2 AND eleveur_id = $3
    `;

    await sequelize.query(updateStockQuery, {
      replacements: [quantite, volaille_id, eleveurId],
      type: QueryTypes.UPDATE
    });

    res.status(201).json({
      status: 'success',
      message: 'Vente enregistrée avec succès',
      data: {
        vente: result[0][0]
      }
    });

  } catch (error) {
    console.error('Erreur lors de l\'enregistrement de la vente:', error);
    res.status(500).json({
      status: 'error',
      message: 'Erreur serveur lors de l\'enregistrement de la vente'
    });
  }
});

// @route   GET /api/eleveurs/:id/activites
// @desc    Get multi-activity view for éleveur
// @access  Private/Éleveur
router.get('/:id/activites', auth, isEleveur, async (req, res) => {
  try {
    const eleveurId = req.params.id;
    const { type_activite } = req.query;

    if (req.user.role !== 'admin' && req.user.profile_id != eleveurId) {
      return res.status(403).json({
        status: 'error',
        message: 'Accès refusé'
      });
    }

    let whereClause = 'WHERE v.eleveur_id = $1';
    let replacements = [eleveurId];

    if (type_activite) {
      whereClause += ' AND v.espece = $2';
      replacements.push(type_activite);
    }

    const activitesQuery = `
      SELECT
        v.espece as type_activite,
        v.race,
        SUM(v.quantite) as stock_total,
        AVG(v.age_semaines) as age_moyen,
        COUNT(DISTINCT v.id) as nombre_lots,
        COALESCE(SUM(vt.total_amount), 0) as revenus_totaux,
        COALESCE(SUM(vt.total_amount) FILTER (WHERE vt.created_at >= NOW() - INTERVAL '30 days'), 0) as revenus_mois,
        COUNT(DISTINCT sq.id) as nombre_saisies_quotidiennes,
        COALESCE(SUM(sq.nombre_morts), 0) as total_morts,
        COALESCE(SUM(po.quantite_oeufs), 0) as production_oeufs
      FROM volailles v
      LEFT JOIN ventes vt ON v.id = vt.volaille_id
      LEFT JOIN saisies_quotidiennes sq ON v.id = sq.volaille_id
      LEFT JOIN production_oeufs po ON v.id = po.volaille_id
      ${whereClause}
      GROUP BY v.espece, v.race
      ORDER BY stock_total DESC
    `;

    const activites = await sequelize.query(activitesQuery, {
      replacements,
      type: QueryTypes.SELECT
    });

    res.json({
      status: 'success',
      data: {
        activites,
        filtre_applique: type_activite || 'toutes'
      }
    });

  } catch (error) {
    console.error('Erreur lors de la récupération des activités:', error);
    res.status(500).json({
      status: 'error',
      message: 'Erreur serveur'
    });
  }
});

module.exports = router;
