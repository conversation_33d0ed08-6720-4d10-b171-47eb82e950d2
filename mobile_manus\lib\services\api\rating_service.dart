import 'package:dio/dio.dart';
import 'package:mobile_manus/models/user_rating.dart';

class RatingService {
  final Dio _dio = Dio();  final String baseUrl = 'http://192.168.1.102:3003/api';

  RatingService() {
    _dio.options.baseUrl = baseUrl;
    _dio.interceptors.add(InterceptorsWrapper(
      onRequest: (options, handler) {
        // Add auth token
        // options.headers['Authorization'] = 'Bearer YOUR_TOKEN_HERE';
        return handler.next(options);
      },
      onError: (DioException e, handler) {
        print('RatingService Error: ${e.message}');
        return handler.next(e);
      },
    ));
  }

  Future<List<UserRating>> getUserRatings(int userId) async {
    try {
      final response = await _dio.get('/users/$userId/ratings');
      return (response.data as List)
          .map((json) => UserRating.fromJson(json))
          .toList();
    } catch (e) {
      throw Exception('Failed to load user ratings: $e');
    }
  }

  Future<UserRating> submitRating(UserRating rating) async {
    try {
      final response = await _dio.post(
        '/ratings',
        data: rating.toJson(),
      );
      return UserRating.fromJson(response.data);
    } catch (e) {
      throw Exception('Failed to submit rating: $e');
    }
  }
}
