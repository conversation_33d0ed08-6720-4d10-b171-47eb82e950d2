const express = require('express');
const router = express.Router();
const { auth, checkRole } = require('../middleware/auth');
const authController = require('../controllers/authController');
const jwt = require('jsonwebtoken');
const sequelize = require('../config/database');

// Importer les modèles via le système d'index qui les initialise correctement
const db = require('../models');
const User = db.User;
const Role = db.Role;

// Vérification défensive des modèles importés
if (!User) {
  console.error('ERREUR CRITIQUE: Le modèle User n\'est pas disponible');
  console.error('Modèles disponibles:', Object.keys(db).filter(k => k !== 'sequelize' && k !== 'Sequelize'));
}
if (!Role) {
  console.error('ERREUR CRITIQUE: Le modèle Role n\'est pas disponible');
  console.error('Modèles disponibles:', Object.keys(db).filter(k => k !== 'sequelize' && k !== 'Sequelize'));
}

// @route   POST /api/auth/register
// @desc    Register a user
// @access  Public
router.post('/register', async (req, res) => {
  try {
    const user = await User.register(req.body);
    res.status(201).json(user);
  } catch (error) {
    res.status(400).json({ message: error.message });
  }
});

// @route   GET /api/auth/user
// @desc    Get current user info
// @access  Private
router.get('/user', auth, authController.getCurrentUser);

// @route   GET /api/auth/verify
// @desc    Verify JWT token
// @access  Public
router.get('/verify', authController.verifyToken);

// @route   POST /api/auth/refresh-token
// @desc    Refresh JWT token
// @access  Private
router.post('/refresh-token', auth, authController.refreshToken);

// @route   POST /api/auth/login
// @desc    Authenticate user & get token
// @access  Public
router.post('/login', async (req, res) => {
  try {
    console.log('=== Début de la route /login ===');
    console.log('Body reçu:', req.body);

    const { email, password, firebase_token, firebase_uid } = req.body;

    if (!email) {
      console.error('Email manquant');
      return res.status(400).json({ message: 'Email requis' });
    }

    // Vérifier la connexion à la base de données
    try {
      await sequelize.authenticate();
    } catch (dbError) {
      console.error('Erreur de connexion à la base de données:', dbError);
      return res.status(503).json({ message: 'Service temporairement indisponible' });
    }

    let authData;
    let user;

    // Vérifier d'abord si l'utilisateur existe avec son rôle
    try {
      user = await User.findOne({
        where: { email },
        include: [{ model: Role, as: 'role', attributes: ['name', 'id'] }]
      });
    } catch (dbError) {
      console.error('Erreur lors de la recherche de l\'utilisateur:', dbError);
      return res.status(500).json({ message: 'Erreur lors de la vérification des informations utilisateur' });
    }

    if (!user) {
      console.error("Utilisateur non trouvé ou mot de passe incorrect: ", email);
      return res.status(401).json({ message: "Email ou mot de passe incorrect" });
    }

    // Détection du type d'authentification
    if (firebase_token && firebase_uid) {
      try {
        // Authentification Firebase
        console.log('Tentative de connexion Firebase avec:', { email, firebase_uid });
        authData = await User.loginWithFirebase(email, firebase_token, firebase_uid);
      } catch (firebaseError) {
        console.error('Erreur d\'authentification Firebase:', firebaseError);
        return res.status(401).json({ message: 'Échec de l\'authentification Firebase' });
      }
    } else if (password) {
      try {
        // Authentification traditionnelle email/password
        console.log('Tentative de connexion traditionnelle avec:', { email });
        const isValidPassword = await user.comparePassword(password);

        if (!isValidPassword) {
          console.error('Mot de passe invalide pour:', email);
          return res.status(401).json({ message: 'Email ou mot de passe incorrect' });
        }

        const token = await user.generateToken();
        authData = { user, token };
      } catch (authError) {
        console.error('Erreur lors de l\'authentification:', authError);
        return res.status(500).json({ message: 'Erreur lors de l\'authentification' });
      }
    } else {
      console.error('Données de connexion manquantes');
      return res.status(400).json({ message: 'Mot de passe ou token Firebase requis' });
    }

    if (!authData || !authData.token) {
      console.error('Données d\'authentification invalides');
      return res.status(500).json({ message: 'Erreur lors de la génération du token' });
    }    console.log('Connexion réussie pour:', { email });
    console.log('Token généré:', authData.token ? 'Oui' : 'Non');

    // Convert Sequelize instance to plain object to avoid circular references
    const userPlainObject = authData.user.toJSON ? authData.user.toJSON() : authData.user;

    // Format the response to ensure consistent role structure
    const formattedUser = {
      id: userPlainObject.id,
      email: userPlainObject.email,
      username: userPlainObject.username,
      first_name: userPlainObject.first_name,
      last_name: userPlainObject.last_name,
      status: userPlainObject.status,
      role: userPlainObject.role?.name || userPlainObject.role || null
    };

    console.log('Utilisateur formaté avec rôle:', formattedUser.role);
    console.log('=== Fin de la route /login ===');

    return res.json({
      token: authData.token,
      user: formattedUser
    });
  } catch (error) {
    console.error('=== ERREUR dans la route /login ===');
    console.error('Erreur de connexion:', error.message);
    console.error('Stack trace:', error.stack);
    console.error('=== Fin de l\'erreur ===');

    return res.status(500).json({
      message: 'Une erreur inattendue s\'est produite',
      error: process.env.NODE_ENV === 'development' ? error.message : undefined
    });
  }
});

// @route   GET /api/auth/user
// @desc    Get user data
// @access  Private
router.get('/user', auth, async (req, res) => {
  try {
    console.log('Recherche utilisateur avec ID:', req.user.id);

    // Vérification que le modèle User est disponible
    if (!User) {
      console.error('ERREUR: Le modèle User n\'est pas disponible');
      return res.status(500).json({
        message: 'Erreur de configuration du serveur',
        error: 'Modèle User indisponible'
      });
    }

    const user = await User.findOne({
      where: { id: req.user.id },
      include: [{ model: Role, as: 'role', attributes: ['name'] }]
    });

    if (!user) {
      console.log('Utilisateur non trouvé avec ID:', req.user.id);
      return res.status(404).json({ message: 'Utilisateur non trouvé' });
    }

    console.log('Utilisateur trouvé:', user.email);
    console.log('Rôle inclus:', user.role ? user.role.name : 'Aucun rôle');

    // Exclure le mot de passe et formater la réponse
    const userData = user.toJSON();
    delete userData.password;

    res.json({
      ...userData,
      role: userData.role ? userData.role.name : null
    });
  } catch (error) {
    console.error('Erreur lors de la récupération de l\'utilisateur:', error);
    res.status(500).json({
      message: 'Erreur lors de la récupération des données utilisateur',
      error: error.message
    });
  }
});

// @route   PUT /api/auth/profile
// @desc    Update user profile
// @access  Private
router.put('/profile', auth, async (req, res) => {
  try {
    const user = await User.updateProfile(req.user.id, req.body);
    res.json(user);
  } catch (error) {
    res.status(400).json({ message: error.message });
  }
});

// @route   PUT /api/auth/change-password
// @desc    Change user password
// @access  Private
router.put('/change-password', auth, async (req, res) => {
  const { oldPassword, newPassword } = req.body;

  try {
    const result = await User.changePassword(req.user.id, oldPassword, newPassword);
    res.json(result);
  } catch (error) {
    res.status(400).json({ message: error.message });
  }
});

// @route   POST /api/auth/refresh-token
// @desc    Refresh authentication token
// @access  Public
router.post('/refresh-token', async (req, res) => {
  try {
    console.log('=== Début de la route /refresh-token ===');
    console.log('Body reçu:', req.body);

    const { email, firebase_token, firebase_uid } = req.body;

    if (!email || !firebase_token || !firebase_uid) {
      console.error('Données de rafraîchissement Firebase manquantes');
      return res.status(400).json({ message: 'Email, token Firebase et UID Firebase requis' });
    }

    console.log('Tentative de rafraîchissement avec:', { email, firebase_uid });

    const authData = await User.loginWithFirebase(email, firebase_token, firebase_uid);
    console.log('Rafraîchissement réussi pour:', { email });
    console.log('Nouveau token généré:', authData.token ? 'Oui' : 'Non');
    console.log('=== Fin de la route /refresh-token ===');

    return res.json(authData);
  } catch (error) {
    console.error('=== ERREUR dans la route /refresh-token ===');
    console.error('Erreur de rafraîchissement:', error.message);
    console.error('Stack trace:', error.stack);
    console.error('=== Fin de l\'erreur ===');

    return res.status(400).json({ message: error.message });
  }
});

// @route   GET /api/auth/users
// @desc    Get users by role (admin only)
// @access  Private/Admin
router.get('/users/:role', auth, checkRole(['admin']), async (req, res) => {
  try {
    const users = await User.findByRole(req.params.role);
    res.json(users);
  } catch (error) {
    res.status(500).json({ message: error.message });
  }
});

// @route   GET /api/auth/me
// @desc    Get current user
// @access  Private
router.get('/me', auth, async (req, res) => {
  try {
    const user = await User.findByPk(req.user.id);
    if (!user) {
      return res.status(404).json({ message: 'Utilisateur non trouvé' });
    }
    res.json(user);
  } catch (error) {
    console.error('Erreur lors de la récupération du profil:', error);
    res.status(500).json({ message: 'Erreur serveur' });
  }
});

// @route   POST /api/auth/refresh
// @desc    Refresh JWT token
// @access  Private
router.post('/refresh', auth, async (req, res) => {
  try {
    // L'utilisateur est déjà authentifié grâce au middleware auth
    // Générer un nouveau token
    const payload = {
      user: {
        id: req.user.id,
        role: req.user.role
      }
    };

    const token = jwt.sign(
      payload,
      process.env.JWT_SECRET || 'poultray_dz_secret_key_2023',
      { expiresIn: '24h' }
    );

    console.log('Token rafraîchi pour l\'utilisateur:', req.user.id);
    res.json({ token });
  } catch (error) {
    console.error('Erreur lors du rafraîchissement du token:', error);
    res.status(500).json({ message: 'Erreur serveur' });
  }
});

module.exports = router;
