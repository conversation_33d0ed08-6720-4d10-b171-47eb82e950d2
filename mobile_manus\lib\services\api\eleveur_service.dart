import 'package:dio/dio.dart';
import 'package:mobile_manus/models/eleveur.dart';

class EleveurService {
  final Dio _dio = Dio();  final String baseUrl = 'http://192.168.1.102:3003/api';

  EleveurService() {
    _dio.options.baseUrl = baseUrl;
    _dio.options.connectTimeout = const Duration(seconds: 5);
    _dio.options.receiveTimeout = const Duration(seconds: 3);
  }

  void setAuthToken(String token) {
    _dio.options.headers['Authorization'] = 'Bearer $token';
  }

  Future<Map<String, dynamic>> getDashboard(int eleveurId) async {
    try {
      final response = await _dio.get('/eleveurs/$eleveurId/dashboard');

      if (response.statusCode == 200) {
        return response.data;
      } else {
        throw Exception('Failed to load dashboard');
      }
    } on DioException catch (e) {
      if (e.response != null) {
        throw Exception('Dashboard error: ${e.response?.data['message'] ?? 'Unknown error'}');
      } else {
        throw Exception('Network error: ${e.message}');
      }
    }
  }

  Future<List<Map<String, dynamic>>> getOuvriers(int eleveurId) async {
    try {
      final response = await _dio.get('/eleveurs/$eleveurId/ouvriers');

      if (response.statusCode == 200) {
        return List<Map<String, dynamic>>.from(response.data);
      } else {
        throw Exception('Failed to load workers');
      }
    } on DioException catch (e) {
      if (e.response != null) {
        throw Exception('Workers error: ${e.response?.data['message'] ?? 'Unknown error'}');
      } else {
        throw Exception('Network error: ${e.message}');
      }
    }
  }

  Future<Map<String, dynamic>> createOuvrier(int eleveurId, Map<String, dynamic> ouvrierData) async {
    try {
      final response = await _dio.post(
        '/eleveurs/$eleveurId/ouvriers',
        data: ouvrierData,
      );

      if (response.statusCode == 201) {
        return response.data;
      } else {
        throw Exception('Failed to create worker');
      }
    } on DioException catch (e) {
      if (e.response != null) {
        throw Exception('Create worker error: ${e.response?.data['message'] ?? 'Unknown error'}');
      } else {
        throw Exception('Network error: ${e.message}');
      }
    }
  }

  Future<Map<String, dynamic>> createSaisieQuotidienne(Map<String, dynamic> saisieData) async {
    try {
      final response = await _dio.post(
        '/eleveurs/saisies-quotidiennes',
        data: saisieData,
      );

      if (response.statusCode == 201) {
        return response.data;
      } else {
        throw Exception('Failed to create daily entry');
      }
    } on DioException catch (e) {
      if (e.response != null) {
        throw Exception('Daily entry error: ${e.response?.data['message'] ?? 'Unknown error'}');
      } else {
        throw Exception('Network error: ${e.message}');
      }
    }
  }

  Future<Map<String, dynamic>> createVenteRapide(int eleveurId, Map<String, dynamic> venteData) async {
    try {
      final response = await _dio.post(
        '/eleveurs/$eleveurId/ventes/quick',
        data: venteData,
      );

      if (response.statusCode == 201) {
        return response.data;
      } else {
        throw Exception('Failed to create quick sale');
      }
    } on DioException catch (e) {
      if (e.response != null) {
        throw Exception('Quick sale error: ${e.response?.data['message'] ?? 'Unknown error'}');
      } else {
        throw Exception('Network error: ${e.message}');
      }
    }
  }

  Future<List<Eleveur>> getEleveurs({int page = 1, int limit = 10}) async {
    try {
      final response = await _dio.get(
        '/eleveurs',
        queryParameters: {
          'page': page,
          'limit': limit,
        },
      );

      if (response.statusCode == 200) {
        final List<dynamic> data = response.data['eleveurs'] ?? response.data;
        return data.map((json) => Eleveur.fromJson(json)).toList();
      } else {
        throw Exception('Failed to load eleveurs');
      }
    } on DioException catch (e) {
      if (e.response != null) {
        throw Exception('Eleveurs error: ${e.response?.data['message'] ?? 'Unknown error'}');
      } else {
        throw Exception('Network error: ${e.message}');
      }
    }
  }

  Future<Eleveur> getEleveur(int id) async {
    try {
      final response = await _dio.get('/eleveurs/$id');

      if (response.statusCode == 200) {
        return Eleveur.fromJson(response.data);
      } else {
        throw Exception('Failed to load eleveur');
      }
    } on DioException catch (e) {
      if (e.response != null) {
        throw Exception('Eleveur error: ${e.response?.data['message'] ?? 'Unknown error'}');
      } else {
        throw Exception('Network error: ${e.message}');
      }
    }
  }

  Future<Eleveur> createEleveur(Map<String, dynamic> eleveurData) async {
    try {
      final response = await _dio.post(
        '/eleveurs',
        data: eleveurData,
      );

      if (response.statusCode == 201) {
        return Eleveur.fromJson(response.data);
      } else {
        throw Exception('Failed to create eleveur');
      }
    } on DioException catch (e) {
      if (e.response != null) {
        throw Exception('Create eleveur error: ${e.response?.data['message'] ?? 'Unknown error'}');
      } else {
        throw Exception('Network error: ${e.message}');
      }
    }
  }

  Future<Eleveur> updateEleveur(int id, Map<String, dynamic> eleveurData) async {
    try {
      final response = await _dio.put(
        '/eleveurs/$id',
        data: eleveurData,
      );

      if (response.statusCode == 200) {
        return Eleveur.fromJson(response.data);
      } else {
        throw Exception('Failed to update eleveur');
      }
    } on DioException catch (e) {
      if (e.response != null) {
        throw Exception('Update eleveur error: ${e.response?.data['message'] ?? 'Unknown error'}');
      } else {
        throw Exception('Network error: ${e.message}');
      }
    }
  }

  Future<void> deleteEleveur(int id) async {
    try {
      final response = await _dio.delete('/eleveurs/$id');

      if (response.statusCode != 204) {
        throw Exception('Failed to delete eleveur');
      }
    } on DioException catch (e) {
      if (e.response != null) {
        throw Exception('Delete eleveur error: ${e.response?.data['message'] ?? 'Unknown error'}');
      } else {
        throw Exception('Network error: ${e.message}');
      }
    }
  }
}
