{"cmake": {"generator": {"multiConfig": false, "name": "Ninja"}, "paths": {"cmake": "K:/AndroidSDK/cmake/3.22.1/bin/cmake.exe", "cpack": "K:/AndroidSDK/cmake/3.22.1/bin/cpack.exe", "ctest": "K:/AndroidSDK/cmake/3.22.1/bin/ctest.exe", "root": "K:/AndroidSDK/cmake/3.22.1/share/cmake-3.22"}, "version": {"isDirty": true, "major": 3, "minor": 22, "patch": 1, "string": "3.22.1-g37088a8-dirty", "suffix": "g37088a8"}}, "objects": [{"jsonFile": "codemodel-v2-395111a5e37e17881795.json", "kind": "codemodel", "version": {"major": 2, "minor": 3}}, {"jsonFile": "cache-v2-3c3ed3ee81e4a755aa55.json", "kind": "cache", "version": {"major": 2, "minor": 0}}, {"jsonFile": "cmakeFiles-v1-0b2af559d5400867ccc6.json", "kind": "cmakeFiles", "version": {"major": 1, "minor": 0}}], "reply": {"client-agp": {"cache-v2": {"jsonFile": "cache-v2-3c3ed3ee81e4a755aa55.json", "kind": "cache", "version": {"major": 2, "minor": 0}}, "cmakeFiles-v1": {"jsonFile": "cmakeFiles-v1-0b2af559d5400867ccc6.json", "kind": "cmakeFiles", "version": {"major": 1, "minor": 0}}, "codemodel-v2": {"jsonFile": "codemodel-v2-395111a5e37e17881795.json", "kind": "codemodel", "version": {"major": 2, "minor": 3}}}}}