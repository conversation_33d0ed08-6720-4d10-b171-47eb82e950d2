const { User, Role, Eleveur, <PERSON><PERSON><PERSON>, <PERSON><PERSON><PERSON>, ProductionOeufs } = require('../models');
const { Op } = require('sequelize');

const eleveurController = {
  // Récupérer la liste des éleveurs
  getAllEleveurs: async (req, res) => {
    try {
      const page = parseInt(req.query.page) || 1;
      const limit = parseInt(req.query.limit) || 10;
      const offset = (page - 1) * limit;

      const eleveurs = await User.findAndCountAll({
        include: [
          {
            model: Role,
            as: 'role',
            where: { name: 'eleveur' }
          },
          {
            model: Eleveur,
            as: 'eleveur',
            include: [
              {
                model: Volaille,
                as: 'volailles'
              },
              {
                model: <PERSON><PERSON><PERSON>,
                as: 'poussins'
              },
              {
                model: ProductionOeufs,
                as: 'productionsOeufs'
              }
            ]
          }
        ],
        attributes: { exclude: ['password'] },
        limit,
        offset,
        order: [['createdAt', 'DESC']]
      });

      res.json({
        eleveurs: eleveurs.rows,
        total: eleveurs.count,
        page,
        totalPages: Math.ceil(eleveurs.count / limit)
      });
    } catch (error) {
      console.error('Erreur lors de la récupération des éleveurs:', error);
      res.status(500).json({ message: 'Erreur serveur' });
    }
  },

  // Récupérer un éleveur par son ID
  getEleveurById: async (req, res) => {
    try {
      const { id } = req.params;

      const eleveur = await User.findOne({
        where: { id },
        include: [
          {
            model: Role,
            as: 'role',
            where: { name: 'eleveur' }
          },
          {
            model: Eleveur,
            as: 'eleveur',
            include: [
              {
                model: Volaille,
                as: 'volailles'
              },
              {
                model: Poussin,
                as: 'poussins'
              },
              {
                model: ProductionOeufs,
                as: 'productionsOeufs'
              }
            ]
          }
        ],
        attributes: { exclude: ['password'] }
      });

      if (!eleveur) {
        return res.status(404).json({ message: 'Éleveur non trouvé' });
      }

      res.json(eleveur);
    } catch (error) {
      console.error('Erreur lors de la récupération de l\'éleveur:', error);
      res.status(500).json({ message: 'Erreur serveur' });
    }
  },

  // Mettre à jour un éleveur
  updateEleveur: async (req, res) => {
    try {
      const { id } = req.params;
      const updateData = req.body;

      const eleveur = await User.findOne({
        where: { id },
        include: [
          {
            model: Role,
            as: 'role',
            where: { name: 'eleveur' }
          },
          {
            model: Eleveur,
            as: 'eleveur'
          }
        ]
      });

      if (!eleveur) {
        return res.status(404).json({ message: 'Éleveur non trouvé' });
      }

      // Mise à jour des données de l'utilisateur
      await eleveur.update(updateData);

      // Mise à jour des données spécifiques à l'éleveur
      if (updateData.eleveur) {
        await eleveur.eleveur.update(updateData.eleveur);
      }

      res.json({ message: 'Éleveur mis à jour avec succès' });
    } catch (error) {
      console.error('Erreur lors de la mise à jour de l\'éleveur:', error);
      res.status(500).json({ message: 'Erreur serveur' });
    }
  }
};

module.exports = eleveurController;