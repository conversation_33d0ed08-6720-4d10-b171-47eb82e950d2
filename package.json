{"name": "poultray-dz-backend", "version": "1.0.0", "description": "Backend API for Poultray DZ application", "main": "src/index.js", "scripts": {"start": "node src/index.js", "dev": "PORT=3004 nodemon src/index.js", "dev:win": "set PORT=3004 && nodemon src/index.js", "setup-db": "node scripts/setup-database.js", "create-tables": "node src/database/createMissingTables.js", "test": "jest", "test:watch": "jest --watch", "test:coverage": "jest --coverage"}, "dependencies": {"bcryptjs": "^2.4.3", "cors": "^2.8.5", "dotenv": "^16.3.1", "express": "^4.18.2", "express-validator": "^7.2.1", "firebase-admin": "^11.11.1", "jsonwebtoken": "^9.0.2", "multer": "^1.4.5-lts.1", "nodemailer": "^6.9.7", "openai": "^4.20.1", "pg": "^8.16.2", "pg-hstore": "^2.3.4", "recharts": "^3.0.2", "sequelize": "^6.37.7", "slugify": "^1.6.6", "swagger-ui-express": "^5.0.1", "umzug": "^3.8.2", "yamljs": "^0.3.0"}, "devDependencies": {"@babel/core": "^7.27.4", "@babel/plugin-transform-runtime": "^7.27.4", "@babel/preset-env": "^7.27.2", "@babel/preset-react": "^7.27.1", "axios": "^1.10.0", "chalk": "^5.4.1", "colors": "^1.4.0", "jest": "^29.7.0", "nodemon": "^3.0.2", "source-map-support": "^0.5.21", "supertest": "^7.1.1"}}