<!DOCTYPE html>
<html lang="fr">
<head>
  <meta charset="UTF-8">
  <meta name="viewport" content="width=device-width, initial-scale=1.0">
  <title>Test Page</title>
  <style>
    body {
      font-family: Arial, sans-serif;
      margin: 0;
      padding: 0;
      display: flex;
      justify-content: center;
      align-items: center;
      height: 100vh;
      background-color: #f5f5f5;
    }
    .container {
      text-align: center;
      padding: 2rem;
      background-color: white;
      border-radius: 8px;
      box-shadow: 0 4px 6px rgba(0, 0, 0, 0.1);
    }
    h1 {
      color: #4CAF50;
    }
    p {
      margin-bottom: 1.5rem;
    }
    button {
      background-color: #4CAF50;
      color: white;
      border: none;
      padding: 0.5rem 1rem;
      border-radius: 4px;
      cursor: pointer;
    }
    button:hover {
      background-color: #388E3C;
    }
  </style>
</head>
<body>
  <div class="container">
    <h1>Poultray DZ - Test Page</h1>
    <p>Cette page s'affiche correctement. Si vous voyez ce message, le problème n'est pas lié à votre navigateur ou à votre serveur web, mais probablement à l'application React.</p>
    <button onclick="window.location.href='/';">Retour à l'accueil</button>
  </div>
</body>
</html>