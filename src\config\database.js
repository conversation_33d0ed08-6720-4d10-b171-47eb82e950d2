require('dotenv').config();
const { Sequelize } = require('sequelize');

// Configuration de la base de données PostgreSQL avec Sequelize
const dbConfig = {
  database: process.env.DB_NAME || 'poultraydz',
  user: process.env.DB_USER || 'postgres',
  host: process.env.DB_HOST || 'localhost',
  port: process.env.DB_PORT || '5432'
};

console.log('Database configuration:', dbConfig);

const sequelize = new Sequelize(
  dbConfig.database,
  dbConfig.user,
  process.env.DB_PASSWORD || 'root',
  {
    host: dbConfig.host,
    port: dbConfig.port,
    dialect: 'postgres',
    logging: process.env.NODE_ENV === 'development' ? console.log : false,
    pool: {
      max: 20,
      min: 5,
      idle: 30000,
      acquire: 60000,
      evict: 5000
    },
    dialectOptions: {
      connectTimeout: 60000,
      statement_timeout: 60000,
      idle_in_transaction_session_timeout: 60000
    },
    retry: {
      max: 3,
      timeout: 30000
    }
  }
);

// Test de connexion au démarrage
sequelize
  .authenticate()
  .then(() => {
    console.log('✅ Connection to database has been established successfully.');
  })
  .catch(err => {
    console.error('❌ Unable to connect to the database:', err);
  });

module.exports = sequelize;
