{"cmake": {"generator": {"multiConfig": false, "name": "Ninja"}, "paths": {"cmake": "K:/AndroidSDK/cmake/3.22.1/bin/cmake.exe", "cpack": "K:/AndroidSDK/cmake/3.22.1/bin/cpack.exe", "ctest": "K:/AndroidSDK/cmake/3.22.1/bin/ctest.exe", "root": "K:/AndroidSDK/cmake/3.22.1/share/cmake-3.22"}, "version": {"isDirty": true, "major": 3, "minor": 22, "patch": 1, "string": "3.22.1-g37088a8-dirty", "suffix": "g37088a8"}}, "objects": [{"jsonFile": "codemodel-v2-9a5da68de1c489e9b40d.json", "kind": "codemodel", "version": {"major": 2, "minor": 3}}, {"jsonFile": "cache-v2-d47eaa3a3baccdd1deb8.json", "kind": "cache", "version": {"major": 2, "minor": 0}}, {"jsonFile": "cmakeFiles-v1-ed8f9669b86b6f6c27fb.json", "kind": "cmakeFiles", "version": {"major": 1, "minor": 0}}], "reply": {"client-agp": {"cache-v2": {"jsonFile": "cache-v2-d47eaa3a3baccdd1deb8.json", "kind": "cache", "version": {"major": 2, "minor": 0}}, "cmakeFiles-v1": {"jsonFile": "cmakeFiles-v1-ed8f9669b86b6f6c27fb.json", "kind": "cmakeFiles", "version": {"major": 1, "minor": 0}}, "codemodel-v2": {"jsonFile": "codemodel-v2-9a5da68de1c489e9b40d.json", "kind": "codemodel", "version": {"major": 2, "minor": 3}}}}}