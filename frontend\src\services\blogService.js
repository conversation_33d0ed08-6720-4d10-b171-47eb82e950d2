import axiosInstance from '../utils/axiosConfig';

const blogService = {
  // Récupérer tous les articles avec pagination
  getPosts: async (page = 1, limit = 10, status = null) => {
    try {
      const params = new URLSearchParams({
        page: page.toString(),
        limit: limit.toString(),
      });
      if (status) params.append('status', status);      const response = await axiosInstance.get(`/api/blog/posts?${params}`);
      return response.data;
    } catch (error) {
      throw new Error(error.response?.data?.message || 'Erreur lors de la récupération des articles');
    }
  },

  // Récupérer un article par son ID
  getPostById: async (id) => {
    try {      const response = await axiosInstance.get(`/api/blog/posts/${id}`);
      return response.data;
    } catch (error) {
      throw new Error(error.response?.data?.message || 'Erreur lors de la récupération de l\'article');
    }
  },

  // Créer un nouvel article
  createPost: async (postData) => {
    try {      const response = await axiosInstance.post('/api/blog/posts', postData);
      return response.data;
    } catch (error) {
      throw new Error(error.response?.data?.message || 'Erreur lors de la création de l\'article');
    }
  },

  // Mettre à jour un article
  updatePost: async (id, postData) => {
    try {      const response = await axiosInstance.put(`/api/blog/posts/${id}`, postData);
      return response.data;
    } catch (error) {
      throw new Error(error.response?.data?.message || 'Erreur lors de la mise à jour de l\'article');
    }
  },

  // Supprimer un article
  deletePost: async (id) => {
    try {
      const response = await axiosInstance.delete(`/blog/posts/${id}`);
      return response.data;
    } catch (error) {
      throw new Error(error.response?.data?.message || 'Erreur lors de la suppression de l\'article');
    }
  },

  // Récupérer les articles pour l'administration
  getAdminPosts: async (page = 1, limit = 10) => {
    try {
      const params = new URLSearchParams({
        page: page.toString(),
        limit: limit.toString(),
      });

      const response = await axiosInstance.get(`/blog/admin/posts?${params}`);

      // Valider la structure de la réponse
      const data = response.data;
      if (!data || typeof data !== 'object') {
        throw new Error('Format de réponse invalide du serveur');
      }

      // S'assurer que posts est un tableau
      if (!Array.isArray(data.posts)) {
        console.warn('La propriété posts n\'est pas un tableau, initialisation avec un tableau vide');
        data.posts = [];
      }

      // S'assurer que total est un nombre
      if (typeof data.total !== 'number') {
        data.total = data.posts.length;
      }

      return data;
    } catch (error) {
      console.error('Erreur dans blogService.getAdminPosts:', error);

      // En cas d'erreur, retourner une structure par défaut
      if (error.response?.status === 404) {
        return {
          posts: [],
          total: 0,
          page: parseInt(page),
          limit: parseInt(limit),
          totalPages: 0
        };
      }

      throw new Error(error.response?.data?.message || error.message || 'Erreur lors de la récupération des articles');
    }
  },
};

export default blogService;
