import 'package:flutter/material.dart';
import 'package:mobile_manus/models/user.dart';
import 'package:mobile_manus/providers/auth_provider.dart';
import 'package:provider/provider.dart';

class MarchandDashboard extends StatefulWidget {
  final User user;

  const MarchandDashboard({super.key, required this.user});

  @override
  State<MarchandDashboard> createState() => _MarchandDashboardState();
}

class _MarchandDashboardState extends State<MarchandDashboard> {
  int _selectedIndex = 0;

  @override
  Widget build(BuildContext context) {
    return Scaffold(
      appBar: AppBar(
        title: Text('Marchand - ${widget.user.firstName ?? widget.user.username}'),
        backgroundColor: Colors.purple[700],
        foregroundColor: Colors.white,
        actions: [
          IconButton(
            icon: const Icon(Icons.notifications),
            onPressed: () {
              // TODO: Show notifications
            },
          ),
          IconButton(
            icon: const Icon(Icons.logout),
            onPressed: () {
              Provider.of<AuthProvider>(context, listen: false).logout();
            },
          ),
        ],
      ),
      body: IndexedStack(
        index: _selectedIndex,
        children: [
          _buildOverviewTab(),
          _buildProductsTab(),
          _buildOrdersTab(),
          _buildAnalyticsTab(),
        ],
      ),
      bottomNavigationBar: BottomNavigationBar(
        type: BottomNavigationBarType.fixed,
        currentIndex: _selectedIndex,
        onTap: (index) {
          setState(() {
            _selectedIndex = index;
          });
        },
        selectedItemColor: Colors.purple[700],
        unselectedItemColor: Colors.grey,
        items: const [
          BottomNavigationBarItem(
            icon: Icon(Icons.dashboard),
            label: 'Aperçu',
          ),
          BottomNavigationBarItem(
            icon: Icon(Icons.inventory),
            label: 'Produits',
          ),
          BottomNavigationBarItem(
            icon: Icon(Icons.shopping_cart),
            label: 'Commandes',
          ),
          BottomNavigationBarItem(
            icon: Icon(Icons.analytics),
            label: 'Analyses',
          ),
        ],
      ),
      floatingActionButton: FloatingActionButton(
        onPressed: () {
          _showQuickActionDialog();
        },
        backgroundColor: Colors.purple[700],
        child: const Icon(Icons.add, color: Colors.white),
      ),
    );
  }

  Widget _buildOverviewTab() {
    return SingleChildScrollView(
      padding: const EdgeInsets.all(16),
      child: Column(
        crossAxisAlignment: CrossAxisAlignment.start,
        children: [
          // Welcome Card
          Card(
            child: Padding(
              padding: const EdgeInsets.all(16),
              child: Row(
                children: [
                  CircleAvatar(
                    radius: 30,
                    backgroundColor: Colors.purple[100],
                    child: Icon(
                      Icons.store,
                      size: 30,
                      color: Colors.purple[700],
                    ),
                  ),
                  const SizedBox(width: 16),
                  Expanded(
                    child: Column(
                      crossAxisAlignment: CrossAxisAlignment.start,
                      children: [
                        Text(
                          '${widget.user.firstName ?? widget.user.username}',
                          style: Theme.of(context).textTheme.titleLarge,
                        ),
                        Text(
                          'Marchand d\'aliments et équipements',
                          style: Theme.of(context).textTheme.bodyMedium?.copyWith(
                            color: Colors.grey[600],
                          ),
                        ),
                      ],
                    ),
                  ),
                ],
              ),
            ),
          ),
          
          const SizedBox(height: 16),
          
          // Sales Summary
          Text(
            'Résumé des ventes aujourd\'hui',
            style: Theme.of(context).textTheme.titleLarge,
          ),
          const SizedBox(height: 8),
          
          Row(
            children: [
              Expanded(
                child: _buildStatCard(
                  'Revenus',
                  '125,000 DA',
                  Icons.attach_money,
                  Colors.green,
                ),
              ),
              const SizedBox(width: 8),
              Expanded(
                child: _buildStatCard(
                  'Commandes',
                  '23',
                  Icons.shopping_cart,
                  Colors.blue,
                ),
              ),
            ],
          ),
          
          const SizedBox(height: 8),
          
          Row(
            children: [
              Expanded(
                child: _buildStatCard(
                  'Produits vendus',
                  '156',
                  Icons.inventory,
                  Colors.orange,
                ),
              ),
              const SizedBox(width: 8),
              Expanded(
                child: _buildStatCard(
                  'Nouveaux clients',
                  '8',
                  Icons.person_add,
                  Colors.purple,
                ),
              ),
            ],
          ),
          
          const SizedBox(height: 16),
          
          // Recent Orders
          Text(
            'Commandes récentes',
            style: Theme.of(context).textTheme.titleLarge,
          ),
          const SizedBox(height: 8),
          
          Card(
            child: Column(
              children: [
                _buildOrderItem(
                  'CMD-001',
                  'Ferme Benali',
                  '15,000 DA',
                  'En cours',
                  Colors.orange,
                ),
                const Divider(height: 1),
                _buildOrderItem(
                  'CMD-002',
                  'Ferme Khelifi',
                  '8,500 DA',
                  'Livrée',
                  Colors.green,
                ),
                const Divider(height: 1),
                _buildOrderItem(
                  'CMD-003',
                  'Ferme Meziane',
                  '22,000 DA',
                  'Confirmée',
                  Colors.blue,
                ),
              ],
            ),
          ),
          
          const SizedBox(height: 16),
          
          // Stock Alerts
          Text(
            'Alertes de stock',
            style: Theme.of(context).textTheme.titleLarge,
          ),
          const SizedBox(height: 8),
          
          Card(
            child: Column(
              children: [
                _buildAlertItem(
                  'Stock faible',
                  'Aliment pondeuse - 5 sacs restants',
                  Icons.warning,
                  Colors.orange,
                ),
                const Divider(height: 1),
                _buildAlertItem(
                  'Rupture de stock',
                  'Vitamines A+D3 - 0 unités',
                  Icons.error,
                  Colors.red,
                ),
                const Divider(height: 1),
                _buildAlertItem(
                  'Réapprovisionnement',
                  'Commande fournisseur attendue demain',
                  Icons.local_shipping,
                  Colors.blue,
                ),
              ],
            ),
          ),
          
          const SizedBox(height: 16),
          
          // Popular Products
          Text(
            'Produits populaires',
            style: Theme.of(context).textTheme.titleLarge,
          ),
          const SizedBox(height: 8),
          
          Card(
            child: Column(
              children: [
                _buildProductItem(
                  'Aliment pondeuse Premium',
                  '2,500 DA/sac',
                  '45 vendus',
                  Icons.grass,
                ),
                const Divider(height: 1),
                _buildProductItem(
                  'Vitamines A+D3',
                  '1,200 DA/flacon',
                  '32 vendus',
                  Icons.medication,
                ),
                const Divider(height: 1),
                _buildProductItem(
                  'Abreuvoir automatique',
                  '8,500 DA/unité',
                  '12 vendus',
                  Icons.water_drop,
                ),
              ],
            ),
          ),
          
          const SizedBox(height: 16),
          
          // Quick Actions
          Text(
            'Actions rapides',
            style: Theme.of(context).textTheme.titleLarge,
          ),
          const SizedBox(height: 8),
          
          GridView.count(
            shrinkWrap: true,
            physics: const NeverScrollableScrollPhysics(),
            crossAxisCount: 2,
            crossAxisSpacing: 8,
            mainAxisSpacing: 8,
            childAspectRatio: 2.5,
            children: [
              _buildQuickActionCard(
                'Nouveau produit',
                Icons.add_box,
                Colors.green,
                () {
                  // TODO: Navigate to add product
                },
              ),
              _buildQuickActionCard(
                'Gérer stock',
                Icons.inventory,
                Colors.orange,
                () {
                  setState(() {
                    _selectedIndex = 1;
                  });
                },
              ),
              _buildQuickActionCard(
                'Nouvelle commande',
                Icons.add_shopping_cart,
                Colors.blue,
                () {
                  // TODO: Navigate to new order
                },
              ),
              _buildQuickActionCard(
                'Rapport ventes',
                Icons.analytics,
                Colors.purple,
                () {
                  setState(() {
                    _selectedIndex = 3;
                  });
                },
              ),
            ],
          ),
        ],
      ),
    );
  }

  Widget _buildStatCard(String title, String value, IconData icon, Color color) {
    return Card(
      child: Padding(
        padding: const EdgeInsets.all(16),
        child: Column(
          children: [
            Icon(icon, color: color, size: 32),
            const SizedBox(height: 8),
            Text(
              value,
              style: Theme.of(context).textTheme.titleLarge?.copyWith(
                fontWeight: FontWeight.bold,
                color: color,
              ),
            ),
            Text(
              title,
              style: Theme.of(context).textTheme.bodySmall,
              textAlign: TextAlign.center,
            ),
          ],
        ),
      ),
    );
  }

  Widget _buildOrderItem(String orderId, String customer, String amount, String status, Color statusColor) {
    return ListTile(
      leading: CircleAvatar(
        backgroundColor: statusColor.withOpacity(0.1),
        child: Icon(Icons.shopping_cart, color: statusColor, size: 20),
      ),
      title: Text('$orderId - $customer'),
      subtitle: Text(amount),
      trailing: Container(
        padding: const EdgeInsets.symmetric(horizontal: 8, vertical: 4),
        decoration: BoxDecoration(
          color: statusColor.withOpacity(0.1),
          borderRadius: BorderRadius.circular(12),
        ),
        child: Text(
          status,
          style: TextStyle(
            color: statusColor,
            fontSize: 12,
            fontWeight: FontWeight.w500,
          ),
        ),
      ),
      onTap: () {
        // TODO: Handle order tap
      },
    );
  }

  Widget _buildAlertItem(String title, String subtitle, IconData icon, Color color) {
    return ListTile(
      leading: Icon(icon, color: color),
      title: Text(title),
      subtitle: Text(subtitle),
      trailing: const Icon(Icons.chevron_right),
      onTap: () {
        // TODO: Handle alert tap
      },
    );
  }

  Widget _buildProductItem(String name, String price, String sales, IconData icon) {
    return ListTile(
      leading: Icon(icon, color: Colors.purple[700]),
      title: Text(name),
      subtitle: Text(price),
      trailing: Text(
        sales,
        style: TextStyle(
          color: Colors.grey[600],
          fontSize: 12,
        ),
      ),
      onTap: () {
        // TODO: Handle product tap
      },
    );
  }

  Widget _buildQuickActionCard(String title, IconData icon, Color color, VoidCallback onTap) {
    return Card(
      child: InkWell(
        onTap: onTap,
        borderRadius: BorderRadius.circular(8),
        child: Padding(
          padding: const EdgeInsets.all(12),
          child: Row(
            children: [
              Icon(icon, color: color, size: 24),
              const SizedBox(width: 8),
              Expanded(
                child: Text(
                  title,
                  style: Theme.of(context).textTheme.bodyMedium?.copyWith(
                    fontWeight: FontWeight.w500,
                  ),
                ),
              ),
            ],
          ),
        ),
      ),
    );
  }

  void _showQuickActionDialog() {
    showDialog(
      context: context,
      builder: (BuildContext context) {
        return AlertDialog(
          title: const Text('Action rapide'),
          content: Column(
            mainAxisSize: MainAxisSize.min,
            children: [
              ListTile(
                leading: const Icon(Icons.add_box, color: Colors.green),
                title: const Text('Nouveau produit'),
                onTap: () {
                  Navigator.pop(context);
                  // TODO: Navigate to add product
                },
              ),
              ListTile(
                leading: const Icon(Icons.add_shopping_cart, color: Colors.blue),
                title: const Text('Nouvelle commande'),
                onTap: () {
                  Navigator.pop(context);
                  // TODO: Navigate to new order
                },
              ),
              ListTile(
                leading: const Icon(Icons.inventory, color: Colors.orange),
                title: const Text('Mettre à jour stock'),
                onTap: () {
                  Navigator.pop(context);
                  // TODO: Navigate to update stock
                },
              ),
            ],
          ),
        );
      },
    );
  }

  Widget _buildProductsTab() {
    return const Center(
      child: Text('Gestion des produits - À implémenter'),
    );
  }

  Widget _buildOrdersTab() {
    return const Center(
      child: Text('Gestion des commandes - À implémenter'),
    );
  }

  Widget _buildAnalyticsTab() {
    return const Center(
      child: Text('Analyses et rapports - À implémenter'),
    );
  }
}

