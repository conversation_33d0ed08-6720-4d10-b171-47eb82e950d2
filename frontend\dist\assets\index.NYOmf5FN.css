@keyframes fadeInUp{0%{opacity:0;transform:translateY(20px)}to{opacity:1;transform:translateY(0)}}@keyframes float{0%{transform:translateY(0)}50%{transform:translateY(-10px)}to{transform:translateY(0)}}@keyframes pulse{0%{transform:scale(1);box-shadow:0 4px 10px #00000026}50%{transform:scale(1.05);box-shadow:0 6px 15px #0003}to{transform:scale(1);box-shadow:0 4px 10px #00000026}}@keyframes rotate{0%{transform:rotate(0)}to{transform:rotate(360deg)}}@keyframes fadeIn{0%{opacity:0}to{opacity:1}}@keyframes slideInLeft{0%{opacity:0;transform:translate(-50px)}to{opacity:1;transform:translate(0)}}@keyframes slideInRight{0%{opacity:0;transform:translate(50px)}to{opacity:1;transform:translate(0)}}@keyframes slideInUp{0%{opacity:0;transform:translateY(50px)}to{opacity:1;transform:translateY(0)}}.animate-fadeInUp{animation:fadeInUp .6s ease-out forwards}.animate-float{animation:float 3s ease-in-out infinite}.animate-pulse{animation:pulse 2s infinite}.animate-rotate{animation:rotate 10s linear infinite}.scroll-anim{opacity:0;transition:all .8s ease-out}.scroll-anim.visible{opacity:1}.fade-in.visible{animation:fadeIn 1s ease forwards}.slide-in-left.visible{animation:slideInLeft .8s ease forwards}.slide-in-right.visible{animation:slideInRight .8s ease forwards}.slide-in-up.visible{animation:slideInUp .8s ease forwards}.delay-100{animation-delay:.1s}.delay-200{animation-delay:.2s}.delay-300{animation-delay:.3s}.delay-400{animation-delay:.4s}.delay-500{animation-delay:.5s}.feature-card{transition:transform .3s ease,box-shadow .3s ease}.feature-card:hover{transform:translateY(-10px);box-shadow:0 12px 20px #0000001a}@media (max-width: 600px){.animate-float{animation-duration:2s}.scroll-anim{transition:all .6s ease-out}}.cta-button{transition:all .3s ease}.cta-button:hover{transform:translateY(-3px);box-shadow:0 6px 15px #4caf504d}
