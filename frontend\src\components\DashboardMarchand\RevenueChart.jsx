
import { useTheme } from '@mui/material/styles';
import { Box, Typography } from '@mui/material';
import { ResponsiveContainer, LineChart, Line, XAxis, YAxis, CartesianGrid, Tooltip, Legend } from 'recharts';

/**
 * Composant pour afficher l'évolution des revenus sous forme de graphique linéaire
 * @param {Object} props - Propriétés du composant
 * @param {Array} props.data - Données de revenus par période
 * @param {string} props.timeRange - Période sélectionnée ('week', 'month', 'year')
 */
const RevenueChart = ({ data = [], timeRange }) => {
  const theme = useTheme();

  // Formater les données pour le graphique
  const formattedData = data.map(item => ({
    date: formatDate(new Date(item.date), timeRange),
    revenue: parseFloat(item.revenue) || 0
  }));

  // Si aucune donnée, afficher un message
  if (formattedData.length === 0) {
    return (
      <Box sx={{ display: 'flex', justifyContent: 'center', alignItems: 'center', height: 300 }}>
        <Typography variant="body1" color="textSecondary">
          Aucune donnée de revenu disponible pour cette période
        </Typography>
      </Box>
    );
  }

  return (
    <Box sx={{ width: '100%', height: 300 }}>
      <ResponsiveContainer>
        <LineChart
          data={formattedData}
          margin={{ top: 5, right: 30, left: 20, bottom: 5 }}
        >
          <CartesianGrid strokeDasharray="3 3" stroke={theme.palette.divider} />
          <XAxis
            dataKey="date"
            stroke={theme.palette.text.secondary}
            style={{ fontSize: '0.75rem' }}
          />
          <YAxis
            stroke={theme.palette.text.secondary}
            style={{ fontSize: '0.75rem' }}
            tickFormatter={(value) => `${value} DA`}
          />
          <Tooltip
            formatter={(value) => [`${value.toFixed(2)} DA`, 'Revenu']}
            labelFormatter={(label) => `Date: ${label}`}
            contentStyle={{
              backgroundColor: theme.palette.background.paper,
              border: `1px solid ${theme.palette.divider}`,
              borderRadius: 4,
              boxShadow: theme.shadows[3]
            }}
          />
          <Legend />
          <Line
            type="monotone"
            dataKey="revenue"
            name="Revenu"
            stroke={theme.palette.primary.main}
            strokeWidth={2}
            dot={{ r: 4, strokeWidth: 1 }}
            activeDot={{ r: 6, strokeWidth: 1 }}
          />
        </LineChart>
      </ResponsiveContainer>
    </Box>
  );
};

/**
 * Formate une date selon la période sélectionnée
 * @param {Date} date - Date à formater
 * @param {string} timeRange - Période ('week', 'month', 'year')
 * @returns {string} - Date formatée
 */
const formatDate = (date, timeRange) => {
  if (!date || !(date instanceof Date) || isNaN(date)) {
    return 'Date invalide';
  }

  const day = date.getDate().toString().padStart(2, '0');
  const month = (date.getMonth() + 1).toString().padStart(2, '0');
  const year = date.getFullYear();

  if (timeRange === 'week') {
    return `${day}/${month}`;
  } else if (timeRange === 'month') {
    return `${day}/${month}`;
  } else {
    return `${month}/${year}`;
  }
};

export default RevenueChart;
