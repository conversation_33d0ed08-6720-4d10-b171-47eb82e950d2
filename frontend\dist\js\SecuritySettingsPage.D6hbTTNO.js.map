{"version": 3, "file": "SecuritySettingsPage.D6hbTTNO.js", "sources": ["../../src/pages/admin/SecuritySettingsPage.jsx"], "sourcesContent": ["import { useState, useEffect } from 'react';\r\nimport {\r\n  Box,\r\n  Typography,\r\n  Paper,\r\n  TextField,\r\n  Button,\r\n  CircularProgress,\r\n  Alert,\r\n  Grid,\r\n  FormControlLabel,\r\n  Switch,\r\n  Divider,\r\n  Slider,\r\n  InputAdornment,\r\n  MenuItem,\r\n  Snackbar,\r\n  Select,\r\n  FormControl,\r\n  InputLabel\r\n} from '@mui/material';\r\nimport {\r\n  Save as SaveIcon,\r\n  Security as SecurityIcon,\r\n  LockClock as LockClockIcon,\r\n  AdminPanelSettings as AdminPanelSettingsIcon,\r\n  VpnKey as VpnKeyIcon,\r\n  VerifiedUser as VerifiedUserIcon\r\n} from '@mui/icons-material';\r\nimport settingsService from '../../services/settingsService';\r\nimport { useLanguage } from '../../contexts/LanguageContext';\r\n\r\nconst SecuritySettingsPage = () => {\r\n  const { t } = useLanguage();\r\n\r\n  const [settings, setSettings] = useState({\r\n    enable2FA: false,\r\n    sessionTimeout: 30,\r\n    maxLoginAttempts: 5,\r\n    lockoutDuration: 15,\r\n    passwordComplexityRegex: '',\r\n    passwordHistoryCount: 3,\r\n    passwordExpiryDays: 90,\r\n    contentSecurityPolicy: '',\r\n    corsAllowedOrigins: '',\r\n    logLevel: 'info',\r\n    apiRateLimitingEnabled: true,\r\n    apiRateLimitRequests: 100,\r\n    apiRateLimitWindowMs: 900000 // 15 minutes in milliseconds\r\n  });\r\n\r\n  const [loading, setLoading] = useState(true);\r\n  const [saving, setSaving] = useState(false);\r\n  const [error, setError] = useState('');\r\n  const [success, setSuccess] = useState('');\r\n  const [toast, setToast] = useState({ open: false, message: '', severity: 'info' });\r\n\r\n  useEffect(() => {\r\n    const fetchSettings = async () => {\r\n      try {\r\n        setLoading(true);\r\n        console.log('Fetching security settings via SecuritySettingsPage component...');\r\n        const data = await settingsService.getSecuritySettings();\r\n        console.log('Fetched security settings:', data);\r\n        setSettings(data);\r\n        setError('');\r\n      } catch (err) {\r\n        console.error('Error fetching security settings:', err);\r\n        setError(t('settings.security.fetchError') || 'Failed to load security settings');\r\n        showToast(t('settings.security.fetchError') || 'Failed to load security settings', 'error');\r\n      } finally {\r\n        setLoading(false);\r\n      }\r\n    };\r\n\r\n    fetchSettings();\r\n  }, [t]);\r\n\r\n  const handleChange = (e) => {\r\n    const { name, value, checked } = e.target;\r\n    const newValue = e.target.type === 'checkbox' ? checked : value;\r\n\r\n    setSettings(prev => ({\r\n      ...prev,\r\n      [name]: newValue\r\n    }));\r\n  };\r\n\r\n  const handleSliderChange = (name) => (_, newValue) => {\r\n    setSettings(prev => ({\r\n      ...prev,\r\n      [name]: newValue\r\n    }));\r\n  };\r\n\r\n  const handleSubmit = async (e) => {\r\n    e.preventDefault();\r\n\r\n    try {\r\n      setSaving(true);\r\n      setError('');\r\n      console.log('Submitting security settings:', settings);\r\n      await settingsService.updateSecuritySettings(settings);\r\n      console.log('Security settings updated successfully');\r\n      setSuccess(t('settings.security.saveSuccess') || 'Security settings updated successfully');\r\n      showToast(t('settings.security.saveSuccess') || 'Security settings updated successfully', 'success');\r\n    } catch (err) {\r\n      console.error('Error updating security settings:', err);\r\n      setError(t('settings.security.saveError') || 'Failed to update security settings');\r\n      showToast(t('settings.security.saveError') || 'Failed to update security settings', 'error');\r\n    } finally {\r\n      setSaving(false);\r\n    }\r\n  };\r\n\r\n  const showToast = (message, severity) => {\r\n    setToast({ open: true, message, severity });\r\n  };\r\n\r\n  const handleCloseToast = () => {\r\n    setToast({ ...toast, open: false });\r\n  };\r\n\r\n  if (loading) {\r\n    return (\r\n      <Box sx={{ display: 'flex', justifyContent: 'center', mt: 4 }}>\r\n        <CircularProgress />\r\n      </Box>\r\n    );\r\n  }\r\n\r\n  return (\r\n    <Box sx={{ p: 3 }}>\r\n      <Typography variant=\"h4\" component=\"h1\" gutterBottom>\r\n        <SecurityIcon sx={{ mr: 1, verticalAlign: 'middle' }} />\r\n        {t('settings.security.title') || 'Security Settings'}\r\n      </Typography>\r\n      <Paper sx={{ p: 3, mt: 2 }}>\r\n        {error && <Alert severity=\"error\" sx={{ mb: 2 }}>{error}</Alert>}\r\n        {success && <Alert severity=\"success\" sx={{ mb: 2 }}>{success}</Alert>}\r\n\r\n        <form onSubmit={handleSubmit}>\r\n          <Grid container spacing={3}>\r\n            {/* Authentication Section */}\r\n            <Grid item xs={12}>\r\n              <Typography variant=\"h5\" component=\"h2\" gutterBottom>\r\n                <VerifiedUserIcon sx={{ mr: 1, verticalAlign: 'middle' }} />\r\n                {t('settings.security.authentication.title') || 'Authentication'}\r\n              </Typography>\r\n              <Divider sx={{ mb: 2 }} />\r\n            </Grid>\r\n\r\n            <Grid item xs={12} sm={6}>\r\n              <FormControlLabel\r\n                control={\r\n                  <Switch\r\n                    checked={settings.enable2FA}\r\n                    onChange={handleChange}\r\n                    name=\"enable2FA\"\r\n                    color=\"primary\"\r\n                  />\r\n                }\r\n                label={t('settings.security.authentication.enable2FA') || 'Enable Two-Factor Authentication'}\r\n              />\r\n            </Grid>\r\n\r\n            <Grid item xs={12} sm={6}>\r\n              <Typography gutterBottom>\r\n                {t('settings.security.authentication.sessionTimeout') || 'Session Timeout (minutes)'}\r\n              </Typography>\r\n              <Slider\r\n                value={settings.sessionTimeout}\r\n                onChange={handleSliderChange('sessionTimeout')}\r\n                aria-labelledby=\"session-timeout-slider\"\r\n                valueLabelDisplay=\"auto\"\r\n                step={5}\r\n                marks\r\n                min={5}\r\n                max={120}\r\n              />\r\n            </Grid>\r\n\r\n            <Grid item xs={12} sm={6}>\r\n              <TextField\r\n                fullWidth\r\n                label={t('settings.security.authentication.maxLoginAttempts') || 'Max Login Attempts'}\r\n                name=\"maxLoginAttempts\"\r\n                type=\"number\"\r\n                value={settings.maxLoginAttempts}\r\n                onChange={handleChange}\r\n                InputProps={{\r\n                  startAdornment: (\r\n                    <InputAdornment position=\"start\">\r\n                      <LockClockIcon />\r\n                    </InputAdornment>\r\n                  ),\r\n                }}\r\n              />\r\n            </Grid>\r\n\r\n            <Grid item xs={12} sm={6}>\r\n              <TextField\r\n                fullWidth\r\n                label={t('settings.security.authentication.lockoutDuration') || 'Lockout Duration (minutes)'}\r\n                name=\"lockoutDuration\"\r\n                type=\"number\"\r\n                value={settings.lockoutDuration}\r\n                onChange={handleChange}\r\n              />\r\n            </Grid>\r\n\r\n            {/* Password Policy Section */}\r\n            <Grid item xs={12}>\r\n              <Typography variant=\"h5\" component=\"h2\" gutterBottom sx={{ mt: 2 }}>\r\n                <VpnKeyIcon sx={{ mr: 1, verticalAlign: 'middle' }} />\r\n                {t('settings.security.passwordPolicy.title') || 'Password Policy'}\r\n              </Typography>\r\n              <Divider sx={{ mb: 2 }} />\r\n            </Grid>\r\n\r\n            <Grid item xs={12}>\r\n              <TextField\r\n                fullWidth\r\n                label={t('settings.security.passwordPolicy.complexityRegex') || 'Password Complexity Regex'}\r\n                name=\"passwordComplexityRegex\"\r\n                value={settings.passwordComplexityRegex}\r\n                onChange={handleChange}\r\n                helperText={t('settings.security.passwordPolicy.complexityRegexHelp') || 'Regular expression for password validation (e.g., ^(?=.*[a-z])(?=.*[A-Z])(?=.*\\\\d)[a-zA-Z\\\\d]{8,}$)'}\r\n              />\r\n            </Grid>\r\n\r\n            <Grid item xs={12} sm={6}>\r\n              <TextField\r\n                fullWidth\r\n                label={t('settings.security.passwordPolicy.historyCount') || 'Password History Count'}\r\n                name=\"passwordHistoryCount\"\r\n                type=\"number\"\r\n                value={settings.passwordHistoryCount}\r\n                onChange={handleChange}\r\n                helperText={t('settings.security.passwordPolicy.historyCountHelp') || 'Number of previous passwords to remember to prevent reuse'}\r\n              />\r\n            </Grid>\r\n\r\n            <Grid item xs={12} sm={6}>\r\n              <TextField\r\n                fullWidth\r\n                label={t('settings.security.passwordPolicy.expiryDays') || 'Password Expiry (days)'}\r\n                name=\"passwordExpiryDays\"\r\n                type=\"number\"\r\n                value={settings.passwordExpiryDays}\r\n                onChange={handleChange}\r\n                helperText={t('settings.security.passwordPolicy.expiryDaysHelp') || 'Number of days after which passwords expire (0 to disable)'}\r\n              />\r\n            </Grid>\r\n\r\n            {/* API Security Section */}\r\n            <Grid item xs={12}>\r\n              <Typography variant=\"h5\" component=\"h2\" gutterBottom sx={{ mt: 2 }}>\r\n                <AdminPanelSettingsIcon sx={{ mr: 1, verticalAlign: 'middle' }} />\r\n                {t('settings.security.apiSecurity.title') || 'API Security'}\r\n              </Typography>\r\n              <Divider sx={{ mb: 2 }} />\r\n            </Grid>\r\n\r\n            <Grid item xs={12}>\r\n              <TextField\r\n                fullWidth\r\n                label={t('settings.security.apiSecurity.corsAllowedOrigins') || 'CORS Allowed Origins'}\r\n                name=\"corsAllowedOrigins\"\r\n                value={settings.corsAllowedOrigins}\r\n                onChange={handleChange}\r\n                helperText={t('settings.security.apiSecurity.corsAllowedOriginsHelp') || 'Comma-separated list of allowed origins for CORS (e.g., http://localhost:3000,https://yourdomain.com)'}\r\n              />\r\n            </Grid>\r\n\r\n            <Grid item xs={12} sm={6}>\r\n              <FormControl fullWidth>\r\n                <InputLabel>{t('settings.security.apiSecurity.logLevel') || 'Log Level'}</InputLabel>\r\n                <Select\r\n                  name=\"logLevel\"\r\n                  value={settings.logLevel}\r\n                  label={t('settings.security.apiSecurity.logLevel') || 'Log Level'}\r\n                  onChange={handleChange}\r\n                >\r\n                  <MenuItem value=\"debug\">Debug</MenuItem>\r\n                  <MenuItem value=\"info\">Info</MenuItem>\r\n                  <MenuItem value=\"warn\">Warning</MenuItem>\r\n                  <MenuItem value=\"error\">Error</MenuItem>\r\n                </Select>\r\n              </FormControl>\r\n            </Grid>\r\n\r\n            <Grid item xs={12} sm={6}>\r\n              <FormControlLabel\r\n                control={\r\n                  <Switch\r\n                    checked={settings.apiRateLimitingEnabled}\r\n                    onChange={handleChange}\r\n                    name=\"apiRateLimitingEnabled\"\r\n                    color=\"primary\"\r\n                  />\r\n                }\r\n                label={t('settings.security.apiSecurity.rateLimitingEnabled') || 'Enable API Rate Limiting'}\r\n              />\r\n            </Grid>\r\n\r\n            {settings.apiRateLimitingEnabled && (\r\n              <>\r\n                <Grid item xs={12} sm={6}>\r\n                  <TextField\r\n                    fullWidth\r\n                    label={t('settings.security.apiSecurity.rateLimitRequests') || 'Rate Limit Requests'}\r\n                    name=\"apiRateLimitRequests\"\r\n                    type=\"number\"\r\n                    value={settings.apiRateLimitRequests}\r\n                    onChange={handleChange}\r\n                    helperText={t('settings.security.apiSecurity.rateLimitRequestsHelp') || 'Maximum number of requests in the time window'}\r\n                  />\r\n                </Grid>\r\n\r\n                <Grid item xs={12} sm={6}>\r\n                  <TextField\r\n                    fullWidth\r\n                    label={t('settings.security.apiSecurity.rateLimitWindowMs') || 'Rate Limit Window (ms)'}\r\n                    name=\"apiRateLimitWindowMs\"\r\n                    type=\"number\"\r\n                    value={settings.apiRateLimitWindowMs}\r\n                    onChange={handleChange}\r\n                    helperText={t('settings.security.apiSecurity.rateLimitWindowMsHelp') || 'Time window for rate limiting in milliseconds (e.g., 900000 for 15 minutes)'}\r\n                  />\r\n                </Grid>\r\n              </>\r\n            )}\r\n\r\n            <Grid item xs={12}>\r\n              <Box sx={{ display: 'flex', justifyContent: 'flex-end', mt: 2 }}>\r\n                <Button\r\n                  type=\"submit\"\r\n                  variant=\"contained\"\r\n                  color=\"primary\"\r\n                  startIcon={<SaveIcon />}\r\n                  disabled={saving}\r\n                >\r\n                  {saving ? (\r\n                    <>\r\n                      <CircularProgress size={24} sx={{ mr: 1 }} />\r\n                      {t('common.saving') || 'Saving...'}\r\n                    </>\r\n                  ) : (\r\n                    t('common.save') || 'Save Changes'\r\n                  )}\r\n                </Button>\r\n              </Box>\r\n            </Grid>\r\n          </Grid>\r\n        </form>\r\n      </Paper>\r\n\r\n      <Snackbar\r\n        open={toast.open}\r\n        autoHideDuration={6000}\r\n        onClose={handleCloseToast}\r\n        anchorOrigin={{ vertical: 'bottom', horizontal: 'right' }}\r\n      >\r\n        <Alert onClose={handleCloseToast} severity={toast.severity} sx={{ width: '100%' }}>\r\n          {toast.message}\r\n        </Alert>\r\n      </Snackbar>\r\n    </Box>\r\n  );\r\n};\r\n\r\nexport default SecuritySettingsPage;\r\n"], "names": ["SecuritySettingsPage", "useLanguage", "settings", "setSettings", "useState", "loading", "setLoading", "saving", "setSaving", "error", "setError", "success", "setSuccess", "toast", "setToast", "useEffect", "data", "settingsService", "err", "showToast", "handleChange", "e", "name", "value", "checked", "newValue", "prev", "handleSliderChange", "_", "handleSubmit", "message", "severity", "handleCloseToast", "jsx", "Box", "CircularProgress", "jsxs", "Typography", "SecurityIcon", "Paper", "<PERSON><PERSON>", "Grid", "VerifiedUserIcon", "Divider", "FormControlLabel", "Switch", "Slide<PERSON>", "TextField", "InputAdornment", "LockClockIcon", "VpnKeyIcon", "AdminPanelSettingsIcon", "FormControl", "InputLabel", "Select", "MenuItem", "Fragment", "<PERSON><PERSON>", "SaveIcon", "Snackbar"], "mappings": "sXAgCA,MAAMA,GAAuB,IAAM,CACjC,KAAM,CAAE,CAAA,EAAMC,EAAA,EAER,CAACC,EAAUC,CAAW,EAAIC,WAAS,CACvC,UAAW,GACX,eAAgB,GAChB,iBAAkB,EAClB,gBAAiB,GACjB,wBAAyB,GACzB,qBAAsB,EACtB,mBAAoB,GACpB,sBAAuB,GACvB,mBAAoB,GACpB,SAAU,OACV,uBAAwB,GACxB,qBAAsB,IACtB,qBAAsB,GAAA,CACvB,EAEK,CAACC,EAASC,CAAU,EAAIF,EAAAA,SAAS,EAAI,EACrC,CAACG,EAAQC,CAAS,EAAIJ,EAAAA,SAAS,EAAK,EACpC,CAACK,EAAOC,CAAQ,EAAIN,EAAAA,SAAS,EAAE,EAC/B,CAACO,EAASC,CAAU,EAAIR,EAAAA,SAAS,EAAE,EACnC,CAACS,EAAOC,CAAQ,EAAIV,EAAAA,SAAS,CAAE,KAAM,GAAO,QAAS,GAAI,SAAU,MAAA,CAAQ,EAEjFW,EAAAA,UAAU,IAAM,EACQ,SAAY,CAChC,GAAI,CACFT,EAAW,EAAI,EACf,QAAQ,IAAI,kEAAkE,EAC9E,MAAMU,EAAO,MAAMC,EAAgB,oBAAA,EACnC,QAAQ,IAAI,6BAA8BD,CAAI,EAC9Cb,EAAYa,CAAI,EAChBN,EAAS,EAAE,CAAA,OACJQ,EAAK,CACZ,QAAQ,MAAM,oCAAqCA,CAAG,EACtDR,EAAS,EAAE,8BAA8B,GAAK,kCAAkC,EAChFS,EAAU,EAAE,8BAA8B,GAAK,mCAAoC,OAAO,CAAA,QAC5F,CACEb,EAAW,EAAK,CAAA,CAClB,GAGF,CAAc,EACb,CAAC,CAAC,CAAC,EAEN,MAAMc,EAAgBC,GAAM,CAC1B,KAAM,CAAE,KAAAC,EAAM,MAAAC,EAAO,QAAAC,CAAA,EAAYH,EAAE,OAC7BI,EAAWJ,EAAE,OAAO,OAAS,WAAaG,EAAUD,EAE1DpB,EAAYuB,IAAS,CACnB,GAAGA,EACH,CAACJ,CAAI,EAAGG,CAAA,EACR,CAAA,EAGEE,EAAsBL,GAAS,CAACM,EAAGH,IAAa,CACpDtB,EAAYuB,IAAS,CACnB,GAAGA,EACH,CAACJ,CAAI,EAAGG,CAAA,EACR,CAAA,EAGEI,EAAe,MAAOR,GAAM,CAChCA,EAAE,eAAA,EAEF,GAAI,CACFb,EAAU,EAAI,EACdE,EAAS,EAAE,EACX,QAAQ,IAAI,gCAAiCR,CAAQ,EACrD,MAAMe,EAAgB,uBAAuBf,CAAQ,EACrD,QAAQ,IAAI,wCAAwC,EACpDU,EAAW,EAAE,+BAA+B,GAAK,wCAAwC,EACzFO,EAAU,EAAE,+BAA+B,GAAK,yCAA0C,SAAS,CAAA,OAC5FD,EAAK,CACZ,QAAQ,MAAM,oCAAqCA,CAAG,EACtDR,EAAS,EAAE,6BAA6B,GAAK,oCAAoC,EACjFS,EAAU,EAAE,6BAA6B,GAAK,qCAAsC,OAAO,CAAA,QAC7F,CACEX,EAAU,EAAK,CAAA,CACjB,EAGIW,EAAY,CAACW,EAASC,IAAa,CACvCjB,EAAS,CAAE,KAAM,GAAM,QAAAgB,EAAS,SAAAC,EAAU,CAAA,EAGtCC,EAAmB,IAAM,CAC7BlB,EAAS,CAAE,GAAGD,EAAO,KAAM,GAAO,CAAA,EAGpC,OAAIR,EAEA4B,EAAAA,IAACC,EAAA,CAAI,GAAI,CAAE,QAAS,OAAQ,eAAgB,SAAU,GAAI,CAAA,EACxD,SAAAD,EAAAA,IAACE,IAAiB,EACpB,SAKDD,EAAA,CAAI,GAAI,CAAE,EAAG,GACZ,SAAA,CAAAE,OAACC,GAAW,QAAQ,KAAK,UAAU,KAAK,aAAY,GAClD,SAAA,CAAAJ,MAACK,GAAa,GAAI,CAAE,GAAI,EAAG,cAAe,UAAY,EACrD,EAAE,yBAAyB,GAAK,mBAAA,EACnC,EACAF,OAACG,GAAM,GAAI,CAAE,EAAG,EAAG,GAAI,GACpB,SAAA,CAAA9B,GAASwB,EAAAA,IAACO,GAAM,SAAS,QAAQ,GAAI,CAAE,GAAI,CAAA,EAAM,SAAA/B,CAAA,CAAM,EACvDE,GAAWsB,EAAAA,IAACO,EAAA,CAAM,SAAS,UAAU,GAAI,CAAE,GAAI,CAAA,EAAM,SAAA7B,CAAA,CAAQ,EAE9DsB,EAAAA,IAAC,QAAK,SAAUJ,EACd,gBAACY,EAAA,CAAK,UAAS,GAAC,QAAS,EAEvB,SAAA,CAAAL,EAAAA,KAACK,EAAA,CAAK,KAAI,GAAC,GAAI,GACb,SAAA,CAAAL,OAACC,GAAW,QAAQ,KAAK,UAAU,KAAK,aAAY,GAClD,SAAA,CAAAJ,MAACS,GAAiB,GAAI,CAAE,GAAI,EAAG,cAAe,UAAY,EACzD,EAAE,wCAAwC,GAAK,gBAAA,EAClD,QACCC,EAAA,CAAQ,GAAI,CAAE,GAAI,EAAE,CAAG,CAAA,EAC1B,QAECF,EAAA,CAAK,KAAI,GAAC,GAAI,GAAI,GAAI,EACrB,SAAAR,EAAAA,IAACW,EAAA,CACC,QACEX,EAAAA,IAACY,EAAA,CACC,QAAS3C,EAAS,UAClB,SAAUkB,EACV,KAAK,YACL,MAAM,SAAA,CAAA,EAGV,MAAO,EAAE,4CAA4C,GAAK,kCAAA,CAAA,EAE9D,SAECqB,EAAA,CAAK,KAAI,GAAC,GAAI,GAAI,GAAI,EACrB,SAAA,CAAAR,MAACI,GAAW,aAAY,GACrB,SAAA,EAAE,iDAAiD,GAAK,4BAC3D,EACAJ,EAAAA,IAACa,EAAA,CACC,MAAO5C,EAAS,eAChB,SAAUyB,EAAmB,gBAAgB,EAC7C,kBAAgB,yBAChB,kBAAkB,OAClB,KAAM,EACN,MAAK,GACL,IAAK,EACL,IAAK,GAAA,CAAA,CACP,EACF,QAECc,EAAA,CAAK,KAAI,GAAC,GAAI,GAAI,GAAI,EACrB,SAAAR,EAAAA,IAACc,EAAA,CACC,UAAS,GACT,MAAO,EAAE,mDAAmD,GAAK,qBACjE,KAAK,mBACL,KAAK,SACL,MAAO7C,EAAS,iBAChB,SAAUkB,EACV,WAAY,CACV,eACEa,EAAAA,IAACe,EAAA,CAAe,SAAS,QACvB,SAAAf,EAAAA,IAACgB,IAAc,CAAA,CACjB,CAAA,CAEJ,CAAA,EAEJ,QAECR,EAAA,CAAK,KAAI,GAAC,GAAI,GAAI,GAAI,EACrB,SAAAR,EAAAA,IAACc,EAAA,CACC,UAAS,GACT,MAAO,EAAE,kDAAkD,GAAK,6BAChE,KAAK,kBACL,KAAK,SACL,MAAO7C,EAAS,gBAChB,SAAUkB,CAAA,CAAA,EAEd,EAGAgB,EAAAA,KAACK,EAAA,CAAK,KAAI,GAAC,GAAI,GACb,SAAA,CAAAL,EAAAA,KAACC,EAAA,CAAW,QAAQ,KAAK,UAAU,KAAK,aAAY,GAAC,GAAI,CAAE,GAAI,CAAA,EAC7D,SAAA,CAAAJ,MAACiB,GAAW,GAAI,CAAE,GAAI,EAAG,cAAe,UAAY,EACnD,EAAE,wCAAwC,GAAK,iBAAA,EAClD,QACCP,EAAA,CAAQ,GAAI,CAAE,GAAI,EAAE,CAAG,CAAA,EAC1B,EAEAV,EAAAA,IAACQ,EAAA,CAAK,KAAI,GAAC,GAAI,GACb,SAAAR,EAAAA,IAACc,EAAA,CACC,UAAS,GACT,MAAO,EAAE,kDAAkD,GAAK,4BAChE,KAAK,0BACL,MAAO7C,EAAS,wBAChB,SAAUkB,EACV,WAAY,EAAE,sDAAsD,GAAK,qGAAA,CAAA,EAE7E,QAECqB,EAAA,CAAK,KAAI,GAAC,GAAI,GAAI,GAAI,EACrB,SAAAR,EAAAA,IAACc,EAAA,CACC,UAAS,GACT,MAAO,EAAE,+CAA+C,GAAK,yBAC7D,KAAK,uBACL,KAAK,SACL,MAAO7C,EAAS,qBAChB,SAAUkB,EACV,WAAY,EAAE,mDAAmD,GAAK,2DAAA,CAAA,EAE1E,QAECqB,EAAA,CAAK,KAAI,GAAC,GAAI,GAAI,GAAI,EACrB,SAAAR,EAAAA,IAACc,EAAA,CACC,UAAS,GACT,MAAO,EAAE,6CAA6C,GAAK,yBAC3D,KAAK,qBACL,KAAK,SACL,MAAO7C,EAAS,mBAChB,SAAUkB,EACV,WAAY,EAAE,iDAAiD,GAAK,4DAAA,CAAA,EAExE,EAGAgB,EAAAA,KAACK,EAAA,CAAK,KAAI,GAAC,GAAI,GACb,SAAA,CAAAL,EAAAA,KAACC,EAAA,CAAW,QAAQ,KAAK,UAAU,KAAK,aAAY,GAAC,GAAI,CAAE,GAAI,CAAA,EAC7D,SAAA,CAAAJ,MAACkB,GAAuB,GAAI,CAAE,GAAI,EAAG,cAAe,UAAY,EAC/D,EAAE,qCAAqC,GAAK,cAAA,EAC/C,QACCR,EAAA,CAAQ,GAAI,CAAE,GAAI,EAAE,CAAG,CAAA,EAC1B,EAEAV,EAAAA,IAACQ,EAAA,CAAK,KAAI,GAAC,GAAI,GACb,SAAAR,EAAAA,IAACc,EAAA,CACC,UAAS,GACT,MAAO,EAAE,kDAAkD,GAAK,uBAChE,KAAK,qBACL,MAAO7C,EAAS,mBAChB,SAAUkB,EACV,WAAY,EAAE,sDAAsD,GAAK,uGAAA,CAAA,EAE7E,EAEAa,EAAAA,IAACQ,EAAA,CAAK,KAAI,GAAC,GAAI,GAAI,GAAI,EACrB,SAAAL,EAAAA,KAACgB,EAAA,CAAY,UAAS,GACpB,SAAA,CAAAnB,EAAAA,IAACoB,EAAA,CAAY,SAAA,EAAE,wCAAwC,GAAK,YAAY,EACxEjB,EAAAA,KAACkB,EAAA,CACC,KAAK,WACL,MAAOpD,EAAS,SAChB,MAAO,EAAE,wCAAwC,GAAK,YACtD,SAAUkB,EAEV,SAAA,CAAAa,EAAAA,IAACsB,EAAA,CAAS,MAAM,QAAQ,SAAA,QAAK,EAC7BtB,EAAAA,IAACsB,EAAA,CAAS,MAAM,OAAO,SAAA,OAAI,EAC3BtB,EAAAA,IAACsB,EAAA,CAAS,MAAM,OAAO,SAAA,UAAO,EAC9BtB,EAAAA,IAACsB,EAAA,CAAS,MAAM,QAAQ,SAAA,OAAA,CAAK,CAAA,CAAA,CAAA,CAC/B,CAAA,CACF,CAAA,CACF,QAECd,EAAA,CAAK,KAAI,GAAC,GAAI,GAAI,GAAI,EACrB,SAAAR,EAAAA,IAACW,EAAA,CACC,QACEX,EAAAA,IAACY,EAAA,CACC,QAAS3C,EAAS,uBAClB,SAAUkB,EACV,KAAK,yBACL,MAAM,SAAA,CAAA,EAGV,MAAO,EAAE,mDAAmD,GAAK,0BAAA,CAAA,EAErE,EAEClB,EAAS,wBACRkC,EAAAA,KAAAoB,EAAAA,SAAA,CACE,SAAA,CAAAvB,MAACQ,GAAK,KAAI,GAAC,GAAI,GAAI,GAAI,EACrB,SAAAR,EAAAA,IAACc,EAAA,CACC,UAAS,GACT,MAAO,EAAE,iDAAiD,GAAK,sBAC/D,KAAK,uBACL,KAAK,SACL,MAAO7C,EAAS,qBAChB,SAAUkB,EACV,WAAY,EAAE,qDAAqD,GAAK,+CAAA,CAAA,EAE5E,QAECqB,EAAA,CAAK,KAAI,GAAC,GAAI,GAAI,GAAI,EACrB,SAAAR,EAAAA,IAACc,EAAA,CACC,UAAS,GACT,MAAO,EAAE,iDAAiD,GAAK,yBAC/D,KAAK,uBACL,KAAK,SACL,MAAO7C,EAAS,qBAChB,SAAUkB,EACV,WAAY,EAAE,qDAAqD,GAAK,6EAAA,CAAA,CAC1E,CACF,CAAA,EACF,QAGDqB,EAAA,CAAK,KAAI,GAAC,GAAI,GACb,SAAAR,EAAAA,IAACC,EAAA,CAAI,GAAI,CAAE,QAAS,OAAQ,eAAgB,WAAY,GAAI,GAC1D,SAAAD,EAAAA,IAACwB,EAAA,CACC,KAAK,SACL,QAAQ,YACR,MAAM,UACN,gBAAYC,EAAA,EAAS,EACrB,SAAUnD,EAET,WACC6B,EAAAA,KAAAoB,EAAAA,SAAA,CACE,SAAA,CAAAvB,MAACE,GAAiB,KAAM,GAAI,GAAI,CAAE,GAAI,GAAK,EAC1C,EAAE,eAAe,GAAK,WAAA,EACzB,EAEA,EAAE,aAAa,GAAK,cAAA,CAAA,EAG1B,CAAA,CACF,CAAA,CAAA,CACF,CAAA,CACF,CAAA,EACF,EAEAF,EAAAA,IAAC0B,EAAA,CACC,KAAM9C,EAAM,KACZ,iBAAkB,IAClB,QAASmB,EACT,aAAc,CAAE,SAAU,SAAU,WAAY,OAAA,EAEhD,SAAAC,EAAAA,IAACO,EAAA,CAAM,QAASR,EAAkB,SAAUnB,EAAM,SAAU,GAAI,CAAE,MAAO,MAAA,EACtE,WAAM,OAAA,CACT,CAAA,CAAA,CACF,EACF,CAEJ"}