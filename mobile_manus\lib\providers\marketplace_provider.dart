import 'package:flutter/material.dart';
import 'package:mobile_manus/models/annonce.dart';
import 'package:mobile_manus/services/api/marketplace_service.dart';

class MarketplaceProvider with ChangeNotifier {
  final MarketplaceService _marketplaceService = MarketplaceService();

  List<Annonce> _announcements = [];
  List<Annonce> _favorites = [];

  List<Annonce> get announcements => _announcements;
  List<Annonce> get favorites => _favorites;

  Future<void> fetchAnnouncements({String? espece, double? prixMin, double? prixMax, int page = 1, int limit = 10}) async {
    try {
      _announcements = await _marketplaceService.getAnnouncements(espece: espece, prixMin: prixMin, prixMax: prixMax, page: page, limit: limit);
      notifyListeners();
    } catch (e) {
      print('Error fetching announcements: $e');
    }
  }

  Future<void> createAnnouncement(Annonce annonce) async {
    try {
      final newAnnonce = await _marketplaceService.createAnnouncement(annonce);
      _announcements.add(newAnnonce);
      notifyListeners();
    } catch (e) {
      print('Error creating announcement: $e');
    }
  }

  Future<void> fetchFavorites() async {
    try {
      _favorites = await _marketplaceService.getFavorites();
      notifyListeners();
    } catch (e) {
      print('Error fetching favorites: $e');
    }
  }

  Future<void> toggleFavorite(int id) async {
    try {
      await _marketplaceService.toggleFavorite(id);
      // Re-fetch favorites to update the state
      await fetchFavorites();
    } catch (e) {
      print('Error toggling favorite: $e');
    }
  }

  Future<void> loadListings() async {
    await fetchAnnouncements();
  }
}
