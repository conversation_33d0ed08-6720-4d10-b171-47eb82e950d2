import React, { useState, useEffect } from 'react';
import { useNavigate } from 'react-router-dom';
import { useAuth } from '../../contexts/AuthContext';
import axiosInstance from '../../utils/axiosConfig';
import {
  Container,
  Typography,
  Grid,
  Paper,
  Box,
  Card,
  CardContent,
  CardHeader,
  Divider,
  List,
  ListItem,
  ListItemText,
  ListItemAvatar,
  Avatar,
  Button,
  IconButton,
  Tabs,
  Tab,
  TextField,
  MenuItem,
  FormControl,
  InputLabel,
  Select,
  CircularProgress,
} from '@mui/material';
import {
  Dashboard as DashboardIcon,
  Person as PersonIcon,
  Pets as PetsIcon,
  TrendingUp as TrendingUpIcon,
  AttachMoney as MoneyIcon,
  Notifications as NotificationsIcon,
  Settings as SettingsIcon,
  Psychology as AIIcon,
  Article as ArticleIcon,
  Language as LanguageIcon,
} from '@mui/icons-material';
import { adminService, commonService } from '../../services/dbService';
import { <PERSON><PERSON><PERSON>, Bar, XAxis, YAxis, CartesianGrid, Tooltip, Legend, ResponsiveContainer, PieChart, Pie, Cell } from 'recharts';

// Composant pour afficher une carte de statistique
const StatCard = ({ title, value, icon, color }) => {
  // Formater la valeur pour gérer les cas null/undefined
  const formattedValue = value !== undefined && value !== null
    ? (typeof value === 'number' ? value.toLocaleString() : value)
    : '0';

  return (
    <Paper
      elevation={2}
      sx={{
        p: 3,
        display: 'flex',
        flexDirection: 'column',
        height: '100%',
        borderTop: `4px solid ${color}`,
      }}
    >
      <Box sx={{ display: 'flex', justifyContent: 'space-between', mb: 2 }}>
        <Typography variant="h6" component="h2" color="text.secondary">
          {title}
        </Typography>
        <Avatar sx={{ bgcolor: color, width: 40, height: 40 }}>
          {icon}
        </Avatar>
      </Box>
      <Typography variant="h4" component="div" sx={{ fontWeight: 'bold' }}>
        {formattedValue}
      </Typography>
    </Paper>
  );
};

// Composant pour afficher un graphique à barres amélioré
const EnhancedBarChart = ({ data, title, color }) => {
  return (
    <Card sx={{ height: '100%' }}>
      <CardHeader title={title} />
      <Divider />
      <CardContent sx={{ height: 250 }}>
        <ResponsiveContainer width="100%" height="100%">
          <BarChart data={data} margin={{ top: 5, right: 30, left: 20, bottom: 5 }}>
            <CartesianGrid strokeDasharray="3 3" />
            <XAxis dataKey="label" />
            <YAxis />
            <Tooltip />
            <Bar dataKey="value" fill={color} />
          </BarChart>
        </ResponsiveContainer>
      </CardContent>
    </Card>
  );
};

// Composant pour afficher les dernières activités
const RecentActivities = ({ activities }) => {
  return (
    <Card sx={{ height: '100%' }}>
      <CardHeader title="Activités Récentes" />
      <Divider />
      <CardContent sx={{ p: 0 }}>
        <List>
          {activities.map((activity, index) => (
            <React.Fragment key={index}>
              <ListItem alignItems="flex-start">
                <ListItemAvatar>
                  <Avatar sx={{ bgcolor: activity.color }}>{activity.icon}</Avatar>
                </ListItemAvatar>
                <ListItemText
                  primary={activity.title}
                  secondary={
                    <>
                      <Typography variant="body2" color="text.primary" component="span">
                        {activity.description}
                      </Typography>
                      <Typography variant="caption" display="block" color="text.secondary">
                        {activity.time}
                      </Typography>
                    </>
                  }
                />
              </ListItem>
              {index < activities.length - 1 && <Divider variant="inset" component="li" />}
            </React.Fragment>
          ))}
        </List>
      </CardContent>
    </Card>
  );
};

// Composant pour afficher les statistiques d'IA
const AIStats = () => {
  const COLORS = ['#0088FE', '#00C49F', '#FFBB28', '#FF8042'];

  const data = [
    { name: 'Articles générés', value: 12 },
    { name: 'Analyses de données', value: 8 },
    { name: 'Pages créées', value: 5 },
    { name: 'Traductions', value: 15 },
  ];

  return (
    <Card sx={{ height: '100%' }}>
      <CardHeader title="Utilisation de l'IA" />
      <Divider />
      <CardContent sx={{ height: 250 }}>
        <ResponsiveContainer width="100%" height="100%">
          <PieChart>
            <Pie
              data={data}
              cx="50%"
              cy="50%"
              labelLine={false}
              label={({ name, percent }) => `${name}: ${(percent * 100).toFixed(0)}%`}
              outerRadius={80}
              fill="#8884d8"
              dataKey="value"
            >
              {data.map((entry, index) => (
                <Cell key={`cell-${index}`} fill={COLORS[index % COLORS.length]} />
              ))}
            </Pie>
            <Tooltip />
            <Legend />
          </PieChart>
        </ResponsiveContainer>
      </CardContent>
    </Card>
  );
};

// Composant pour afficher les statistiques de blog
const BlogStats = () => {
  const data = [
    { label: 'Jan', value: 4 },
    { label: 'Fév', value: 3 },
    { label: 'Mar', value: 5 },
    { label: 'Avr', value: 2 },
    { label: 'Mai', value: 6 },
    { label: 'Juin', value: 8 },
  ];

  return (
    <Card sx={{ height: '100%' }}>
      <CardHeader title="Activité du Blog" />
      <Divider />
      <CardContent sx={{ height: 250 }}>
        <ResponsiveContainer width="100%" height="100%">
          <BarChart data={data} margin={{ top: 5, right: 30, left: 20, bottom: 5 }}>
            <CartesianGrid strokeDasharray="3 3" />
            <XAxis dataKey="label" />
            <YAxis />
            <Tooltip />
            <Bar dataKey="value" fill="#8884d8" />
          </BarChart>
        </ResponsiveContainer>
      </CardContent>
    </Card>
  );
};

function AdminDashboard() {
  const { user } = useAuth();
  const navigate = useNavigate();
  const [stats, setStats] = useState({
    totalEleveurs: 0,
    totalVolailles: 0,
    totalValeur: 0,
    totalVeterinaires: 0,
    totalMarchands: 0,
  });

  const [tabValue, setTabValue] = useState(0);
  const [volailles, setVolailles] = useState([]);
  const [eleveurs, setEleveurs] = useState([]);
  const [loading, setLoading] = useState(true);
  const [error, setError] = useState(null);
  const [notifications, setNotifications] = useState([]);

  // Données simulées pour les graphiques
  const especesData = [
    { label: 'Poulets', value: 120 },
    { label: 'Dindes', value: 80 },
    { label: 'Canards', value: 40 },
    { label: 'Cailles', value: 25 },
  ];

  const regionsData = [
    { label: 'Alger', value: 85 },
    { label: 'Oran', value: 65 },
    { label: 'Constantine', value: 45 },
    { label: 'Annaba', value: 30 },
    { label: 'Blida', value: 25 },
  ];

  const recentActivities = [
    {
      title: 'Nouvel éleveur inscrit',
      description: 'Ahmed Benali a rejoint la plateforme',
      time: 'Il y a 2 heures',
      icon: <PersonIcon />,
      color: 'primary.main',
    },
    {
      title: 'Nouveau lot ajouté',
      description: '500 poulets ajoutés par Ferme Avicole du Sud',
      time: 'Il y a 5 heures',
      icon: <PetsIcon />,
      color: 'secondary.main',
    },
    {
      title: 'Vente effectuée',
      description: 'Vente de 200 dindes pour 150,000 DA',
      time: 'Hier à 14:30',
      icon: <MoneyIcon />,
      color: 'success.main',
    },
    {
      title: 'Nouvel article de blog',
      description: 'Article sur les techniques d\'élevage moderne publié',
      time: 'Hier à 10:15',
      icon: <ArticleIcon />,
      color: 'info.main',
    },
    {
      title: 'Analyse IA générée',
      description: 'Rapport sur les tendances du marché avicole',
      time: 'Il y a 2 jours',
      icon: <AIIcon />,
      color: 'warning.main',
    },
  ];

  useEffect(() => {
    const fetchStats = async () => {
      try {
        setLoading(true);
        const response = await adminService.getStats();
        setStats({
          totalEleveurs: response.totalEleveurs || 0,
          totalVolailles: response.volailles?.total_volailles || 0,
          totalValeur: response.volailles?.prix_moyen || 0,
          totalVeterinaires: response.totalVeterinaires || 0,
          totalMarchands: response.totalMarchands || 0,
        });
        setError(null);
      } catch (err) {
        console.error('Erreur lors de la récupération des statistiques:', err);
        setError('Erreur lors de la récupération des données');
      } finally {
        setLoading(false);
      }
    };

    const fetchUsers = async () => {
      try {
        const response = await adminService.getUsers('eleveur', 1, 100);
        setEleveurs(response.users || response);
      } catch (err) {
        console.error('Erreur lors de la récupération des utilisateurs:', err);
      }
    };

    fetchStats();
    fetchUsers();
  }, []);

  const handleTabChange = (event, newValue) => {
    setTabValue(newValue);
  };

  if (loading) {
    return (
      <Box sx={{ display: 'flex', justifyContent: 'center', alignItems: 'center', height: '80vh' }}>
        <CircularProgress />
      </Box>
    );
  }

  if (error) {
    return (
      <Box sx={{ display: 'flex', justifyContent: 'center', alignItems: 'center', height: '80vh' }}>
        <Typography color="error">{error}</Typography>
      </Box>
    );
  }

  return (
    <Container maxWidth="xl" sx={{ mt: 4, mb: 4 }}>
      <Grid container spacing={3}>
        <Grid item xs={12} sm={6} md={4} lg={2.4}>
          <StatCard
            title="Total Éleveurs"
            value={stats.totalEleveurs}
            icon={<PersonIcon />}
            color="#2196f3"
          />
        </Grid>
        <Grid item xs={12} sm={6} md={4} lg={2.4}>
          <StatCard
            title="Total Volailles"
            value={stats.totalVolailles}
            icon={<PetsIcon />}
            color="#4caf50"
          />
        </Grid>
        <Grid item xs={12} sm={6} md={4} lg={2.4}>
          <StatCard
            title="Valeur Totale"
            value={`${stats.totalValeur.toLocaleString()} DA`}
            icon={<MoneyIcon />}
            color="#ff9800"
          />
        </Grid>
        <Grid item xs={12} sm={6} md={4} lg={2.4}>
          <StatCard
            title="Total Vétérinaires"
            value={stats.totalVeterinaires}
            icon={<PersonIcon />}
            color="#f44336"
          />
        </Grid>
        <Grid item xs={12} sm={6} md={4} lg={2.4}>
          <StatCard
            title="Total Marchands"
            value={stats.totalMarchands}
            icon={<PersonIcon />}
            color="#9c27b0"
          />
        </Grid>

        <Grid item xs={12} md={6}>
          <EnhancedBarChart
            data={especesData}
            title="Répartition par Espèces"
            color="#2196f3"
          />
        </Grid>

        <Grid item xs={12} md={6}>
          <EnhancedBarChart
            data={regionsData}
            title="Répartition par Région"
            color="#4caf50"
          />
        </Grid>

        <Grid item xs={12} md={4}>
          <RecentActivities activities={recentActivities} />
        </Grid>

        <Grid item xs={12} md={4}>
          <AIStats />
        </Grid>

        <Grid item xs={12} md={4}>
          <BlogStats />
        </Grid>
      </Grid>
    </Container>
  );
}

export default AdminDashboard;
