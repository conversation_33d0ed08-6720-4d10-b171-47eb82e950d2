
import {
  Grid,
  Paper,
  Box,
  Typography,
  Avatar,
  Tooltip,
  IconButton,
  Divider,
  useTheme,
  useMediaQuery
} from '@mui/material';
import {
  Pets as PetsIcon,
  AttachMoney as MoneyIcon,
  TrendingUp as TrendingUpIcon,
  ArrowUpward as ArrowUpIcon,
  ArrowDownward as ArrowDownIcon,
  Info as InfoIcon,
  ShowChart as ShowChartIcon
} from '@mui/icons-material';
import { ResponsiveContainer, AreaChart, Area } from 'recharts';

/**
 * Composant amélioré pour les cartes de statistiques
 * Affiche les statistiques clés avec des tendances et des mini-graphiques
 *
 * @param {Object} props - Les propriétés du composant
 * @param {number} props.totalVolailles - Nombre total de volailles
 * @param {number} props.valeurStock - Valeur totale du stock en DA
 * @param {number} props.ventesMois - Montant total des ventes du mois en DA
 * @param {Object} props.tendances - Données de tendance pour les mini-graphiques
 * @returns {JSX.Element} - Le composant de cartes de statistiques
 */
const StatCards = ({
  totalVolailles = 0,
  valeurStock = 0,
  ventesMois = 0,
  tendances = {}
}) => {
  const theme = useTheme();
  const isMobile = useMediaQuery(theme.breakpoints.down('sm'));

  // Données simulées pour les tendances
  const defaultTendances = {
    volailles: [
      { date: '01/11', value: 1100 },
      { date: '05/11', value: 1150 },
      { date: '10/11', value: 1200 },
      { date: '15/11', value: 1180 },
      { date: '20/11', value: 1220 },
      { date: '25/11', value: 1250 },
      { date: '30/11', value: 1200 }
    ],
    valeur: [
      { date: '01/11', value: 550000 },
      { date: '05/11', value: 580000 },
      { date: '10/11', value: 600000 },
      { date: '15/11', value: 590000 },
      { date: '20/11', value: 610000 },
      { date: '25/11', value: 640000 },
      { date: '30/11', value: 620000 }
    ],
    ventes: [
      { date: '01/11', value: 20000 },
      { date: '05/11', value: 35000 },
      { date: '10/11', value: 50000 },
      { date: '15/11', value: 65000 },
      { date: '20/11', value: 95000 },
      { date: '25/11', value: 140000 },
      { date: '30/11', value: 179000 }
    ]
  };

  // Utiliser les tendances fournies ou les tendances par défaut
  const trendData = Object.keys(tendances).length > 0 ? tendances : defaultTendances;

  // Calculer les variations en pourcentage
  const getVariation = (data) => {
    if (!data || data.length < 2) return { value: 0, isPositive: true };

    const lastValue = data[data.length - 1].value;
    const firstValue = data[0].value;

    if (firstValue === 0) return { value: 0, isPositive: true };

    const variation = ((lastValue - firstValue) / firstValue) * 100;
    return {
      value: Math.abs(variation).toFixed(1),
      isPositive: variation >= 0
    };
  };

  const volaillesVariation = getVariation(trendData.volailles);
  const valeurVariation = getVariation(trendData.valeur);
  const ventesVariation = getVariation(trendData.ventes);

  // Composant pour le mini-graphique
  const MiniChart = ({ data, color }) => (
    <Box sx={{ height: 40, width: '100%' }}>
      <ResponsiveContainer width="100%" height="100%">
        <AreaChart data={data} margin={{ top: 0, right: 0, left: 0, bottom: 0 }}>
          <defs>
            <linearGradient id={`colorGradient-${color.replace('#', '')}`} x1="0" y1="0" x2="0" y2="1">
              <stop offset="5%" stopColor={color} stopOpacity={0.8} />
              <stop offset="95%" stopColor={color} stopOpacity={0} />
            </linearGradient>
          </defs>
          <Area
            type="monotone"
            dataKey="value"
            stroke={color}
            fillOpacity={1}
            fill={`url(#colorGradient-${color.replace('#', '')})`}
          />
        </AreaChart>
      </ResponsiveContainer>
    </Box>
  );

  // Composant pour l'indicateur de tendance
  const TrendIndicator = ({ variation, showIcon = true }) => (
    <Box
      sx={{
        display: 'flex',
        alignItems: 'center',
        color: variation.isPositive ? 'success.main' : 'error.main',
        fontSize: '0.875rem',
        fontWeight: 500
      }}
    >
      {showIcon && (variation.isPositive ? <ArrowUpIcon fontSize="small" /> : <ArrowDownIcon fontSize="small" />)}
      {variation.value}%
    </Box>
  );

  return (
    <Grid container spacing={3}>
      {/* Total Volailles */}
      <Grid item xs={12} sm={6} md={4}>
        <Paper
          elevation={2}
          sx={{
            p: 2,
            height: '100%',
            borderRadius: 2,
            background: 'linear-gradient(135deg, #f0f9ff 0%, #e6f7ff 100%)',
            border: '1px solid #e6f7ff',
            transition: 'transform 0.3s ease-in-out, box-shadow 0.3s ease-in-out',
            '&:hover': {
              transform: 'translateY(-5px)',
              boxShadow: '0 8px 16px rgba(0, 0, 0, 0.1)'
            }
          }}
        >
          <Box sx={{ display: 'flex', justifyContent: 'space-between', mb: 1 }}>
            <Typography variant="subtitle1" color="text.secondary" fontWeight={500}>
              Total Volailles
            </Typography>
            <Avatar sx={{ bgcolor: 'primary.main', width: 40, height: 40 }}>
              <PetsIcon />
            </Avatar>
          </Box>

          <Box sx={{ display: 'flex', alignItems: 'baseline', mb: 1 }}>
            <Typography variant="h4" component="div" fontWeight="bold">
              {totalVolailles.toLocaleString()}
            </Typography>
            <Box sx={{ ml: 2, display: 'flex', alignItems: 'center' }}>
              <TrendIndicator variation={volaillesVariation} />
              <Tooltip title="Par rapport au début du mois">
                <IconButton size="small">
                  <InfoIcon fontSize="small" />
                </IconButton>
              </Tooltip>
            </Box>
          </Box>

          <Divider sx={{ my: 1 }} />

          <Box sx={{ display: 'flex', justifyContent: 'space-between', alignItems: 'center' }}>
            <Typography variant="caption" color="text.secondary">
              Évolution ce mois
            </Typography>
            <Tooltip title="Voir les détails">
              <IconButton size="small">
                <ShowChartIcon fontSize="small" />
              </IconButton>
            </Tooltip>
          </Box>

          <MiniChart data={trendData.volailles} color={theme.palette.primary.main} />
        </Paper>
      </Grid>

      {/* Valeur du Stock */}
      <Grid item xs={12} sm={6} md={4}>
        <Paper
          elevation={2}
          sx={{
            p: 2,
            height: '100%',
            borderRadius: 2,
            background: 'linear-gradient(135deg, #f5f3ff 0%, #ede9fe 100%)',
            border: '1px solid #ede9fe',
            transition: 'transform 0.3s ease-in-out, box-shadow 0.3s ease-in-out',
            '&:hover': {
              transform: 'translateY(-5px)',
              boxShadow: '0 8px 16px rgba(0, 0, 0, 0.1)'
            }
          }}
        >
          <Box sx={{ display: 'flex', justifyContent: 'space-between', mb: 1 }}>
            <Typography variant="subtitle1" color="text.secondary" fontWeight={500}>
              Valeur du Stock
            </Typography>
            <Avatar sx={{ bgcolor: 'secondary.main', width: 40, height: 40 }}>
              <MoneyIcon />
            </Avatar>
          </Box>

          <Box sx={{ display: 'flex', alignItems: 'baseline', mb: 1 }}>
            <Typography variant="h4" component="div" fontWeight="bold">
              {valeurStock.toLocaleString()} DA
            </Typography>
            <Box sx={{ ml: 2, display: 'flex', alignItems: 'center' }}>
              <TrendIndicator variation={valeurVariation} />
              <Tooltip title="Par rapport au début du mois">
                <IconButton size="small">
                  <InfoIcon fontSize="small" />
                </IconButton>
              </Tooltip>
            </Box>
          </Box>

          <Divider sx={{ my: 1 }} />

          <Box sx={{ display: 'flex', justifyContent: 'space-between', alignItems: 'center' }}>
            <Typography variant="caption" color="text.secondary">
              Évolution ce mois
            </Typography>
            <Tooltip title="Voir les détails">
              <IconButton size="small">
                <ShowChartIcon fontSize="small" />
              </IconButton>
            </Tooltip>
          </Box>

          <MiniChart data={trendData.valeur} color={theme.palette.secondary.main} />
        </Paper>
      </Grid>

      {/* Ventes du Mois */}
      <Grid item xs={12} sm={6} md={4}>
        <Paper
          elevation={2}
          sx={{
            p: 2,
            height: '100%',
            borderRadius: 2,
            background: 'linear-gradient(135deg, #f0fdf4 0%, #dcfce7 100%)',
            border: '1px solid #dcfce7',
            transition: 'transform 0.3s ease-in-out, box-shadow 0.3s ease-in-out',
            '&:hover': {
              transform: 'translateY(-5px)',
              boxShadow: '0 8px 16px rgba(0, 0, 0, 0.1)'
            }
          }}
        >
          <Box sx={{ display: 'flex', justifyContent: 'space-between', mb: 1 }}>
            <Typography variant="subtitle1" color="text.secondary" fontWeight={500}>
              Ventes du Mois
            </Typography>
            <Avatar sx={{ bgcolor: 'success.main', width: 40, height: 40 }}>
              <TrendingUpIcon />
            </Avatar>
          </Box>

          <Box sx={{ display: 'flex', alignItems: 'baseline', mb: 1 }}>
            <Typography variant="h4" component="div" fontWeight="bold">
              {ventesMois.toLocaleString()} DA
            </Typography>
            <Box sx={{ ml: 2, display: 'flex', alignItems: 'center' }}>
              <TrendIndicator variation={ventesVariation} />
              <Tooltip title="Par rapport au mois précédent">
                <IconButton size="small">
                  <InfoIcon fontSize="small" />
                </IconButton>
              </Tooltip>
            </Box>
          </Box>

          <Divider sx={{ my: 1 }} />

          <Box sx={{ display: 'flex', justifyContent: 'space-between', alignItems: 'center' }}>
            <Typography variant="caption" color="text.secondary">
              Progression ce mois
            </Typography>
            <Tooltip title="Voir les détails">
              <IconButton size="small">
                <ShowChartIcon fontSize="small" />
              </IconButton>
            </Tooltip>
          </Box>

          <MiniChart data={trendData.ventes} color={theme.palette.success.main} />
        </Paper>
      </Grid>
    </Grid>
  );
};

export default StatCards;
