import 'package:dio/dio.dart';
import 'package:mobile_manus/models/annonce.dart';

class MarketplaceService {
  final Dio _dio = Dio();  final String baseUrl = 'http://192.168.1.102:3003/api';

  MarketplaceService() {
    _dio.options.baseUrl = baseUrl;
    _dio.options.connectTimeout = const Duration(seconds: 5);
    _dio.options.receiveTimeout = const Duration(seconds: 3);
  }

  void setAuthToken(String token) {
    _dio.options.headers['Authorization'] = 'Bearer $token';
  }

  Future<Map<String, dynamic>> getAnnonces({
    int page = 1,
    int limit = 10,
    String? espece,
    double? prixMin,
    double? prixMax,
  }) async {
    try {
      final queryParams = <String, dynamic>{
        'page': page,
        'limit': limit,
      };

      if (espece != null) queryParams['espece'] = espece;
      if (prixMin != null) queryParams['prix_min'] = prixMin;
      if (prixMax != null) queryParams['prix_max'] = prixMax;

      final response = await _dio.get(
        '/marketplace/annonces',
        queryParameters: queryParams,
      );

      if (response.statusCode == 200) {
        return response.data;
      } else {
        throw Exception('Failed to load annonces');
      }
    } on DioException catch (e) {
      if (e.response != null) {
        throw Exception('Annonces error: ${e.response?.data['message'] ?? 'Unknown error'}');
      } else {
        throw Exception('Network error: ${e.message}');
      }
    }
  }

  Future<Annonce> getAnnonce(int id) async {
    try {
      final response = await _dio.get('/marketplace/annonces/$id');

      if (response.statusCode == 200) {
        return Annonce.fromJson(response.data);
      } else {
        throw Exception('Failed to load annonce');
      }
    } on DioException catch (e) {
      if (e.response != null) {
        throw Exception('Annonce error: ${e.response?.data['message'] ?? 'Unknown error'}');
      } else {
        throw Exception('Network error: ${e.message}');
      }
    }
  }

  Future<Annonce> createAnnonce(Map<String, dynamic> annonceData) async {
    try {
      final response = await _dio.post(
        '/marketplace/annonces',
        data: annonceData,
      );

      if (response.statusCode == 201) {
        return Annonce.fromJson(response.data);
      } else {
        throw Exception('Failed to create annonce');
      }
    } on DioException catch (e) {
      if (e.response != null) {
        throw Exception('Create annonce error: ${e.response?.data['message'] ?? 'Unknown error'}');
      } else {
        throw Exception('Network error: ${e.message}');
      }
    }
  }

  Future<Annonce> updateAnnonce(int id, Map<String, dynamic> annonceData) async {
    try {
      final response = await _dio.put(
        '/marketplace/annonces/$id',
        data: annonceData,
      );

      if (response.statusCode == 200) {
        return Annonce.fromJson(response.data);
      } else {
        throw Exception('Failed to update annonce');
      }
    } on DioException catch (e) {
      if (e.response != null) {
        throw Exception('Update annonce error: ${e.response?.data['message'] ?? 'Unknown error'}');
      } else {
        throw Exception('Network error: ${e.message}');
      }
    }
  }

  Future<void> deleteAnnonce(int id) async {
    try {
      final response = await _dio.delete('/marketplace/annonces/$id');

      if (response.statusCode != 200) {
        throw Exception('Failed to delete annonce');
      }
    } on DioException catch (e) {
      if (e.response != null) {
        throw Exception('Delete annonce error: ${e.response?.data['message'] ?? 'Unknown error'}');
      } else {
        throw Exception('Network error: ${e.message}');
      }
    }
  }

  Future<Map<String, dynamic>> toggleFavorite(int annonceId) async {
    try {
      final response = await _dio.post('/marketplace/annonces/$annonceId/favoris');

      if (response.statusCode == 200) {
        return response.data;
      } else {
        throw Exception('Failed to toggle favorite');
      }
    } on DioException catch (e) {
      if (e.response != null) {
        throw Exception('Favorite error: ${e.response?.data['message'] ?? 'Unknown error'}');
      } else {
        throw Exception('Network error: ${e.message}');
      }
    }
  }

  Future<List<Annonce>> getFavorites() async {
    try {
      final response = await _dio.get('/marketplace/favoris');

      if (response.statusCode == 200) {
        final List<dynamic> data = response.data;
        return data.map((json) => Annonce.fromJson(json)).toList();
      } else {
        throw Exception('Failed to load favorites');
      }
    } on DioException catch (e) {
      if (e.response != null) {
        throw Exception('Favorites error: ${e.response?.data['message'] ?? 'Unknown error'}');
      } else {
        throw Exception('Network error: ${e.message}');
      }
    }
  }

  Future<void> removeFavorite(int annonceId) async {
    try {
      final response = await _dio.delete('/marketplace/favoris/$annonceId');

      if (response.statusCode != 200) {
        throw Exception('Failed to remove favorite');
      }
    } on DioException catch (e) {
      if (e.response != null) {
        throw Exception('Remove favorite error: ${e.response?.data['message'] ?? 'Unknown error'}');
      } else {
        throw Exception('Network error: ${e.message}');
      }
    }
  }

  Future<List<Annonce>> searchAnnonces(String query, {
    String? espece,
    double? prixMin,
    double? prixMax,
  }) async {
    try {
      final queryParams = <String, dynamic>{
        'q': query,
      };

      if (espece != null) queryParams['espece'] = espece;
      if (prixMin != null) queryParams['prix_min'] = prixMin;
      if (prixMax != null) queryParams['prix_max'] = prixMax;

      final response = await _dio.get(
        '/marketplace/search',
        queryParameters: queryParams,
      );

      if (response.statusCode == 200) {
        final List<dynamic> data = response.data['annonces'] ?? response.data;
        return data.map((json) => Annonce.fromJson(json)).toList();
      } else {
        throw Exception('Failed to search annonces');
      }
    } on DioException catch (e) {
      if (e.response != null) {
        throw Exception('Search error: ${e.response?.data['message'] ?? 'Unknown error'}');
      } else {
        throw Exception('Network error: ${e.message}');
      }
    }
  }

  Future<List<Map<String, dynamic>>> getCategories() async {
    try {
      final response = await _dio.get('/marketplace/categories');

      if (response.statusCode == 200) {
        return List<Map<String, dynamic>>.from(response.data);
      } else {
        throw Exception('Failed to load categories');
      }
    } on DioException catch (e) {
      if (e.response != null) {
        throw Exception('Categories error: ${e.response?.data['message'] ?? 'Unknown error'}');
      } else {
        throw Exception('Network error: ${e.message}');
      }
    }
  }

  Future<Map<String, dynamic>> getMarketplaceStats() async {
    try {
      final response = await _dio.get('/marketplace/stats');

      if (response.statusCode == 200) {
        return response.data;
      } else {
        throw Exception('Failed to load marketplace stats');
      }
    } on DioException catch (e) {
      if (e.response != null) {
        throw Exception('Stats error: ${e.response?.data['message'] ?? 'Unknown error'}');
      } else {
        throw Exception('Network error: ${e.message}');
      }
    }
  }

  Future<List<Annonce>> getAnnouncements({
    String? espece,
    double? prixMin,
    double? prixMax,
    int page = 1,
    int limit = 10,
  }) async {
    try {
      final response = await getAnnonces(
        espece: espece,
        prixMin: prixMin,
        prixMax: prixMax,
        page: page,
        limit: limit,
      );

      final List<dynamic> annoncesList = response['data'];
      return annoncesList.map((json) => Annonce.fromJson(json)).toList();
    } catch (e) {
      throw Exception('Failed to get announcements: $e');
    }
  }

  Future<Annonce> createAnnouncement(Annonce annonce) async {
    try {
      final response = await _dio.post(
        '/marketplace/annonces',
        data: annonce.toJson(),
      );

      if (response.statusCode == 201) {
        return Annonce.fromJson(response.data['data']);
      } else {
        throw Exception('Failed to create announcement');
      }
    } on DioException catch (e) {
      if (e.response != null) {
        throw Exception('Create announcement error: ${e.response?.data['message'] ?? 'Unknown error'}');
      } else {
        throw Exception('Network error: ${e.message}');
      }
    }
  }
}
