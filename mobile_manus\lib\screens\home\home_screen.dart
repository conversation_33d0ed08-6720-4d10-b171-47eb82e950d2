import 'package:flutter/material.dart';
import 'package:provider/provider.dart';
import 'package:mobile_manus/providers/auth_provider.dart';
import 'package:mobile_manus/screens/dashboard/eleveur_dashboard.dart';
import 'package:mobile_manus/screens/dashboard/veterinaire_dashboard.dart';
import 'package:mobile_manus/screens/dashboard/marchand_dashboard.dart';
import 'package:mobile_manus/screens/feed/feed_management_screen.dart';
import 'package:mobile_manus/screens/marketplace/marketplace_screen.dart';

class HomeScreen extends StatelessWidget {
  const HomeScreen({super.key});

  @override
  Widget build(BuildContext context) {
    return Consumer<AuthProvider>(
      builder: (context, authProvider, child) {
        final user = authProvider.user;
        
        if (user == null) {
          return const Scaffold(
            body: Center(
              child: CircularProgressIndicator(),
            ),
          );
        }

        // Route to appropriate dashboard based on user role
        switch (user.role) {
          case 'eleveur':
            return EleveurDashboard(user: user);
          case 'veterinaire':
            return VeterinaireDashboard(user: user);
          case 'marchand':
            return MarchandDashboard(user: user);
          case 'acheteur':
            return _buildGenericDashboard(context, user, 'Acheteur Dashboard');
          case 'admin':
            return _buildGenericDashboard(context, user, 'Admin Dashboard');
          default:
            return _buildGenericDashboard(context, user, 'Dashboard');
        }
      },
    );
  }

  Widget _buildGenericDashboard(BuildContext context, user, String title) {
    return Scaffold(
      appBar: AppBar(
        title: Text(title),
        actions: [
          IconButton(
            icon: const Icon(Icons.logout),
            onPressed: () {
              Provider.of<AuthProvider>(context, listen: false).logout();
            },
          ),
        ],
      ),
      body: Center(
        child: Column(
          mainAxisAlignment: MainAxisAlignment.center,
          children: [
            Text(
              'Bienvenue, ${user.firstName ?? user.username}!',
              style: Theme.of(context).textTheme.headlineMedium,
            ),
            const SizedBox(height: 20),
            Text(
              'Rôle: ${user.role}',
              style: Theme.of(context).textTheme.bodyLarge,
            ),
            const SizedBox(height: 20),
            ElevatedButton(
              onPressed: () {
                Navigator.push(
                  context,
                  MaterialPageRoute(
                    builder: (context) => const FeedManagementScreen(),
                  ),
                );
              },
              child: const Text('Gestion des Aliments'),
            ),
            const SizedBox(height: 10),
            ElevatedButton(
              onPressed: () {
                Navigator.push(
                  context,
                  MaterialPageRoute(
                    builder: (context) => const MarketplaceScreen(),
                  ),
                );
              },
              child: const Text('Marketplace'),
            ),
          ],
        ),
      ),
    );
  }
}

