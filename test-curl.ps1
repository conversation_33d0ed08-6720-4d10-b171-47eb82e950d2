# Set default encoding to UTF-8
$PSDefaultParameterValues['*:Encoding'] = 'UTF8'
[Console]::OutputEncoding = [System.Text.Encoding]::UTF8

# Function to check if server is running
function Test-ServerConnection {
    try {
        $response = Invoke-WebRequest -Uri "http://localhost:3003/api/health" -Method GET -TimeoutSec 5
        return $true
    }
    catch {
        return $false
    }
}

# Function to make API requests
function Invoke-ApiRequest {
    param (
        [string]$Method,
        [string]$Endpoint,
        [string]$Body = "",
        [string]$Token = ""
    )

    $headers = @{
        "Content-Type" = "application/json"
    }

    if ($Token) {
        $headers["Authorization"] = "Bearer $Token"
    }

    $params = @{
        Uri         = "http://localhost:3003$Endpoint"
        Method      = $Method
        ContentType = "application/json"
        Headers     = $headers
    }

    if ($Body) {
        $params["Body"] = $Body
    }    try {
        # Add a small delay between requests
        Start-Sleep -Milliseconds 500

        $response = Invoke-RestMethod @params

        # Format the response for display
        if ($response) {
            $responseInfo = if ($response -is [Array]) {
                "Found $($response.Count) items"
            }
            else {
                ($response | ConvertTo-Json -Depth 2).Substring(0, [Math]::Min(100, ($response | ConvertTo-Json -Depth 2).Length)) + "..."
            }
            Write-Host "Success! Response: $responseInfo" -ForegroundColor Green
        }

        return $response
    }
    catch {
        $statusCode = $_.Exception.Response.StatusCode.value__
        $statusDescription = $_.Exception.Response.StatusDescription
        $errorMessage = $_.Exception.Message

        Write-Host "Error testing $Endpoint" -ForegroundColor Red
        Write-Host "Status Code: $statusCode" -ForegroundColor Red
        Write-Host "Description: $statusDescription" -ForegroundColor Red
        Write-Host "Message: $errorMessage" -ForegroundColor Red
        return $null
    }
}

# Check if server is running
Write-Host "`n=== Checking Server Status ===`n" -ForegroundColor Cyan
if (-not (Test-ServerConnection)) {
    Write-Host "Error: Cannot connect to server at http://localhost:3003" -ForegroundColor Red
    Write-Host "Please make sure the server is running and try again." -ForegroundColor Red
    exit 1
}
Write-Host "Server is running and accessible`n" -ForegroundColor Green

Write-Host "`n=== Testing Authentication Endpoints ===`n" -ForegroundColor Cyan

# Test login (admin)
Write-Host "Testing POST /api/auth/login (admin)" -ForegroundColor Yellow
$loginResponse = Invoke-ApiRequest -Method "POST" -Endpoint "/api/auth/login" `
    -Body '{"email":"<EMAIL>","password":"admin123"}'
if ($loginResponse) {
    Write-Host "Login successful. Token received." -ForegroundColor Green
    $token = $loginResponse.token
    $adminId = $loginResponse.user.id
    Write-Host "Logged in as: $($loginResponse.user.username) (Role: $($loginResponse.user.role))`n"
}
else {
    Write-Host "Login failed. Cannot continue tests.`n" -ForegroundColor Red
    exit
}

# Store user info
$userId = $loginResponse.user.id
Write-Host "Logged in as: $($loginResponse.user.username) (Role: $($loginResponse.user.role))`n"

# Test admin routes
Write-Host "`n=== Testing Admin Routes ===`n" -ForegroundColor Cyan

# Get users list
Write-Host "Testing GET /api/admin/users" -ForegroundColor Yellow
$users = Invoke-ApiRequest -Method "GET" -Endpoint "/api/admin/users" -Token $token
if ($users) {
    Write-Host "Found $($users.Count) users`n"
}

# Get roles list
Write-Host "Testing GET /api/admin/roles" -ForegroundColor Yellow
$roles = Invoke-ApiRequest -Method "GET" -Endpoint "/api/admin/roles" -Token $token
if ($roles) {
    Write-Host "Found $($roles.Count) roles`n"
}

Write-Host "`n=== Testing User Management Endpoints ===`n" -ForegroundColor Cyan

# List users
Write-Host "Testing GET /api/users" -ForegroundColor Yellow
$users = Invoke-ApiRequest -Method "GET" -Endpoint "/api/users" -Token $token
Write-Host "Users found: $($users.Count)`n"

# Get specific user
Write-Host "Testing GET /api/users/$userId" -ForegroundColor Yellow
$userDetails = Invoke-ApiRequest -Method "GET" -Endpoint "/api/users/$userId" -Token $token
Write-Host "User details retrieved`n"

Write-Host "`n=== Testing Éleveur Endpoints ===`n" -ForegroundColor Cyan

# List éleveurs
Write-Host "Testing GET /api/eleveurs" -ForegroundColor Yellow
$eleveurs = Invoke-ApiRequest -Method "GET" -Endpoint "/api/eleveurs" -Token $token
Write-Host "Éleveurs found: $($eleveurs.Count)`n"

# Get éleveur dashboard (admin only)
if ($adminId) {
    Write-Host "Testing GET /api/eleveurs/$adminId/dashboard" -ForegroundColor Yellow
    $eleveurDashboard = Invoke-ApiRequest -Method "GET" -Endpoint "/api/eleveurs/$adminId/dashboard" -Token $token
}

Write-Host "`n=== Testing Vétérinaire Endpoints ===`n" -ForegroundColor Cyan

# List vétérinaires
Write-Host "Testing GET /api/veterinaires" -ForegroundColor Yellow
$veterinaires = Invoke-ApiRequest -Method "GET" -Endpoint "/api/veterinaires" -Token $token
Write-Host "Vétérinaires found: $($veterinaires.Count)`n"

# Get vétérinaire profile
Write-Host "Testing GET /api/veterinaire/profile" -ForegroundColor Yellow
$veterinaireProfile = Invoke-ApiRequest -Method "GET" -Endpoint "/api/veterinaire/profile" -Token $token

# Get vétérinaire dashboard
Write-Host "Testing GET /api/veterinaire/dashboard" -ForegroundColor Yellow
$veterinaireDashboard = Invoke-ApiRequest -Method "GET" -Endpoint "/api/veterinaire/dashboard" -Token $token

# Get vétérinaire notifications
Write-Host "Testing GET /api/veterinaire/notifications" -ForegroundColor Yellow
$veterinaireNotifications = Invoke-ApiRequest -Method "GET" -Endpoint "/api/veterinaire/notifications" -Token $token

# Get specific vétérinaire (admin only)
if ($adminId) {
    Write-Host "Testing GET /api/veterinaires/$adminId" -ForegroundColor Yellow
    $veterinaireDetails = Invoke-ApiRequest -Method "GET" -Endpoint "/api/veterinaires/$adminId" -Token $token
}

Write-Host "`n=== Testing Volailles Endpoints ===`n" -ForegroundColor Cyan

# List volailles
Write-Host "Testing GET /api/volailles" -ForegroundColor Yellow
$volailles = Invoke-ApiRequest -Method "GET" -Endpoint "/api/volailles" -Token $token
Write-Host "Volailles found: $($volailles.Count)`n"

# Get specific volaille (admin only)
if ($adminId) {
    Write-Host "Testing GET /api/volailles/$adminId" -ForegroundColor Yellow
    $volailleById = Invoke-ApiRequest -Method "GET" -Endpoint "/api/volailles/$adminId" -Token $token

    # Get volailles by éleveur (admin only)
    Write-Host "Testing GET /api/volailles/eleveur/$adminId" -ForegroundColor Yellow
    $volaillesByEleveur = Invoke-ApiRequest -Method "GET" -Endpoint "/api/volailles/eleveur/$adminId" -Token $token
}

Write-Host "`n=== Testing Prescriptions Endpoints ===`n" -ForegroundColor Cyan

# Get prescriptions by vétérinaire (admin only)
if ($adminId) {
    Write-Host "Testing GET /api/prescriptions/veterinaire/$adminId" -ForegroundColor Yellow
    $prescriptionsByVet = Invoke-ApiRequest -Method "GET" -Endpoint "/api/prescriptions/veterinaire/$adminId" -Token $token

    # Get prescriptions by éleveur (admin only)
    Write-Host "Testing GET /api/prescriptions/eleveur/$adminId" -ForegroundColor Yellow
    $prescriptionsByEleveur = Invoke-ApiRequest -Method "GET" -Endpoint "/api/prescriptions/eleveur/$adminId" -Token $token

    # Get specific prescription (admin only)
    Write-Host "Testing GET /api/prescriptions/$adminId" -ForegroundColor Yellow
    $prescriptionById = Invoke-ApiRequest -Method "GET" -Endpoint "/api/prescriptions/$adminId" -Token $token
}

Write-Host "`n=== Testing Produits Endpoints ===`n" -ForegroundColor Cyan

# Search produits
Write-Host "Testing GET /api/produits/recherche" -ForegroundColor Yellow
$produitsSearch = Invoke-ApiRequest -Method "GET" -Endpoint "/api/produits/recherche" -Token $token
if ($adminId) {
    # Get produits by marchand (admin only)
    Write-Host "Testing GET /api/produits/marchand/$adminId" -ForegroundColor Yellow
    $produitsByMarchand = Invoke-ApiRequest -Method "GET" -Endpoint "/api/produits/marchand/$adminId" -Token $token

    # Get specific produit (admin only)
    Write-Host "Testing GET /api/produits/$adminId" -ForegroundColor Yellow
    $produitById = Invoke-ApiRequest -Method "GET" -Endpoint "/api/produits/$adminId" -Token $token
}

Write-Host "`n=== Testing Production d'oeufs Endpoints ===`n" -ForegroundColor Cyan

# List production d'oeufs
Write-Host "Testing GET /api/production-oeufs" -ForegroundColor Yellow
$productionOeufs = Invoke-ApiRequest -Method "GET" -Endpoint "/api/production-oeufs" -Token $token

# Get production d'oeufs statistiques
Write-Host "Testing GET /api/production-oeufs/statistiques" -ForegroundColor Yellow
$productionOeufsStats = Invoke-ApiRequest -Method "GET" -Endpoint "/api/production-oeufs/statistiques" -Token $token

Write-Host "`n=== Testing Suivi vétérinaire Endpoints ===`n" -ForegroundColor Cyan

# List suivi vétérinaire
Write-Host "Testing GET /api/suivi-veterinaire" -ForegroundColor Yellow
$suiviVeterinaire = Invoke-ApiRequest -Method "GET" -Endpoint "/api/suivi-veterinaire" -Token $token

# Get planified suivis vétérinaires
Write-Host "Testing GET /api/suivi-veterinaire/planifies" -ForegroundColor Yellow
$suiviVeterinairePlanifies = Invoke-ApiRequest -Method "GET" -Endpoint "/api/suivi-veterinaire/planifies" -Token $token

Write-Host "`n=== Testing Ventes Endpoints ===`n" -ForegroundColor Cyan

# Get ventes by éleveur (admin only)
if ($adminId) {
    Write-Host "Testing GET /api/ventes/eleveur/$adminId" -ForegroundColor Yellow
    $ventesByEleveur = Invoke-ApiRequest -Method "GET" -Endpoint "/api/ventes/eleveur/$adminId" -Token $token

    # Get specific vente (admin only)
    Write-Host "Testing GET /api/ventes/$adminId" -ForegroundColor Yellow
    $venteById = Invoke-ApiRequest -Method "GET" -Endpoint "/api/ventes/$adminId" -Token $token
}

Write-Host "`n=== Testing Translations Endpoints ===`n" -ForegroundColor Cyan

# List translations
Write-Host "Testing GET /api/translations" -ForegroundColor Yellow
$translations = Invoke-ApiRequest -Method "GET" -Endpoint "/api/translations" -Token $token

# Get translation languages
Write-Host "Testing GET /api/translations/languages" -ForegroundColor Yellow
$translationLanguages = Invoke-ApiRequest -Method "GET" -Endpoint "/api/translations/languages" -Token $token

# Get translation categories
Write-Host "Testing GET /api/translations/categories" -ForegroundColor Yellow
$translationCategories = Invoke-ApiRequest -Method "GET" -Endpoint "/api/translations/categories" -Token $token

Write-Host "`n=== Test Complete ===`n" -ForegroundColor Green
