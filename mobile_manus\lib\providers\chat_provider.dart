import 'package:flutter/material.dart';
import 'package:mobile_manus/models/chat_message.dart';
import 'package:mobile_manus/models/chat_thread.dart';
import 'package:mobile_manus/services/api/chat_service.dart';

class ChatProvider with ChangeNotifier {
  final ChatService _chatService = ChatService();

  List<ChatThread> _chatThreads = [];
  List<ChatMessage> _currentChatMessages = [];

  List<ChatThread> get chatThreads => _chatThreads;
  List<ChatMessage> get currentChatMessages => _currentChatMessages;

  Future<void> fetchChatThreads() async {
    try {
      _chatThreads = await _chatService.getChatThreads();
      notifyListeners();
    } catch (e) {
      print('Error fetching chat threads: $e');
    }
  }

  Future<void> fetchChatMessages(int threadId) async {
    try {
      _currentChatMessages = await _chatService.getChatMessages(threadId);
      notifyListeners();
    } catch (e) {
      print('Error fetching chat messages: $e');
    }
  }

  Future<void> sendMessage(int threadId, String content) async {
    try {
      final newMessage = await _chatService.sendMessage(threadId, content);
      _currentChatMessages.add(newMessage);
      notifyListeners();
    } catch (e) {
      print('Error sending message: $e');
    }
  }
}


