import { createContext, useState, useEffect, useContext } from 'react';
import api from '../services/api';
import { firebaseAuth } from '../services/firebaseAuth';
import { auth } from '../services/firebaseConfig';

const AuthContext = createContext();

export const useAuth = () => useContext(AuthContext);

export const AuthProvider = ({ children }) => {
  const [user, setUser] = useState(null);
  const [token, setToken] = useState(localStorage.getItem('token'));
  const [loading, setLoading] = useState(true);
  const [error, setError] = useState(null);

  // L'instance api gère automatiquement les tokens via les intercepteurs
  useEffect(() => {
    if (!token) {
      setLoading(false);
      return;
    }
  }, [token]);

  // Load user data if token exists
  useEffect(() => {
    const loadUser = async () => {
      console.log('🔍 AuthContext: Chargement utilisateur, token:', !!token);
      if (!token) {
        console.log('❌ AuthContext: Pas de token, arrêt du chargement');
        setLoading(false);
        return;
      }

      try {        console.log('📡 AuthContext: Appel API /api/auth/user');
        const res = await api.get('/api/auth/user');
        console.log('✅ AuthContext: Utilisateur chargé:', res.data);

        // Ensure role is properly formatted as string
        const userData = res.data;
        if (userData.role && typeof userData.role === 'object') {
          userData.role = userData.role.name;
        }

        console.log('🔧 AuthContext: Rôle formaté:', userData.role);
        setUser(userData);
        setError(null);
      } catch (err) {
        console.error('❌ AuthContext: Erreur chargement utilisateur:', err);
        setError('Session expirée. Veuillez vous reconnecter.');
        // Log the full error response if available
        if (err.response) {
          console.error('❌ AuthContext: Réponse d\'erreur:', err.response.data);
          console.error('❌ AuthContext: Statut:', err.response.status);
          console.error('❌ AuthContext: En-têtes:', JSON.stringify(err.response.headers));
        } else {
          console.error('❌ AuthContext: Aucune réponse d\'erreur:', err);
        }
        localStorage.removeItem('token');
        setToken(null);
        setUser(null);
      } finally {
        console.log('🏁 AuthContext: Fin du chargement');
        setLoading(false);
      }
    };

    loadUser();
  }, [token]);

  // Login user
  const login = async (email, password) => {
    try {
      setLoading(true);
      setError(null);

      console.log('🔐 AuthContext: Début de la connexion pour:', email);
      const authData = await firebaseAuth.login(email, password);
      console.log('✅ AuthContext: Données d\'authentification reçues:', {
        hasToken: !!authData.token,
        hasUser: !!authData.user,
        userRole: authData.user?.role
      });

      // Le token est déjà stocké dans localStorage par firebaseAuth.login
      setToken(authData.token);
      setUser(authData.user);

      console.log('🎯 AuthContext: État mis à jour - utilisateur connecté avec le rôle:', authData.user?.role);
      return authData.user;
    } catch (err) {
      console.error('❌ AuthContext: Erreur de connexion:', err);
      setError(err.message || 'Erreur de connexion');
      throw err;
    } finally {
      setLoading(false);
    }
  };

  // Register user
  const register = async (userData) => {
    try {
      setLoading(true);
      // Utiliser Firebase Auth pour l'inscription
      const authData = await firebaseAuth.register(userData);
      return authData;
    } catch (err) {
      setError(err.message || 'Erreur d\'inscription');
      throw err;
    } finally {
      setLoading(false);
    }
  };

  // Logout user
  const logout = async () => {
    try {
      await firebaseAuth.logout();
      setToken(null);
      setUser(null);
      localStorage.removeItem('originalAdminToken');
      localStorage.removeItem('impersonationExpiresAt');
    } catch (err) {
      console.error('Erreur lors de la déconnexion:', err);
      setError(err.message || 'Erreur lors de la déconnexion');
    }
  };

  // Update user profile
  const updateProfile = async (profileData) => {
    try {
      setLoading(true);
      const res = await api.put('/api/auth/profile', profileData);
      setUser(res.data);
      return res.data;
    } catch (err) {
      setError(err.response?.data?.message || 'Erreur de mise à jour du profil');
      throw err;
    } finally {
      setLoading(false);
    }
  };

  // Change password
  const changePassword = async (oldPassword, newPassword) => {
    try {
      setLoading(true);
      const res = await api.put('/api/auth/change-password', { oldPassword, newPassword });
      return res.data;
    } catch (err) {
      setError(err.response?.data?.message || 'Erreur de changement de mot de passe');
      throw err;
    } finally {
      setLoading(false);
    }
  };

  // Check if user has a specific role
  const hasRole = (role) => {
    return user && user.role === role;
  };

  // Check if user is authenticated
  const isAuthenticated = () => {
    return !!user;
  };

  const value = {
    user,
    token,
    loading,
    error,
    login,
    register,
    logout,
    updateProfile,
    changePassword,
    hasRole,
    isAuthenticated,
    setError
  };

  return <AuthContext.Provider value={value}>{children}</AuthContext.Provider>;
};

export default AuthContext;
