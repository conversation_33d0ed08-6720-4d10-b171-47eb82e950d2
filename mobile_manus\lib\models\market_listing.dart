class MarketListing {
  final int id;
  final String title;
  final String description;
  final String category;
  final double price;
  final String? location;
  final int views;
  final String? imageUrl;

  MarketListing({
    required this.id,
    required this.title,
    required this.description,
    required this.category,
    required this.price,
    this.location,
    required this.views,
    this.imageUrl,
  });

  factory MarketListing.fromJson(Map<String, dynamic> json) {
    return MarketListing(
      id: json['id'] ?? 0,
      title: json['title'] ?? '',
      description: json['description'] ?? '',
      category: json['category'] ?? '',
      price: (json['price'] ?? 0).toDouble(),
      location: json['location'],
      views: json['views'] ?? 0,
      imageUrl: json['imageUrl'],
    );
  }

  Map<String, dynamic> toJson() {
    return {
      'id': id,
      'title': title,
      'description': description,
      'category': category,
      'price': price,
      'location': location,
      'views': views,
      'imageUrl': imageUrl,
    };
  }
}

